// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Login command
Cypress.Commands.add('login', (username = 'admin', password = 'password') => {
  cy.visit('/login')
  cy.get('#username').type(username)
  cy.get('#password').type(password)
  cy.get('button[type="submit"]').click()
  cy.url().should('include', '/index')
})

// Logout command
Cypress.Commands.add('logout', () => {
  cy.get('a:contains("Uitloggen")').click()
  cy.url().should('include', '/login')
})

// Create travel record command
Cypress.Commands.add('createTravelRecord', (date, projectNumber, addressA, addressB, returnTrip = false) => {
  cy.visit('/travel_records/create')
  cy.get('#date').type(date)
  cy.get('#project_number').type(projectNumber)
  cy.get('#address_a').type(addressA)
  cy.get('#address_b').type(addressB)
  
  if (returnTrip) {
    cy.get('#return_trip').check()
  }
  
  cy.get('button[type="submit"]').click()
  cy.url().should('include', '/travel_records')
})

// Create declaration command
Cypress.Commands.add('createDeclaration', (title, totalAmount, amountExclVat, productService, emailedReceipt = false) => {
  cy.visit('/declarations/create')
  cy.get('#titel').type(title)
  cy.get('#totaal_bedrag').type(totalAmount)
  cy.get('#bedrag_excl_btw').type(amountExclVat)
  cy.get('#product_dienst').type(productService)
  
  if (emailedReceipt) {
    cy.get('#bon_factuur_gemaild').check()
  }
  
  cy.get('button[type="submit"]').click()
  cy.url().should('include', '/declarations')
})
