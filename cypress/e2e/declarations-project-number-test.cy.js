describe('Declarations Project Number Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })
  })

  it('should display project number field in declarations index page', () => {
    cy.visit('/declarations')
    
    // Check that page loads
    cy.get('h1').should('contain.text', 'Mijn Declaraties')
    
    // Check that project number filter field exists
    cy.get('input[name="project_number"]').should('be.visible')
    cy.get('label[for="project_number"]').should('contain.text', 'Projectnummer')
    
    // Check that table header includes project number
    cy.get('table thead th').should('contain.text', 'Projectnummer')
    
    cy.log('✅ Project number field is visible in declarations index')
  })

  it('should allow creating a declaration with project number', () => {
    cy.visit('/declarations/create')
    
    // Check that page loads
    cy.get('h1').should('contain.text', '<PERSON>euwe Declaratie')
    
    // Check that project number field exists and is required
    cy.get('input[name="project_number"]').should('be.visible')
    cy.get('input[name="project_number"]').should('have.attr', 'required')
    cy.get('label[for="project_number"]').should('contain.text', 'Projectnummer')
    
    // Fill in the form
    cy.get('input[name="titel"]').type('Test Declaratie met Projectnummer')
    cy.get('input[name="project_number"]').type('2024-TEST-001')
    cy.get('input[name="totaal_bedrag"]').type('100.50')
    cy.get('input[name="bedrag_excl_btw"]').type('83.06')
    cy.get('select[name="btw_percentage"]').select('21')
    cy.get('textarea[name="product_dienst"]').type('Test product voor projectnummer test')
    
    // Upload a test image
    cy.fixture('test-receipt.jpg', 'base64').then(fileContent => {
      cy.get('input[name="bon_factuur"]').selectFile({
        contents: Cypress.Buffer.from(fileContent, 'base64'),
        fileName: 'test-receipt.jpg',
        mimeType: 'image/jpeg'
      })
    })
    
    // Submit the form
    cy.get('button[type="submit"]').click()
    
    // Should redirect to declarations index
    cy.url().should('include', '/declarations')
    
    // Should show success message
    cy.get('body').should('contain.text', 'succesvol')
    
    cy.log('✅ Declaration with project number created successfully')
  })

  it('should display project number in declarations table', () => {
    cy.visit('/declarations')
    
    // Check if there are any declarations
    cy.get('table tbody tr').then($rows => {
      if ($rows.length > 1 || !$rows.first().find('td').text().includes('Geen declaraties gevonden')) {
        // There are declarations, check project number column
        cy.get('table tbody tr').first().within(() => {
          cy.get('td').should('have.length.at.least', 8) // Should have at least 8 columns including project number
        })
        
        // Check that project number column exists (3rd column after checkbox and title)
        cy.get('table tbody tr').first().find('td').eq(2).should('exist')
        
        cy.log('✅ Project number is displayed in declarations table')
      } else {
        cy.log('ℹ️ No declarations found to test project number display')
      }
    })
  })

  it('should allow filtering by project number', () => {
    cy.visit('/declarations')
    
    // Test project number filter
    cy.get('input[name="project_number"]').type('2024-TEST')
    cy.get('button:contains("Filter toepassen")').click()
    
    // Should stay on same page with filter applied
    cy.url().should('include', '/declarations')
    cy.url().should('include', 'project_number=2024-TEST')
    
    // Test filter reset
    cy.get('a:contains("Filter resetten")').click()
    
    // Should reset filters
    cy.url().should('not.include', 'project_number')
    cy.get('input[name="project_number"]').should('have.value', '')
    
    cy.log('✅ Project number filtering works correctly')
  })

  it('should show project number in edit form', () => {
    cy.visit('/declarations')
    
    // Check if there are any editable declarations
    cy.get('table tbody tr').then($rows => {
      if ($rows.length > 1 || !$rows.first().find('td').text().includes('Geen declaraties gevonden')) {
        // Look for an edit link
        cy.get('a:contains("Bewerken")').first().then($editLink => {
          if ($editLink.length > 0) {
            cy.wrap($editLink).click()
            
            // Should be on edit page
            cy.url().should('include', '/declarations/edit/')
            cy.get('h1').should('contain.text', 'Declaratie Bewerken')
            
            // Check that project number field exists and has value
            cy.get('input[name="project_number"]').should('be.visible')
            cy.get('input[name="project_number"]').should('have.attr', 'required')
            cy.get('label[for="project_number"]').should('contain.text', 'Projectnummer')
            
            // Project number field should have a value (either from DB or default 'Algemeen')
            cy.get('input[name="project_number"]').should('not.have.value', '')
            
            cy.log('✅ Project number field is present and populated in edit form')
          } else {
            cy.log('ℹ️ No editable declarations found to test edit form')
          }
        })
      } else {
        cy.log('ℹ️ No declarations found to test edit functionality')
      }
    })
  })

  it('should validate project number field is required', () => {
    cy.visit('/declarations/create')
    
    // Fill in all fields except project number
    cy.get('input[name="titel"]').type('Test Declaratie')
    // Leave project number empty
    cy.get('input[name="totaal_bedrag"]').type('100.00')
    cy.get('input[name="bedrag_excl_btw"]').type('82.64')
    cy.get('select[name="btw_percentage"]').select('21')
    cy.get('textarea[name="product_dienst"]').type('Test product')
    
    // Upload a test image
    cy.fixture('test-receipt.jpg', 'base64').then(fileContent => {
      cy.get('input[name="bon_factuur"]').selectFile({
        contents: Cypress.Buffer.from(fileContent, 'base64'),
        fileName: 'test-receipt.jpg',
        mimeType: 'image/jpeg'
      })
    })
    
    // Try to submit without project number
    cy.get('button[type="submit"]').click()
    
    // Should show validation error (browser validation)
    cy.get('input[name="project_number"]:invalid').should('exist')
    
    cy.log('✅ Project number field validation works correctly')
  })

  it('should maintain consistency with travel records project number format', () => {
    cy.visit('/declarations/create')
    
    // Check placeholder text matches travel records format
    cy.get('input[name="project_number"]').should('have.attr', 'placeholder', 'Bijv. 2024-001')
    
    // Compare with travel records page
    cy.visit('/travel_records/create')
    cy.get('input[name="project_number"]').should('have.attr', 'placeholder', 'Bijv. 2024-001')
    
    cy.log('✅ Project number format is consistent between declarations and travel records')
  })

  it('should display project number in admin view', () => {
    // Switch to admin user
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
    
    cy.visit('/admin/declarations')
    
    // Check if there are any users with declarations
    cy.get('body').then($body => {
      if ($body.find('a[href*="/admin/declarations/show/"]').length > 0) {
        // Click on first user's declarations
        cy.get('a[href*="/admin/declarations/show/"]').first().click()
        
        // Should be on admin declarations view
        cy.url().should('include', '/admin/declarations/show/')
        
        // Check that project number column exists in admin table
        cy.get('table thead th').should('contain.text', 'Projectnummer')
        
        cy.log('✅ Project number is displayed in admin declarations view')
      } else {
        cy.log('ℹ️ No user declarations found to test admin view')
      }
    })
  })
})
