describe('Asset Search Focus', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should automatically focus on search field when page loads', () => {
    cy.visit('/admin/assets/manage')
    
    // Controleer dat de pagina laadt
    cy.get('body').should('contain.text', 'Asset toewijzen/innemen')
    
    // Controleer dat het zoekveld bestaat
    cy.get('#uid').should('be.visible')
    
    // Controleer dat het zoekveld automatisch focus heeft
    cy.focused().should('have.id', 'uid')
    
    cy.log('Search field automatically receives focus on page load')
  })

  it('should allow typing immediately without clicking', () => {
    cy.visit('/admin/assets/manage')
    
    // Wacht tot de pagina volledig geladen is
    cy.get('#uid').should('be.visible')
    
    // Type direct zonder te klikken (omdat focus automatisch is)
    cy.type('TEST123')
    
    // Controleer dat de tekst in het veld staat
    cy.get('#uid').should('have.value', 'TEST123')
    
    cy.log('Can type immediately without clicking due to auto-focus')
  })

  it('should support Enter key to trigger search', () => {
    cy.visit('/admin/assets/manage')
    
    // Wacht tot de pagina volledig geladen is
    cy.get('#uid').should('be.visible')
    
    // Type een test UID
    cy.type('NONEXISTENT123')
    
    // Druk op Enter
    cy.get('#uid').type('{enter}')
    
    // Controleer dat de zoekfunctie wordt uitgevoerd
    // (Dit zou normaal een alert geven voor niet-bestaande assets)
    cy.wait(1000)
    
    cy.log('Enter key triggers search functionality')
  })

  it('should maintain focus after clearing the field', () => {
    cy.visit('/admin/assets/manage')
    
    // Type iets in het veld
    cy.type('TEST')
    cy.get('#uid').should('have.value', 'TEST')
    
    // Clear het veld
    cy.get('#uid').clear()
    
    // Controleer dat focus nog steeds op het veld staat
    cy.focused().should('have.id', 'uid')
    
    // Type opnieuw om te bevestigen dat focus behouden is
    cy.type('NEWTEST')
    cy.get('#uid').should('have.value', 'NEWTEST')
    
    cy.log('Focus is maintained after clearing the field')
  })

  it('should work with keyboard navigation', () => {
    cy.visit('/admin/assets/manage')
    
    // Controleer dat het zoekveld focus heeft
    cy.focused().should('have.id', 'uid')
    
    // Tab naar de zoekknop
    cy.focused().tab()
    cy.focused().should('have.id', 'searchAsset')
    
    // Tab terug naar het zoekveld
    cy.focused().tab({ shift: true })
    cy.focused().should('have.id', 'uid')
    
    cy.log('Keyboard navigation works correctly with auto-focus')
  })

  it('should focus on search field when navigating from other pages', () => {
    // Start op een andere pagina
    cy.visit('/admin/assets')
    cy.get('body').should('contain.text', 'Asset beheer')
    
    // Navigeer naar de manage pagina
    cy.get('a[href="/admin/assets/manage"]').click()
    
    // Controleer dat het zoekveld automatisch focus krijgt
    cy.focused().should('have.id', 'uid')
    
    cy.log('Search field gets focus when navigating from other pages')
  })

  it('should handle search workflow efficiently with auto-focus', () => {
    cy.visit('/admin/assets/manage')
    
    // Direct typen (geen klik nodig)
    cy.type('TEST123')
    
    // Enter om te zoeken
    cy.get('#uid').type('{enter}')
    
    cy.wait(1000)
    
    // Clear en nieuwe zoekopdracht
    cy.get('#uid').clear()
    cy.type('ANOTHER456')
    
    // Klik op zoekknop deze keer
    cy.get('#searchAsset').click()
    
    cy.wait(1000)
    
    cy.log('Efficient search workflow with auto-focus and Enter key support')
  })

  it('should maintain accessibility with auto-focus', () => {
    cy.visit('/admin/assets/manage')
    
    // Controleer dat het veld toegankelijk is
    cy.get('#uid').should('have.attr', 'placeholder', 'Voer UID in')
    cy.get('#uid').should('be.visible')
    cy.get('#uid').should('not.be.disabled')
    
    // Controleer dat focus correct is
    cy.focused().should('have.id', 'uid')
    
    // Controleer dat labels/placeholders correct zijn
    cy.get('body').should('contain.text', 'Asset zoeken op UID')
    
    cy.log('Auto-focus maintains accessibility standards')
  })

  it('should work correctly with browser back/forward navigation', () => {
    // Ga naar manage pagina
    cy.visit('/admin/assets/manage')
    cy.focused().should('have.id', 'uid')
    
    // Type iets
    cy.type('BACKTEST')
    
    // Ga naar een andere pagina
    cy.visit('/admin/assets')
    
    // Ga terug
    cy.go('back')
    
    // Controleer dat focus weer op het zoekveld staat
    cy.focused().should('have.id', 'uid')
    
    cy.log('Auto-focus works with browser navigation')
  })

  it('should handle multiple rapid page loads correctly', () => {
    // Test meerdere snelle page loads
    for (let i = 0; i < 3; i++) {
      cy.visit('/admin/assets/manage')
      cy.focused().should('have.id', 'uid')
      cy.type(`TEST${i}`)
      cy.get('#uid').should('have.value', `TEST${i}`)
      cy.get('#uid').clear()
    }
    
    cy.log('Auto-focus handles multiple rapid page loads correctly')
  })
})
