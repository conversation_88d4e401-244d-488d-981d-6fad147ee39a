describe('Asset Assignment Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should load asset management page without database errors', () => {
    cy.visit('/admin/assets/manage')
    
    // Controleer dat de pagina laadt
    cy.get('body').should('contain.text', 'Asset toewijzen/innemen')
    
    // Controleer dat er geen database errors zijn
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    cy.get('body').should('not.contain.text', '404')
    cy.get('body').should('not.contain.text', 'notes')
    
    // Controleer dat het zoekveld automatisch focus heeft
    cy.focused().should('have.id', 'uid')
    
    cy.log('Asset management page loads successfully without database errors')
  })

  it('should handle asset search without errors', () => {
    cy.visit('/admin/assets/manage')
    
    // Test asset zoeken
    cy.get('#uid').type('TESTCODE')
    cy.get('#searchAsset').click()
    
    cy.wait(2000)
    
    // Controleer dat er geen database errors zijn
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    cy.get('body').should('not.contain.text', 'notes')
    
    cy.log('Asset search works without database errors')
  })

  it('should verify database structure is correct', () => {
    cy.visit('/admin/assets/manage')
    
    // Controleer dat de pagina correct laadt zonder structuur errors
    cy.get('body').then(($body) => {
      const text = $body.text()
      
      // Controleer dat er geen database structure errors zijn
      expect(text).to.not.include('Unknown column \'notes\'')
      expect(text).to.not.include('Unknown column \'action\'')
      expect(text).to.not.include('SQLSTATE[42S22]')
      expect(text).to.not.include('Duplicate column name')
      
      cy.log('Database structure verification successful')
    })
  })

  it('should have working auto-focus on search field', () => {
    cy.visit('/admin/assets/manage')
    
    // Controleer dat het zoekveld automatisch focus heeft
    cy.focused().should('have.id', 'uid')
    
    // Test dat je direct kunt typen
    cy.type('TEST123')
    cy.get('#uid').should('have.value', 'TEST123')
    
    // Test Enter key functionaliteit
    cy.get('#uid').clear()
    cy.type('ENTER_TEST{enter}')
    
    cy.wait(1000)
    
    // Controleer dat er geen errors zijn na Enter
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    
    cy.log('Auto-focus and Enter key functionality works correctly')
  })

  it('should display asset table without errors', () => {
    cy.visit('/admin/assets/manage')
    
    // Controleer dat de asset tabel wordt getoond
    cy.get('table').should('be.visible')
    cy.get('body').should('contain.text', 'Alle assets')
    
    // Controleer dat er geen database errors zijn in de tabel
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    
    cy.log('Asset table displays correctly without database errors')
  })
})
