describe('Admin Dashboard Button Alignment', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should have vertically aligned buttons using flexbox layout', () => {
    cy.visit('/admin')
    
    // Controleer dat admin dashboard laadt
    cy.get('body').should('contain.text', 'Admin Dashboard')
    
    // Controleer dat alle cards flexbox layout hebben
    const cardSelectors = [
      '.bg-blue-50',    // Gebruikers
      '.bg-indigo-50',  // Reiskosten
      '.bg-green-50',   // Declaraties
      '.bg-yellow-50',  // Assets
      '.bg-purple-50'   // Logs
    ]
    
    cardSelectors.forEach(selector => {
      cy.get(selector).should('have.class', 'flex')
      cy.get(selector).should('have.class', 'flex-col')
      cy.get(selector).should('have.class', 'h-full')
      
      cy.log(`Card ${selector} has proper flexbox layout`)
    })
  })

  it('should have buttons positioned at bottom of cards with mt-auto', () => {
    cy.visit('/admin')
    
    // Alle admin dashboard knoppen
    const buttons = [
      { href: '/admin/users', text: 'Beheer gebruikers' },
      { href: '/admin/travel_records', text: 'Beheer reiskosten' },
      { href: '/admin/declarations', text: 'Beheer declaraties' },
      { href: '/admin/assets', text: 'Beheer assets' },
      { href: '/logs', text: 'Bekijk logs' }
    ]
    
    buttons.forEach(button => {
      cy.get(`a[href="${button.href}"]`).should('be.visible')
      cy.get(`a[href="${button.href}"]`).should('have.class', 'mt-auto')
      
      cy.log(`Button "${button.text}" has mt-auto class for bottom alignment`)
    })
  })

  it('should have content areas with flex-grow for proper spacing', () => {
    cy.visit('/admin')
    
    // Controleer dat content areas flex-grow hebben
    cy.get('.bg-blue-50 .flex-grow').should('exist')
    cy.get('.bg-indigo-50 .flex-grow').should('exist')
    cy.get('.bg-green-50 .flex-grow').should('exist')
    cy.get('.bg-yellow-50 .flex-grow').should('exist')
    cy.get('.bg-purple-50 .flex-grow').should('exist')
    
    cy.log('All content areas have flex-grow for proper spacing')
  })

  it('should maintain equal card heights in grid layout', () => {
    cy.visit('/admin')
    
    // Controleer dat de grid layout correct is
    cy.get('.grid').should('exist')
    cy.get('.grid-cols-1').should('exist')
    cy.get('.md\\:grid-cols-5').should('exist')
    
    // Controleer dat alle cards h-full hebben voor gelijke hoogte
    const cardCount = 5
    cy.get('.grid .h-full').should('have.length', cardCount)
    
    cy.log('Grid layout maintains equal card heights')
  })

  it('should have visually aligned buttons when viewed', () => {
    cy.visit('/admin')
    
    // Test visuele uitlijning door button posities te vergelijken
    const buttonHrefs = [
      '/admin/users',
      '/admin/travel_records', 
      '/admin/declarations',
      '/admin/assets',
      '/logs'
    ]
    
    // Verzamel button posities
    const buttonPositions = []
    
    buttonHrefs.forEach((href, index) => {
      cy.get(`a[href="${href}"]`).then($button => {
        const rect = $button[0].getBoundingClientRect()
        buttonPositions.push({
          href,
          bottom: rect.bottom,
          top: rect.top
        })
        
        cy.log(`Button ${href} position: top=${rect.top}, bottom=${rect.bottom}`)
        
        // Als dit de laatste button is, vergelijk posities
        if (index === buttonHrefs.length - 1) {
          // Controleer dat buttons ongeveer op dezelfde hoogte staan
          // (binnen een redelijke marge voor verschillende content hoogtes)
          const bottomPositions = buttonPositions.map(pos => pos.bottom)
          const maxBottom = Math.max(...bottomPositions)
          const minBottom = Math.min(...bottomPositions)
          const difference = maxBottom - minBottom
          
          // Buttons zouden binnen 50px van elkaar moeten staan
          if (difference <= 50) {
            cy.log('Buttons are well aligned (difference: ' + difference + 'px)')
          } else {
            cy.log('Buttons may have alignment issues (difference: ' + difference + 'px)')
          }
        }
      })
    })
  })

  it('should handle different content lengths gracefully', () => {
    cy.visit('/admin')
    
    // Controleer dat cards met verschillende content lengtes goed werken
    
    // Assets card heeft de meeste content
    cy.get('.bg-yellow-50').should('contain.text', 'Totaal aantal assets')
    cy.get('.bg-yellow-50').should('contain.text', 'Beschikbaar')
    cy.get('.bg-yellow-50').should('contain.text', 'Uitgegeven')
    
    // Logs card heeft de minste content
    cy.get('.bg-purple-50').should('contain.text', 'Bekijk systeemlogboeken')
    
    // Beide zouden dezelfde button uitlijning moeten hebben
    cy.get('.bg-yellow-50 a').should('have.class', 'mt-auto')
    cy.get('.bg-purple-50 a').should('have.class', 'mt-auto')
    
    cy.log('Different content lengths handled gracefully with flexbox')
  })

  it('should maintain responsive layout on different screen sizes', () => {
    // Test desktop layout
    cy.viewport(1200, 800)
    cy.visit('/admin')
    
    cy.get('.md\\:grid-cols-5').should('exist')
    cy.log('Desktop layout: 5 columns')
    
    // Test tablet layout
    cy.viewport(768, 1024)
    cy.visit('/admin')
    
    cy.get('.grid-cols-1').should('exist')
    cy.log('Tablet layout: 1 column (stacked)')
    
    // Test mobile layout
    cy.viewport(375, 667)
    cy.visit('/admin')
    
    cy.get('.grid-cols-1').should('exist')
    cy.log('Mobile layout: 1 column (stacked)')
    
    // Controleer dat buttons nog steeds correct uitgelijnd zijn op mobile
    cy.get('a[href="/admin/users"]').should('have.class', 'mt-auto')
    cy.get('a[href="/admin/assets"]').should('have.class', 'mt-auto')
  })

  it('should have consistent spacing and margins', () => {
    cy.visit('/admin')
    
    // Controleer dat alle cards consistent spacing hebben
    const expectedClasses = [
      'rounded-lg',
      'p-6',
      'shadow-sm',
      'flex',
      'flex-col',
      'h-full'
    ]
    
    const cardSelectors = [
      '.bg-blue-50',
      '.bg-indigo-50',
      '.bg-green-50',
      '.bg-yellow-50',
      '.bg-purple-50'
    ]
    
    cardSelectors.forEach(selector => {
      expectedClasses.forEach(className => {
        cy.get(selector).should('have.class', className)
      })
      
      cy.log(`Card ${selector} has consistent spacing classes`)
    })
  })

  it('should work correctly with dynamic content', () => {
    cy.visit('/admin')
    
    // Test dat de layout werkt zelfs als content dynamisch verandert
    // (bijvoorbeeld als er geen recent assigned assets zijn)
    
    cy.get('.bg-yellow-50').then($card => {
      const hasRecentAssets = $card.text().includes('Recent uitgegeven assets')
      
      if (hasRecentAssets) {
        cy.log('Assets card shows recent assignments - layout should handle this')
      } else {
        cy.log('Assets card shows no recent assignments - layout should handle this')
      }
      
      // In beide gevallen zou de button onderaan moeten staan
      cy.get('.bg-yellow-50 a').should('have.class', 'mt-auto')
    })
  })
})
