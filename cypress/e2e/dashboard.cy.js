describe('Dashboard', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
    cy.visit('/')
  })

  it('should display dashboard with statistics', () => {
    // Controleer of het dashboard wordt getoond
    cy.get('body').then(($body) => {
      const hasDashboardTitle = $body.text().match(/dashboard/i);
      
      if (hasDashboardTitle) {
        cy.log('Dashboard titel gevonden');
      } else {
        cy.url().should('not.include', '/login');
      }
    })
  })

  it('should show travel records statistics', () => {
    // Controleer of reiskosten statistieken worden getoond
    cy.get('body').then(($body) => {
      const hasTravelStats = $body.text().match(/reiskosten|kilometers|travel/i);
      
      if (hasTravelStats) {
        cy.log('Reiskosten statistieken gevonden');
        
        // Controleer of uitbetaalde kilometers worden getoond
        const hasPaidKm = $body.text().match(/uitbetaalde kilometers|paid.*km/i);
        
        if (hasPaidKm) {
          cy.log('Uitbetaalde kilometers statistiek gevonden');
        }
      }
    })
  })

  it('should show declarations statistics', () => {
    // Controleer of declaraties statistieken worden getoond
    cy.get('body').then(($body) => {
      const hasDeclarationStats = $body.text().match(/declaraties|declarations/i);
      
      if (hasDeclarationStats) {
        cy.log('Declaraties statistieken gevonden');
        
        // Controleer of uitbetaalde bedragen worden getoond
        const hasPaidAmount = $body.text().match(/uitbetaalde.*bedrag|paid.*amount/i);
        
        if (hasPaidAmount) {
          cy.log('Uitbetaalde bedragen statistiek gevonden');
        }
      }
    })
  })

  it('should navigate to travel records from dashboard', () => {
    // Klik op de reiskosten link vanuit het dashboard
    cy.get('body').then(($body) => {
      if ($body.find('a:contains("Bekijk reiskosten"), a:contains("bekijk reiskosten"), a[href*="travel_records"]').length > 0) {
        cy.get('a:contains("Bekijk reiskosten"), a:contains("bekijk reiskosten"), a[href*="travel_records"]').first().click();
        
        cy.url().should('include', '/travel_records');
        cy.log('Succesvol genavigeerd naar reiskosten vanuit dashboard');
      } else {
        cy.log('Geen reiskosten link gevonden op dashboard');
      }
    })
  })

  it('should navigate to declarations from dashboard', () => {
    // Klik op de declaraties link vanuit het dashboard
    cy.get('body').then(($body) => {
      if ($body.find('a:contains("Bekijk declaraties"), a:contains("bekijk declaraties"), a[href*="declarations"]').length > 0) {
        cy.get('a:contains("Bekijk declaraties"), a:contains("bekijk declaraties"), a[href*="declarations"]').first().click();
        
        cy.url().should('include', '/declarations');
        cy.log('Succesvol genavigeerd naar declaraties vanuit dashboard');
      } else {
        cy.log('Geen declaraties link gevonden op dashboard');
      }
    })
  })

  it('should show admin functions for admin users', () => {
    // Controleer of admin functies worden getoond voor admin gebruikers
    cy.get('body').then(($body) => {
      const hasAdminSection = $body.text().match(/admin.*functies|admin.*functions/i);
      
      if (hasAdminSection) {
        cy.log('Admin functies sectie gevonden');
        
        // Controleer of er admin links zijn
        const hasAdminLinks = $body.find('a[href*="admin"]').length > 0;
        
        if (hasAdminLinks) {
          cy.log('Admin links gevonden');
        }
      } else {
        cy.log('Geen admin functies sectie gevonden (mogelijk geen admin gebruiker)');
      }
    })
  })

  it('should navigate to admin dashboard', () => {
    // Test navigatie naar admin dashboard
    cy.get('body').then(($body) => {
      if ($body.find('a:contains("Admin Dashboard"), a:contains("admin dashboard"), a[href="/admin"]').length > 0) {
        cy.get('a:contains("Admin Dashboard"), a:contains("admin dashboard"), a[href="/admin"]').first().click();
        
        cy.url().should('include', '/admin');
        cy.log('Succesvol genavigeerd naar admin dashboard');
      } else {
        cy.log('Geen admin dashboard link gevonden');
      }
    })
  })

  it('should display correct number formatting', () => {
    // Controleer of getallen correct worden geformatteerd (Nederlandse notatie)
    cy.get('body').then(($body) => {
      // Zoek naar bedragen met komma's (Nederlandse notatie)
      const hasCorrectFormatting = $body.text().match(/\d+,\d+|\d+\.\d{3}/);
      
      if (hasCorrectFormatting) {
        cy.log('Correcte Nederlandse getalnotatie gevonden');
      } else {
        cy.log('Geen specifieke getalnotatie gedetecteerd');
      }
    })
  })
})
