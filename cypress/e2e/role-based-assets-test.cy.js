describe('Role-based Assets & Notifications Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
  })

  it('should show simplified asset view for regular users', () => {
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.visit('/assets')
    
    // Check that user sees simplified view
    cy.get('h1').should('contain.text', 'Asset overzicht')
    
    // Check that only essential columns are shown for users
    cy.get('table thead tr th').should('contain.text', 'Naam')
    cy.get('table thead tr th').should('contain.text', 'Merk')
    cy.get('table thead tr th').should('contain.text', 'Model')
    cy.get('table thead tr th').should('contain.text', 'Type')
    cy.get('table thead tr th').should('contain.text', 'Huidige/La<PERSON>te houder')
    cy.get('table thead tr th').should('contain.text', 'Acties')
    
    // Should NOT show admin-only columns
    cy.get('table thead tr th').should('not.contain.text', 'UID')
    cy.get('table thead tr th').should('not.contain.text', 'Status')
    cy.get('table thead tr th').should('not.contain.text', 'Zichtbaarheid')
    
    // Check for "Toewijzen aan mij" buttons for available assets
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        cy.get('table tbody tr').each(($row) => {
          const holderCell = $row.find('td').eq(4) // Huidige/Laatste houder column
          const actionCell = $row.find('td').eq(5) // Acties column
          
          if (holderCell.text().includes('Beschikbaar')) {
            // Should have "Toewijzen aan mij" button
            cy.wrap(actionCell).should('contain.text', 'Toewijzen aan mij')
          } else {
            // Should show "Niet beschikbaar"
            cy.wrap(actionCell).should('contain.text', 'Niet beschikbaar')
          }
        })
      }
    })
    
    cy.log('✅ Regular user sees simplified asset view')
  })

  it('should show full asset view for administrators', () => {
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    cy.visit('/assets')
    
    // Check that admin sees full view
    cy.get('h1').should('contain.text', 'Asset overzicht')
    
    // Check that all columns are shown for admins
    cy.get('table thead tr th').should('contain.text', 'UID')
    cy.get('table thead tr th').should('contain.text', 'Naam')
    cy.get('table thead tr th').should('contain.text', 'Merk')
    cy.get('table thead tr th').should('contain.text', 'Model')
    cy.get('table thead tr th').should('contain.text', 'Type')
    cy.get('table thead tr th').should('contain.text', 'Status')
    cy.get('table thead tr th').should('contain.text', 'Huidige houder')
    cy.get('table thead tr th').should('contain.text', 'Acties')
    
    // Should show admin actions
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        cy.get('table tbody tr').first().within(() => {
          cy.get('td').last().should('contain.text', 'Details')
        })
      }
    })
    
    cy.log('✅ Administrator sees full asset view')
  })

  it('should show admin notification badge in header', () => {
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    cy.visit('/')
    
    // Check that admin sees Alerts link
    cy.get('header').should('contain.text', 'Alerts')
    
    // Check that notification badge exists (may be hidden if no notifications)
    cy.get('#notification-badge').should('exist')
    
    // Check that notifications link works
    cy.get('a[href="/notifications"]').should('be.visible')
    
    cy.log('✅ Admin notification system is visible')
  })

  it('should not show notification badge for regular users', () => {
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.visit('/')
    
    // Regular users should not see Alerts link
    cy.get('header').should('not.contain.text', 'Alerts')
    
    // Should not have notification badge
    cy.get('#notification-badge').should('not.exist')
    
    cy.log('✅ Regular users do not see notification system')
  })

  it('should test "assign to me" functionality', () => {
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.visit('/assets')
    
    // Look for available assets
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        // Find a row with "Toewijzen aan mij" button
        cy.get('table tbody tr').each(($row) => {
          const actionCell = $row.find('td').eq(5)
          
          if (actionCell.find('button:contains("Toewijzen aan mij")').length > 0) {
            // Click the assign button
            cy.wrap(actionCell).find('button:contains("Toewijzen aan mij")').click()
            
            // Should redirect back to assets page
            cy.url().should('include', '/assets')
            
            // Should show success message
            cy.get('body').should('contain.text', 'succesvol')
            
            return false // Break out of each loop
          }
        })
      } else {
        cy.log('No assets available to test assignment')
      }
    })
    
    cy.log('✅ Asset assignment functionality works')
  })

  it('should verify notification creation for asset assignment', () => {
    // First assign an asset as regular user
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.visit('/assets')
    
    // Try to assign an asset
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        cy.get('button:contains("Toewijzen aan mij")').first().then(($btn) => {
          if ($btn.length > 0) {
            cy.wrap($btn).click()
            
            // Now login as admin to check notifications
            cy.get('@users').then(users => {
              cy.login(users.admin.username, users.admin.password)
            })
            
            cy.visit('/notifications')
            
            // Should see notification about asset assignment
            cy.get('body').should('contain.text', 'Asset')
            cy.get('body').should('contain.text', 'toegewezen')
            
            cy.log('✅ Asset assignment creates admin notification')
          } else {
            cy.log('No available assets to assign')
          }
        })
      }
    })
  })

  it('should test notification system functionality', () => {
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    cy.visit('/notifications')
    
    // Check notifications page loads
    cy.get('h1').should('contain.text', 'Admin Notificaties')
    
    // Check for notification management buttons
    cy.get('body').should('contain.text', 'Oude notificaties opruimen')
    
    // If there are unread notifications, should see mark all as read button
    cy.get('body').then(($body) => {
      if ($body.text().includes('ongelezen')) {
        cy.get('button:contains("Alle markeren als gelezen")').should('be.visible')
      }
    })
    
    cy.log('✅ Notification management system works')
  })

  it('should verify role-based access control', () => {
    // Test that regular users cannot access admin notifications
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.visit('/notifications')
    
    // Should be redirected away from notifications (not authorized)
    cy.url().should('not.include', '/notifications')
    
    // Test that admins can access notifications
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    cy.visit('/notifications')
    
    // Should be able to access notifications
    cy.url().should('include', '/notifications')
    cy.get('h1').should('contain.text', 'Admin Notificaties')
    
    cy.log('✅ Role-based access control works correctly')
  })

  it('should verify asset filtering works correctly', () => {
    // Test as regular user
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.visit('/assets')
    
    // Should see assets (filtered for user role)
    cy.get('table').should('be.visible')
    
    // Test as admin
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    cy.visit('/assets')
    
    // Should see all assets (no filtering for admin)
    cy.get('table').should('be.visible')
    
    // Admin should see more columns
    cy.get('table thead tr th').should('have.length.greaterThan', 6)
    
    cy.log('✅ Asset filtering works based on user role')
  })
})
