describe('Admin Dashboard Asset Names Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should display correct asset holder names instead of "Onbekend"', () => {
    cy.visit('/admin')
    
    // Check if there are recent asset assignments
    cy.get('body').then(($body) => {
      const bodyText = $body.text()
      
      if (bodyText.includes('Recent uitgegeven assets:')) {
        cy.log('Recent asset assignments section found')
        
        // Check that asset assignments don't show "Onbekend" for valid users
        cy.get('body').then(($body2) => {
          const recentAssignmentsText = $body2.text()
          
          // Look for asset assignment patterns
          const hasAssetAssignments = recentAssignmentsText.match(/→\s*\w+\s*\(/g)
          
          if (hasAssetAssignments) {
            cy.log('Asset assignments found, checking for proper user names')
            
            // Should not show "Onbekend" for assigned assets
            cy.get('.bg-yellow-100').each(($assignment) => {
              const assignmentText = $assignment.text()
              cy.log('Assignment text: ' + assignmentText)
              
              // If there's an assignment, it should not show "Onbekend"
              if (assignmentText.includes('→')) {
                expect(assignmentText).to.not.include('→ Onbekend')
                expect(assignmentText).to.not.include('→ null')
                expect(assignmentText).to.not.include('→ undefined')
              }
            })
          } else {
            cy.log('No asset assignments found to test')
          }
        })
      } else {
        cy.log('No recent asset assignments section found')
      }
    })
  })

  it('should verify asset data includes holder names', () => {
    cy.visit('/admin/assets')
    
    // Check the assets page to see if holder names are properly displayed
    cy.get('body').should('contain.text', 'Assets beheren')
    
    // Check if there are any assigned assets
    cy.get('body').then(($body) => {
      if ($body.find('table tbody tr').length > 0) {
        cy.log('Assets found in table')
        
        // Check that assigned assets show proper user names
        cy.get('table tbody tr').each(($row) => {
          const rowText = $row.text()
          
          // If the asset is assigned (has a user), it should not show empty or "Onbekend"
          if (rowText.includes('Uitgeleend') || rowText.includes('uitgeleend')) {
            // The row should contain a username, not be empty
            expect(rowText).to.not.include('Onbekend')
            expect(rowText).to.not.include('null')
            expect(rowText).to.not.include('undefined')
            cy.log('Asset assignment row looks good: ' + rowText.substring(0, 50) + '...')
          }
        })
      } else {
        cy.log('No assets found in table')
      }
    })
  })

  it('should test asset assignment functionality', () => {
    cy.visit('/admin/assets/manage')
    
    // Check that the asset management page loads
    cy.get('body').should('contain.text', 'Asset toewijzen/innemen')
    
    // Test searching for an asset
    cy.get('#uid').should('be.visible')
    cy.get('#uid').type('HDH16234')
    cy.get('#searchAsset').click()
    
    cy.wait(2000)
    
    // Check that no database errors are shown
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    
    // If asset is found, check that user information is properly displayed
    cy.get('body').then(($body) => {
      if ($body.text().includes('Asset gevonden')) {
        cy.log('Asset found, checking user display')
        
        // Should not show "Onbekend" in asset details
        cy.get('body').should('not.contain.text', 'Huidige houder: Onbekend')
        cy.get('body').should('not.contain.text', 'Huidige houder: null')
        cy.get('body').should('not.contain.text', 'Huidige houder: undefined')
      } else {
        cy.log('Asset not found or not assigned')
      }
    })
  })

  it('should verify database query returns proper user names', () => {
    cy.visit('/admin')
    
    // Check that the dashboard loads without database errors
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    cy.get('body').should('not.contain.text', 'Table \'')
    
    // Check that asset statistics are displayed
    cy.get('body').should('contain.text', 'Totaal aantal assets:')
    cy.get('body').should('contain.text', 'Beschikbaar:')
    cy.get('body').should('contain.text', 'Uitgegeven:')
    
    cy.log('✅ Dashboard loads without database errors')
  })

  it('should check for proper JOIN queries in asset data', () => {
    cy.visit('/admin')
    
    // Verify that the page loads successfully (indicating proper database queries)
    cy.get('h1').should('contain.text', 'Admin Dashboard')
    
    // Check that asset section exists
    cy.get('body').should('contain.text', 'Assets')
    
    // If there are recent assignments, they should have proper formatting
    cy.get('body').then(($body) => {
      const bodyText = $body.text()
      
      if (bodyText.includes('Recent uitgegeven assets:')) {
        // Look for the asset assignment format: "ASSET_ID → USERNAME (DATE)"
        const assignmentPattern = /\w+\s*→\s*\w+\s*\(\d{2}-\d{2}\)/g
        const assignments = bodyText.match(assignmentPattern)
        
        if (assignments) {
          cy.log('Found asset assignments with proper format:')
          assignments.forEach(assignment => {
            cy.log('- ' + assignment)
            // Each assignment should not contain "Onbekend"
            expect(assignment).to.not.include('Onbekend')
          })
        }
      }
    })
    
    cy.log('✅ Asset holder names are properly formatted')
  })
})
