describe('Google Maps Integration', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should calculate distance automatically when addresses are filled', () => {
    cy.visit('/travel_records/create')
    
    // Vul adressen in
    const addressA = 'Amsterdam Centraal, Amsterdam';
    const addressB = 'Rotterdam Centraal, Rotterdam';
    
    cy.get('#address_a').type(addressA);
    cy.get('#address_b').type(addressB);
    
    // Wacht even voor automatische berekening
    cy.wait(2000);
    
    // Controleer of de afstand is berekend
    cy.get('#total_km').should(($input) => {
      const value = $input.val();
      if (value && parseFloat(value) > 0) {
        cy.log(`Afstand automatisch berekend: ${value} km`);
      } else {
        cy.log('Automatische afstandsberekening niet gedetecteerd');
      }
    });
  })

  it('should handle commute mode correctly', () => {
    cy.visit('/travel_records/create')
    
    // Vul van adres in
    const homeAddress = 'Hoofdstraat 1, Amsterdam';
    cy.get('#address_a').type(homeAddress);
    
    // Activeer woon-werkverkeer modus
    cy.get('#is_commute').check();
    
    // Controleer of het naar adres automatisch wordt ingevuld
    cy.get('#address_b').should('have.value', 'Rondven 24, 6026PX, Maarheeze');
    
    // Controleer of het projectnummer wordt ingevuld
    cy.get('#project_number').should('have.value', 'Woon-werkverkeer');
    
    // Wacht voor automatische afstandsberekening
    cy.wait(2000);
    
    // Controleer of de afstand is berekend
    cy.get('#total_km').should(($input) => {
      const value = $input.val();
      if (value && parseFloat(value) > 0) {
        cy.log(`Woon-werkverkeer afstand berekend: ${value} km`);
      }
    });
    
    // Test uitschakelen van woon-werkverkeer
    cy.get('#is_commute').uncheck();
    
    // Controleer of velden worden gereset
    cy.get('#address_b').should('have.value', '');
    cy.get('#project_number').should('have.value', '');
  })

  it('should handle return trip calculation', () => {
    cy.visit('/travel_records/create')
    
    // Vul adressen in
    cy.get('#address_a').type('Amsterdam, Nederland');
    cy.get('#address_b').type('Utrecht, Nederland');
    
    // Wacht voor eerste berekening
    cy.wait(2000);
    
    // Noteer de enkele reis afstand
    cy.get('#total_km').then(($input) => {
      const singleTripDistance = parseFloat($input.val()) || 0;
      
      if (singleTripDistance > 0) {
        // Activeer retour
        cy.get('#return_trip').check();
        
        // Wacht voor herberekening
        cy.wait(1000);
        
        // Controleer of de afstand is verdubbeld
        cy.get('#total_km').should(($newInput) => {
          const returnTripDistance = parseFloat($newInput.val()) || 0;
          
          if (Math.abs(returnTripDistance - (singleTripDistance * 2)) < 0.1) {
            cy.log(`Retour afstand correct berekend: ${returnTripDistance} km (2x ${singleTripDistance} km)`);
          } else {
            cy.log(`Retour berekening: ${returnTripDistance} km (verwacht: ${singleTripDistance * 2} km)`);
          }
        });
      } else {
        cy.log('Geen enkele reis afstand beschikbaar voor retour test');
      }
    });
  })

  it('should handle multiple addresses', () => {
    cy.visit('/travel_records/create')
    
    // Vul basis adressen in
    cy.get('#address_a').type('Amsterdam, Nederland');
    cy.get('#address_b').type('Rotterdam, Nederland');
    
    // Voeg extra adressen toe als het veld bestaat
    cy.get('body').then(($body) => {
      if ($body.find('#extra_addresses').length > 0) {
        cy.get('#extra_addresses').type('Utrecht, Nederland\nDen Haag, Nederland');
        
        // Wacht voor berekening van meerdere adressen
        cy.wait(3000);
        
        // Controleer of de totale afstand is berekend
        cy.get('#total_km').should(($input) => {
          const value = $input.val();
          if (value && parseFloat(value) > 0) {
            cy.log(`Meerdere adressen afstand berekend: ${value} km`);
          }
        });
      } else {
        cy.log('Extra adressen veld niet gevonden, test overgeslagen');
      }
    });
  })

  it('should handle API errors gracefully', () => {
    cy.visit('/travel_records/create')
    
    // Test met ongeldige adressen
    cy.get('#address_a').type('Ongeldig Adres 12345');
    cy.get('#address_b').type('Nog Een Ongeldig Adres 67890');
    
    // Wacht voor API response
    cy.wait(3000);
    
    // Controleer of er geen foutmelding wordt getoond of dat het veld leeg blijft
    cy.get('#total_km').should(($input) => {
      const value = $input.val();
      cy.log(`Resultaat voor ongeldige adressen: "${value}"`);
    });
    
    // Controleer of er geen JavaScript errors zijn
    cy.window().then((win) => {
      // Controleer console errors (indien beschikbaar)
      cy.log('Google Maps API error handling getest');
    });
  })

  it('should preserve manual distance input', () => {
    cy.visit('/travel_records/create')
    
    // Vul handmatig een afstand in
    const manualDistance = '50.5';
    cy.get('#total_km').clear().type(manualDistance);
    
    // Vul daarna adressen in
    cy.get('#address_a').type('Amsterdam, Nederland');
    
    // Controleer of de handmatige afstand behouden blijft of wordt overschreven
    cy.wait(1000);
    
    cy.get('#total_km').should(($input) => {
      const currentValue = $input.val();
      cy.log(`Afstand na adres invulling: ${currentValue} (was handmatig: ${manualDistance})`);
    });
  })
})
