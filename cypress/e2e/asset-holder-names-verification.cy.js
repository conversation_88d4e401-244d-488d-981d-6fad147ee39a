describe('Asset Holder Names Verification', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should show real usernames instead of "Onbekend" in admin dashboard', () => {
    cy.visit('/admin')
    
    // Check that the dashboard loads
    cy.get('h1').should('contain.text', 'Admin Dashboard')
    
    // Check for recent asset assignments
    cy.get('body').then(($body) => {
      const bodyText = $body.text()
      
      if (bodyText.includes('Recent uitgegeven assets:')) {
        cy.log('✅ Recent asset assignments section found')
        
        // Should not show "Onbekend" anymore
        cy.get('body').should('not.contain.text', '→ Onbekend')
        
        // Look for actual usernames in assignments
        const hasRealUsernames = bodyText.includes('→ pelle') || 
                                 bodyText.includes('→ esmee') || 
                                 bodyText.includes('→ jordi') ||
                                 bodyText.match(/→\s*[a-zA-Z]+\s*\(/g)
        
        if (hasRealUsernames) {
          cy.log('✅ Real usernames found in asset assignments')
        } else {
          cy.log('ℹ️ No recent assignments with usernames found')
        }
      } else {
        cy.log('ℹ️ No recent asset assignments to display')
      }
    })
    
    cy.log('✅ Asset holder names verification completed')
  })

  it('should verify asset management page shows correct holder names', () => {
    cy.visit('/admin/assets')
    
    // Check that assets page loads
    cy.get('body').should('contain.text', 'Asset beheer')
    
    // Check that assigned assets show real usernames
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        cy.log(`Found ${$rows.length} assets in table`)
        
        // Look for assigned assets and verify they don't show "Onbekend"
        cy.get('table tbody tr').each(($row) => {
          const rowText = $row.text()
          
          // If asset is assigned (has a status like "uitgeleend" or "In gebruik")
          if (rowText.includes('uitgeleend') || rowText.includes('In gebruik')) {
            // Should not show "Onbekend" as holder
            expect(rowText).to.not.include('Onbekend')
            
            // Should show a real username
            const hasUsername = rowText.includes('pelle') || 
                               rowText.includes('esmee') || 
                               rowText.includes('jordi') ||
                               rowText.match(/[a-zA-Z]+\s+sinds/g)
            
            if (hasUsername) {
              cy.log('✅ Asset shows real username: ' + rowText.substring(0, 50) + '...')
            }
          }
        })
      }
    })
  })

  it('should confirm the fix is working correctly', () => {
    cy.visit('/admin')
    
    // Verify no database errors
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    
    // Verify asset statistics are shown
    cy.get('body').should('contain.text', 'Totaal aantal assets:')
    cy.get('body').should('contain.text', 'Beschikbaar:')
    cy.get('body').should('contain.text', 'Uitgegeven:')
    
    // Most importantly: should not show "Onbekend" in recent assignments
    cy.get('body').should('not.contain.text', '→ Onbekend')
    
    cy.log('✅ Asset holder names fix is working correctly')
  })
})
