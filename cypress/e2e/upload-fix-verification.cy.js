describe('Upload Fix Verification', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should access declaration uploads without 404 errors', () => {
    // Test direct upload access
    cy.request({
      url: '/uploads/declaraties/declaratie_15_686bdc48a6d82.png',
      failOnStatusCode: false
    }).then((response) => {
      // Should not be 404 anymore
      expect(response.status).to.not.equal(404)
      // Should be 200 (success) or 403 (forbidden due to security)
      expect([200, 403]).to.include(response.status)
      cy.log(`Upload access test: ${response.status}`)
    })
  })

  it('should have working upload links in admin declarations', () => {
    cy.visit('/admin/declarations')
    
    cy.get('body').then(($body) => {
      if ($body.find('a[href*="/admin/declarations/user/"]').length > 0) {
        cy.get('a[href*="/admin/declarations/user/"]').first().click()
        
        // Check for upload links
        cy.get('body').then(($userBody) => {
          if ($userBody.find('a:contains("Bekijk bon/factuur")').length > 0) {
            cy.get('a:contains("Bekijk bon/factuur")').first().should('have.attr', 'href').and('match', /^\/uploads\/declaraties\//)
            cy.log('✅ Upload links have correct format')
          } else {
            cy.log('ℹ️ No upload links found (no uploaded files)')
          }
        })
      } else {
        cy.log('ℹ️ No users with declarations found')
      }
    })
  })

  it('should verify UploadController is loaded', () => {
    // Test that the route doesn't give a class not found error
    cy.request({
      url: '/uploads/declaraties/nonexistent.jpg',
      failOnStatusCode: false
    }).then((response) => {
      // Should not be a server error (500) or class not found
      expect(response.status).to.not.equal(500)
      expect(response.body).to.not.include('Class \'UploadController\' not found')
      expect(response.body).to.not.include('Route not found')
      cy.log('✅ UploadController is properly loaded')
    })
  })

  it('should test security features', () => {
    // Test directory traversal protection
    cy.request({
      url: '/uploads/declaraties/../../../bootstrap.php',
      failOnStatusCode: false
    }).then((response) => {
      expect([403, 404]).to.include(response.status)
      expect(response.body).to.not.include('require_once')
      cy.log('✅ Directory traversal protection works')
    })
  })

  it('should verify file access permissions', () => {
    // Test as admin (should work)
    cy.request({
      url: '/uploads/declaraties/declaratie_15_686bdc48a6d82.png',
      failOnStatusCode: false
    }).then((response) => {
      if (response.status === 200) {
        cy.log('✅ Admin can access files')
      } else if (response.status === 403) {
        cy.log('ℹ️ File access restricted (security working)')
      }
      expect([200, 403]).to.include(response.status)
    })
  })
})
