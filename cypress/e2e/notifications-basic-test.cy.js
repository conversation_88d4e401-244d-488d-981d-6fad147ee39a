describe('Notifications Basic Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should show admin notification badge in header', () => {
    cy.visit('/')
    
    // Check that admin sees Alerts link
    cy.get('header').should('contain.text', 'Alerts')
    
    // Check that notification badge exists
    cy.get('#notification-badge').should('exist')
    
    // Check that notifications link works
    cy.get('a[href="/notifications"]').should('be.visible')
    
    cy.log('✅ Admin notification system is visible')
  })

  it('should access notifications page', () => {
    cy.visit('/notifications')
    
    // Check notifications page loads
    cy.get('h1').should('contain.text', 'Admin Notificaties')
    
    // Should not show database errors
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    cy.get('body').should('not.contain.text', 'Table \'')
    
    cy.log('✅ Notifications page loads correctly')
  })

  it('should show notification management buttons', () => {
    cy.visit('/notifications')
    
    // Check for notification management buttons
    cy.get('body').should('contain.text', 'Oude notificaties opruimen')
    
    // Check that page structure is correct
    cy.get('body').should('contain.text', 'Admin Notificaties')
    
    cy.log('✅ Notification management interface works')
  })

  it('should test notification API endpoint', () => {
    // Test the getUnread endpoint
    cy.request('/notifications/getUnread').then((response) => {
      expect(response.status).to.eq(200)
      expect(response.body).to.have.property('notifications')
      expect(response.body).to.have.property('count')
      
      cy.log('✅ Notification API endpoint works')
    })
  })

  it('should verify notification badge updates', () => {
    cy.visit('/')
    
    // Wait for initial notification check
    cy.wait(2000)
    
    // Check that notification badge exists
    cy.get('#notification-badge').should('exist')
    
    // Badge should either be hidden (no notifications) or show a number
    cy.get('#notification-badge').then(($badge) => {
      const isHidden = $badge.hasClass('hidden')
      const hasNumber = $badge.text().match(/\d+/)
      
      expect(isHidden || hasNumber).to.be.true
      
      cy.log('✅ Notification badge behaves correctly')
    })
  })

  it('should test assets page loads correctly', () => {
    cy.visit('/assets')
    
    // Check that assets page loads
    cy.get('h1').should('contain.text', 'Asset overzicht')
    
    // Should not show database errors
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    
    // Should show table
    cy.get('table').should('be.visible')
    
    cy.log('✅ Assets page loads correctly')
  })

  it('should verify admin sees full asset columns', () => {
    cy.visit('/assets')
    
    // Admin should see these columns
    cy.get('table thead tr th').should('contain.text', 'UID')
    cy.get('table thead tr th').should('contain.text', 'Naam')
    cy.get('table thead tr th').should('contain.text', 'Merk')
    cy.get('table thead tr th').should('contain.text', 'Model')
    cy.get('table thead tr th').should('contain.text', 'Type')
    cy.get('table thead tr th').should('contain.text', 'Status')
    cy.get('table thead tr th').should('contain.text', 'Huidige houder')
    cy.get('table thead tr th').should('contain.text', 'Acties')
    
    cy.log('✅ Admin sees full asset view')
  })

  it('should test regular user asset view', () => {
    // Switch to regular user
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.visit('/assets')
    
    // Regular user should see simplified view
    cy.get('table thead tr th').should('contain.text', 'Naam')
    cy.get('table thead tr th').should('contain.text', 'Merk')
    cy.get('table thead tr th').should('contain.text', 'Model')
    cy.get('table thead tr th').should('contain.text', 'Type')
    cy.get('table thead tr th').should('contain.text', 'Huidige/Laatste houder')
    cy.get('table thead tr th').should('contain.text', 'Acties')
    
    // Should NOT see admin-only columns
    cy.get('table thead tr th').should('not.contain.text', 'UID')
    cy.get('table thead tr th').should('not.contain.text', 'Status')
    
    // Should not see notification system
    cy.get('header').should('not.contain.text', 'Alerts')
    cy.get('#notification-badge').should('not.exist')
    
    cy.log('✅ Regular user sees simplified asset view')
  })

  it('should verify system integration works', () => {
    cy.visit('/')
    
    // Check that all main components load
    cy.get('header').should('be.visible')
    cy.get('body').should('contain.text', 'Welkom,')
    
    // Check navigation works
    cy.get('a[href="/assets"]').click()
    cy.url().should('include', '/assets')
    
    cy.get('a[href="/notifications"]').click()
    cy.url().should('include', '/notifications')
    
    cy.log('✅ System integration works correctly')
  })
})
