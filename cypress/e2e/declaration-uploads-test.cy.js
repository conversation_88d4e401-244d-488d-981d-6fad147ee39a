describe('Declaration Uploads Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should access uploaded declaration files as admin', () => {
    cy.visit('/admin/declarations')
    
    // Controleer dat de admin declaraties pagina laadt
    cy.get('body').should('contain.text', 'Declaraties beheren')
    
    // Klik op een gebruiker met declaraties (als die er is)
    cy.get('body').then(($body) => {
      if ($body.find('a[href*="/admin/declarations/user/"]').length > 0) {
        cy.get('a[href*="/admin/declarations/user/"]').first().click()
        
        // Controleer dat de gebruiker declaraties pagina laadt
        cy.get('body').should('contain.text', 'Declarati<PERSON>')
        
        // <PERSON><PERSON>ar "Bekijk bon/factuur" links
        cy.get('body').then(($userBody) => {
          if ($userBody.find('a:contains("Bekijk bon/factuur")').length > 0) {
            // Test de eerste upload link
            cy.get('a:contains("Bekijk bon/factuur")').first().then(($link) => {
              const href = $link.attr('href')
              cy.log('Testing upload link: ' + href)
              
              // Controleer dat de link het juiste formaat heeft
              expect(href).to.match(/^\/uploads\/declaraties\//)
              
              // Test de link (verwacht 200 of 403, maar niet 404)
              cy.request({
                url: href,
                failOnStatusCode: false
              }).then((response) => {
                // Accepteer 200 (success), 403 (forbidden), maar niet 404 (not found)
                expect([200, 403]).to.include(response.status)
                cy.log(`Upload file response: ${response.status}`)
              })
            })
          } else {
            cy.log('No uploaded files found to test')
          }
        })
      } else {
        cy.log('No users with declarations found')
      }
    })
  })

  it('should have correct upload links format in admin view', () => {
    cy.visit('/admin/declarations')
    
    cy.get('body').then(($body) => {
      if ($body.find('a[href*="/admin/declarations/user/"]').length > 0) {
        cy.get('a[href*="/admin/declarations/user/"]').first().click()
        
        // Controleer dat alle upload links het juiste formaat hebben
        cy.get('a:contains("Bekijk bon/factuur")').each(($link) => {
          const href = $link.attr('href')
          
          // Controleer dat de link begint met /uploads/declaraties/
          expect(href).to.match(/^\/uploads\/declaraties\/[^\/]+$/)
          
          // Controleer dat er geen dubbele slashes zijn
          expect(href).to.not.include('//')
          
          // Controleer dat er geen relatieve paden zijn
          expect(href).to.not.include('../')
          
          cy.log('Valid upload link format: ' + href)
        })
      }
    })
  })

  it('should handle upload route correctly', () => {
    // Test de upload route direct
    cy.request({
      url: '/uploads/declaraties/nonexistent.jpg',
      failOnStatusCode: false
    }).then((response) => {
      // Verwacht 404 voor niet-bestaand bestand, maar niet een routing error
      expect(response.status).to.equal(404)
      expect(response.body).to.include('Bestand niet gevonden')
      cy.log('Upload route handles non-existent files correctly')
    })
  })

  it('should test upload security', () => {
    // Test directory traversal protection
    cy.request({
      url: '/uploads/declaraties/../../../config/database.php',
      failOnStatusCode: false
    }).then((response) => {
      // Moet 404 of 403 zijn, niet de database config
      expect([403, 404]).to.include(response.status)
      expect(response.body).to.not.include('database')
      cy.log('Directory traversal protection works')
    })
  })

  it('should verify upload controller exists', () => {
    // Test dat de UploadController route werkt
    cy.request({
      url: '/uploads/declaraties/test.jpg',
      failOnStatusCode: false
    }).then((response) => {
      // Verwacht niet een 500 server error of routing error
      expect(response.status).to.not.equal(500)
      expect(response.body).to.not.include('Route not found')
      expect(response.body).to.not.include('Class \'UploadController\' not found')
      cy.log('UploadController is properly loaded')
    })
  })

  it('should test user access to own files', () => {
    // Log in als gewone gebruiker
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })
    
    cy.visit('/declarations')
    
    // Controleer dat de declaraties pagina laadt
    cy.get('body').should('contain.text', 'Mijn Declaraties')
    
    // Test upload links voor gewone gebruiker
    cy.get('body').then(($body) => {
      if ($body.find('a:contains("Bekijk bon/factuur")').length > 0) {
        cy.get('a:contains("Bekijk bon/factuur")').each(($link) => {
          const href = $link.attr('href')
          
          // Controleer link formaat
          expect(href).to.match(/^\/uploads\/declaraties\//)
          
          cy.log('User upload link: ' + href)
        })
      } else {
        cy.log('No uploaded files found for user')
      }
    })
  })
})
