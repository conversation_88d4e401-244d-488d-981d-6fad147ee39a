describe('Consistent Styling Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should verify consistent table styling across all pages', () => {
    // Test assets page
    cy.visit('/assets')
    cy.get('table').should('have.class', 'min-w-full')
    cy.get('table').should('have.class', 'divide-y')
    cy.get('table').should('have.class', 'divide-gray-200')
    cy.get('table').should('have.class', 'text-sm')
    cy.get('table').should('have.class', 'text-left')
    
    cy.get('thead').should('have.class', 'bg-gray-100')
    cy.get('thead th').first().should('have.class', 'px-3')
    cy.get('thead th').first().should('have.class', 'py-2')
    cy.get('thead th').first().should('have.class', 'font-medium')
    cy.get('thead th').first().should('have.class', 'text-gray-700')
    
    // Test travel records page
    cy.visit('/travel_records')
    cy.get('table').should('have.class', 'min-w-full')
    cy.get('table').should('have.class', 'divide-y')
    cy.get('table').should('have.class', 'divide-gray-200')
    cy.get('table').should('have.class', 'text-sm')
    cy.get('table').should('have.class', 'text-left')
    
    cy.get('thead').should('have.class', 'bg-gray-100')
    cy.get('thead th').eq(1).should('have.class', 'px-3')
    cy.get('thead th').eq(1).should('have.class', 'py-2')
    cy.get('thead th').eq(1).should('have.class', 'font-medium')
    cy.get('thead th').eq(1).should('have.class', 'text-gray-700')
    
    // Test declarations page
    cy.visit('/declarations')
    cy.get('table').should('have.class', 'min-w-full')
    cy.get('table').should('have.class', 'divide-y')
    cy.get('table').should('have.class', 'divide-gray-200')
    cy.get('table').should('have.class', 'text-sm')
    cy.get('table').should('have.class', 'text-left')
    
    cy.get('thead').should('have.class', 'bg-gray-100')
    cy.get('thead th').eq(1).should('have.class', 'px-3')
    cy.get('thead th').eq(1).should('have.class', 'py-2')
    cy.get('thead th').eq(1).should('have.class', 'font-medium')
    cy.get('thead th').eq(1).should('have.class', 'text-gray-700')
    
    cy.log('✅ Table styling is consistent across all pages')
  })

  it('should verify consistent section styling', () => {
    // Test travel records sections
    cy.visit('/travel_records')
    
    // Filter section
    cy.get('h2:contains("Filters")').parent().should('have.class', 'bg-blue-50')
    cy.get('h2:contains("Filters")').parent().should('have.class', 'border-blue-200')
    cy.get('h2:contains("Filters")').parent().should('have.class', 'rounded-lg')
    
    // Data section
    cy.get('h2:contains("Mijn Reiskosten")').parent().should('have.class', 'bg-white')
    cy.get('h2:contains("Mijn Reiskosten")').parent().should('have.class', 'border-gray-200')
    cy.get('h2:contains("Mijn Reiskosten")').parent().should('have.class', 'rounded-lg')
    
    // Test declarations sections
    cy.visit('/declarations')
    
    // Filter section
    cy.get('h2:contains("Filters")').parent().should('have.class', 'bg-green-50')
    cy.get('h2:contains("Filters")').parent().should('have.class', 'border-green-200')
    cy.get('h2:contains("Filters")').parent().should('have.class', 'rounded-lg')
    
    // Data section
    cy.get('h2:contains("Mijn Declaraties")').parent().should('have.class', 'bg-white')
    cy.get('h2:contains("Mijn Declaraties")').parent().should('have.class', 'border-gray-200')
    cy.get('h2:contains("Mijn Declaraties")').parent().should('have.class', 'rounded-lg')
    
    // Test assets section
    cy.visit('/assets')
    cy.get('h2:contains("Assets")').parent().should('have.class', 'bg-white')
    cy.get('h2:contains("Assets")').parent().should('have.class', 'border-gray-200')
    cy.get('h2:contains("Assets")').parent().should('have.class', 'rounded-lg')
    
    cy.log('✅ Section styling is consistent across all pages')
  })

  it('should verify emoji usage is appropriate and not excessive', () => {
    // Check that main headers don't have emojis
    cy.visit('/travel_records')
    cy.get('h1').should('contain.text', 'Mijn Reiskosten')
    cy.get('h1').should('not.contain.text', '📊')
    cy.get('h1').should('not.contain.text', '🔍')
    
    cy.get('h2:contains("Filters")').should('not.contain.text', '🔍')
    cy.get('h2:contains("Mijn Reiskosten")').should('not.contain.text', '📊')
    
    cy.visit('/declarations')
    cy.get('h1').should('contain.text', 'Mijn Declaraties')
    cy.get('h1').should('not.contain.text', '📊')
    cy.get('h1').should('not.contain.text', '🔍')
    
    cy.get('h2:contains("Filters")').should('not.contain.text', '🔍')
    cy.get('h2:contains("Mijn Declaraties")').should('not.contain.text', '📊')
    
    cy.visit('/assets')
    cy.get('h1').should('contain.text', 'Asset overzicht')
    cy.get('h1').should('not.contain.text', '📊')
    cy.get('h1').should('not.contain.text', '🔍')
    
    cy.get('h2:contains("Assets")').should('not.contain.text', '📊')
    
    cy.log('✅ Emoji usage is appropriate and not excessive')
  })

  it('should verify button styling is consistent', () => {
    // Test travel records buttons
    cy.visit('/travel_records')
    
    // Filter buttons
    cy.get('button:contains("Filter toepassen")').should('have.class', 'bg-blue-600')
    cy.get('a:contains("Filter resetten")').should('have.class', 'bg-gray-500')
    
    // Action buttons
    cy.get('button:contains("Geselecteerde reiskosten indienen")').should('have.class', 'bg-green-600')
    cy.get('button:contains("Nieuwe reiskostenregel toevoegen")').should('have.class', 'bg-blue-600')
    
    // Test declarations buttons
    cy.visit('/declarations')
    
    // Filter buttons
    cy.get('button:contains("Filter toepassen")').should('have.class', 'bg-green-600')
    cy.get('a:contains("Filter resetten")').should('have.class', 'bg-gray-500')
    
    // Action buttons
    cy.get('button:contains("Geselecteerde declaraties indienen")').should('have.class', 'bg-green-600')
    cy.get('button:contains("Nieuwe declaratie toevoegen")').should('have.class', 'bg-blue-600')
    
    cy.log('✅ Button styling is consistent across pages')
  })

  it('should verify text content is clean and professional', () => {
    // Check that button text is clean without excessive emojis
    cy.visit('/travel_records')
    cy.get('button:contains("Filter toepassen")').should('not.contain.text', '🔍')
    cy.get('a:contains("Filter resetten")').should('not.contain.text', '🔄')
    cy.get('button:contains("Geselecteerde reiskosten indienen")').should('not.contain.text', '✅')
    cy.get('button:contains("Nieuwe reiskostenregel toevoegen")').should('not.contain.text', '➕')
    
    cy.visit('/declarations')
    cy.get('button:contains("Filter toepassen")').should('not.contain.text', '🔍')
    cy.get('a:contains("Filter resetten")').should('not.contain.text', '🔄')
    cy.get('button:contains("Geselecteerde declaraties indienen")').should('not.contain.text', '✅')
    cy.get('button:contains("Nieuwe declaratie toevoegen")').should('not.contain.text', '➕')
    
    cy.log('✅ Text content is clean and professional')
  })

  it('should verify layout consistency across different user roles', () => {
    // Test as admin
    cy.visit('/assets')
    cy.get('table thead th').should('contain.text', 'UID')
    cy.get('table thead th').should('contain.text', 'Status')
    
    // Switch to regular user
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })
    
    cy.visit('/assets')
    cy.get('table thead th').should('not.contain.text', 'UID')
    cy.get('table thead th').should('not.contain.text', 'Status')
    
    // But table styling should be the same
    cy.get('table').should('have.class', 'min-w-full')
    cy.get('table').should('have.class', 'divide-y')
    cy.get('table').should('have.class', 'divide-gray-200')
    cy.get('table').should('have.class', 'text-sm')
    cy.get('table').should('have.class', 'text-left')
    
    cy.get('thead').should('have.class', 'bg-gray-100')
    
    cy.log('✅ Layout consistency maintained across user roles')
  })

  it('should verify overall visual hierarchy is consistent', () => {
    const pages = ['/assets', '/travel_records', '/declarations']
    
    pages.forEach(page => {
      cy.visit(page)
      
      // Main title should be h1 with consistent styling
      cy.get('h1').should('have.class', 'text-3xl')
      cy.get('h1').should('have.class', 'font-bold')
      cy.get('h1').should('have.class', 'text-gray-900')
      
      // Section headers should be h2 with consistent styling
      cy.get('h2').should('have.class', 'text-lg')
      cy.get('h2').should('have.class', 'font-medium')
      
      // Tables should have consistent wrapper
      cy.get('table').parent().should('have.class', 'overflow-x-auto')
    })
    
    cy.log('✅ Visual hierarchy is consistent across all pages')
  })
})
