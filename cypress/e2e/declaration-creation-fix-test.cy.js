describe('Declaration Creation Fix Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should successfully create a declaration without foreign key errors', () => {
    cy.visit('/declarations/create')
    
    // Check that the form loads correctly
    cy.get('#titel').should('be.visible')
    cy.get('#totaal_bedrag').should('be.visible')
    cy.get('#bedrag_excl_btw').should('be.visible')
    cy.get('#btw_percentage').should('be.visible')
    cy.get('#product_dienst').should('be.visible')
    cy.get('#bon_factuur').should('be.visible')
    
    // Fill in the form
    cy.get('#titel').type('Test Declaratie - Foreign Key Fix')
    cy.get('#totaal_bedrag').type('121.00')
    cy.get('#bedrag_excl_btw').type('100.00')
    cy.get('#btw_percentage').select('21')
    cy.get('#product_dienst').type('Test product voor foreign key fix')
    
    // Create a test image file
    const fileName = 'test-receipt.png'
    cy.fixture('test-image.png', 'base64').then(fileContent => {
      cy.get('#bon_factuur').selectFile({
        contents: Cypress.Buffer.from(fileContent, 'base64'),
        fileName: fileName,
        mimeType: 'image/png'
      })
    })
    
    // Submit the form
    cy.get('form').submit()
    
    // Should redirect to declarations list without errors
    cy.url().should('include', '/declarations')
    
    // Should not show foreign key constraint error
    cy.get('body').should('not.contain.text', 'SQLSTATE[23000]')
    cy.get('body').should('not.contain.text', 'Integrity constraint violation')
    cy.get('body').should('not.contain.text', 'foreign key constraint fails')
    cy.get('body').should('not.contain.text', '404')
    
    // Should show success message or the new declaration
    cy.get('body').then(($body) => {
      const bodyText = $body.text()
      
      // Either success message or the declaration should be visible
      const hasSuccessMessage = bodyText.includes('succesvol') || bodyText.includes('toegevoegd')
      const hasDeclaration = bodyText.includes('Test Declaratie - Foreign Key Fix')
      
      expect(hasSuccessMessage || hasDeclaration).to.be.true
    })
    
    cy.log('✅ Declaration created successfully without foreign key errors')
  })

  it('should handle session validation correctly', () => {
    cy.visit('/declarations/create')
    
    // Test that the page loads without session errors
    cy.get('body').should('not.contain.text', 'Uw sessie is verlopen')
    cy.get('body').should('not.contain.text', 'Er is een probleem met uw account')
    cy.get('body').should('not.contain.text', 'Log opnieuw in')
    
    // Should show the form
    cy.get('form').should('be.visible')
    cy.get('#titel').should('be.visible')
    
    cy.log('✅ Session validation works correctly')
  })

  it('should validate user exists before creating declaration', () => {
    cy.visit('/declarations/create')
    
    // Fill in minimal form data
    cy.get('#titel').type('Session Validation Test')
    cy.get('#totaal_bedrag').type('50.00')
    cy.get('#bedrag_excl_btw').type('41.32')
    cy.get('#btw_percentage').select('21')
    cy.get('#product_dienst').type('Session test')
    
    // Create a test image file
    cy.fixture('test-image.png', 'base64').then(fileContent => {
      cy.get('#bon_factuur').selectFile({
        contents: Cypress.Buffer.from(fileContent, 'base64'),
        fileName: 'session-test.png',
        mimeType: 'image/png'
      })
    })
    
    // Submit the form
    cy.get('form').submit()
    
    // Should not redirect to login (which would indicate session problems)
    cy.url().should('not.include', '/login')
    
    // Should not show database constraint errors
    cy.get('body').should('not.contain.text', 'Cannot add or update a child row')
    cy.get('body').should('not.contain.text', 'foreign key constraint')
    
    cy.log('✅ User validation works correctly')
  })

  it('should show proper error handling for file upload', () => {
    cy.visit('/declarations/create')
    
    // Fill in form without file
    cy.get('#titel').type('File Upload Test')
    cy.get('#totaal_bedrag').type('25.00')
    cy.get('#bedrag_excl_btw').type('20.66')
    cy.get('#btw_percentage').select('21')
    cy.get('#product_dienst').type('File test')
    
    // Try to submit without file
    cy.get('form').submit()
    
    // Should stay on create page due to required file
    cy.url().should('include', '/declarations/create')
    
    // Should show file requirement message
    cy.get('body').should('contain.text', 'verplicht')
    
    cy.log('✅ File upload validation works correctly')
  })

  it('should verify BTW percentage is saved correctly', () => {
    cy.visit('/declarations/create')
    
    // Test with different BTW percentage
    cy.get('#titel').type('BTW Test Declaration')
    cy.get('#totaal_bedrag').type('109.00')
    cy.get('#bedrag_excl_btw').type('100.00')
    cy.get('#btw_percentage').select('9') // Test 9% BTW
    cy.get('#product_dienst').type('BTW test product')
    
    // Add file
    cy.fixture('test-image.png', 'base64').then(fileContent => {
      cy.get('#bon_factuur').selectFile({
        contents: Cypress.Buffer.from(fileContent, 'base64'),
        fileName: 'btw-test.png',
        mimeType: 'image/png'
      })
    })
    
    // Submit the form
    cy.get('form').submit()
    
    // Should redirect successfully
    cy.url().should('include', '/declarations')
    
    // Should not show database errors
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    
    cy.log('✅ BTW percentage handling works correctly')
  })

  it('should test with regular user account', () => {
    // Test with regular user to ensure it works for non-admins too
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })
    
    cy.visit('/declarations/create')
    
    // Should load form for regular user
    cy.get('#titel').should('be.visible')
    
    // Fill in form
    cy.get('#titel').type('Regular User Test')
    cy.get('#totaal_bedrag').type('75.00')
    cy.get('#bedrag_excl_btw').type('61.98')
    cy.get('#btw_percentage').select('21')
    cy.get('#product_dienst').type('Regular user test')
    
    // Add file
    cy.fixture('test-image.png', 'base64').then(fileContent => {
      cy.get('#bon_factuur').selectFile({
        contents: Cypress.Buffer.from(fileContent, 'base64'),
        fileName: 'regular-user-test.png',
        mimeType: 'image/png'
      })
    })
    
    // Submit the form
    cy.get('form').submit()
    
    // Should work for regular users too
    cy.url().should('include', '/declarations')
    cy.get('body').should('not.contain.text', 'foreign key constraint')
    
    cy.log('✅ Declaration creation works for regular users')
  })
})
