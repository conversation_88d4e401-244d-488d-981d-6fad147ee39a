describe('Admin Asset Management', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should access admin asset management from dashboard', () => {
    cy.visit('/admin')
    
    // Controleer dat admin dashboard laadt
    cy.get('body').should('contain.text', 'Admin Dashboard')
    
    // Klik op "Beheer assets" knop
    cy.get('a[href="/admin/assets"]').should('be.visible').click()
    
    // Controleer dat we op de admin assets pagina zijn
    cy.url().should('include', '/admin/assets')
    cy.get('body').should('contain.text', 'Asset beheer')
  })

  it('should display admin asset overview with management buttons', () => {
    cy.visit('/admin/assets')
    
    // Controleer dat de pagina correct laadt
    cy.get('body').should('contain.text', 'Asset beheer')
    
    // Controleer dat management knoppen aanwezig zijn
    cy.get('a[href="/admin/assets/create"]').should('be.visible').should('contain.text', 'Nieuw asset')
    cy.get('a[href="/admin/assets/manage"]').should('be.visible').should('contain.text', 'Toewijzen/Innemen')
    
    // Controleer dat asset types link aanwezig is
    cy.get('a[href="/admin/asset_types"]').should('be.visible')
  })

  it('should access asset assignment/management page', () => {
    cy.visit('/admin/assets')
    
    // Klik op "Toewijzen/Innemen" knop
    cy.get('a[href="/admin/assets/manage"]').click()
    
    // Controleer dat we op de management pagina zijn
    cy.url().should('include', '/admin/assets/manage')
    cy.get('body').should('contain.text', 'Asset toewijzen/innemen')
    
    // Controleer dat zoekfunctionaliteit aanwezig is
    cy.get('#uid').should('be.visible')
    cy.get('#searchAsset').should('be.visible').should('contain.text', 'Zoeken')
  })

  it('should search for assets by UID in management interface', () => {
    cy.visit('/admin/assets/manage')
    
    // Test zoeken met lege UID
    cy.get('#searchAsset').click()
    
    // Test zoeken met niet-bestaande UID
    cy.get('#uid').type('NONEXISTENT123')
    cy.get('#searchAsset').click()
    
    // Wacht op response en controleer dat er geen asset wordt gevonden
    cy.wait(1000)
    cy.get('body').then(($body) => {
      // Controleer of er een error message of "niet gevonden" bericht is
      const hasErrorMessage = $body.text().match(/niet gevonden|error|geen/i);
      if (hasErrorMessage) {
        cy.log('Correct: Asset niet gevonden bericht getoond')
      } else {
        cy.log('Asset zoekfunctionaliteit werkt (geen specifiek error bericht)')
      }
    })
  })

  it('should handle asset creation workflow', () => {
    cy.visit('/admin/assets')
    
    // Klik op "Nieuw asset" knop
    cy.get('a[href="/admin/assets/create"]').click()
    
    // Controleer dat we op de create pagina zijn
    cy.url().should('include', '/admin/assets/create')
    
    // Controleer dat het formulier aanwezig is
    cy.get('body').then(($body) => {
      if ($body.find('form').length > 0) {
        cy.log('Asset creation form found')
        
        // Controleer belangrijke velden
        const expectedFields = ['uid', 'name']
        expectedFields.forEach(field => {
          if ($body.find(`[name="${field}"], #${field}`).length > 0) {
            cy.log(`Field ${field} found`)
          }
        })
      } else {
        cy.log('Asset creation form structure may vary')
      }
    })
  })

  it('should handle asset editing workflow', () => {
    cy.visit('/admin/assets')
    
    // Zoek naar een "Bewerken" link in de tabel
    cy.get('body').then(($body) => {
      const editLinks = $body.find('a:contains("Bewerken"), a[href*="/admin/assets/edit/"]')
      
      if (editLinks.length > 0) {
        cy.wrap(editLinks.first()).click()
        
        // Controleer dat we op een edit pagina zijn
        cy.url().should('include', '/admin/assets/edit/')
        
        // Controleer dat er een formulier is
        cy.get('body').should('contain.text', 'asset')
        cy.log('Asset edit page accessed successfully')
      } else {
        cy.log('No assets available to edit, or edit links not found')
      }
    })
  })

  it('should display asset management actions in overview', () => {
    cy.visit('/admin/assets')
    
    // Controleer dat de tabel met assets wordt getoond
    cy.get('table').should('be.visible')
    
    // Controleer dat er actie kolommen zijn
    cy.get('body').then(($body) => {
      const hasActions = $body.text().match(/acties|bewerken|verwijderen|details/i);
      
      if (hasActions) {
        cy.log('Asset management actions found in overview')
        
        // Controleer specifieke actie links
        const actionTypes = ['Details', 'Bewerken', 'Geschiedenis', 'Verwijderen']
        actionTypes.forEach(action => {
          if ($body.text().includes(action)) {
            cy.log(`${action} action available`)
          }
        })
      } else {
        cy.log('Asset actions may be structured differently')
      }
    })
  })

  it('should handle asset assignment workflow', () => {
    cy.visit('/admin/assets/manage')
    
    // Test de assignment workflow door een asset te zoeken
    cy.get('#uid').should('be.visible')
    
    // Test Enter key functionaliteit
    cy.get('#uid').type('TEST123{enter}')
    
    // Wacht op response
    cy.wait(1000)
    
    // Controleer dat de zoekfunctionaliteit werkt
    cy.get('body').then(($body) => {
      if ($body.find('#assetDetails').length > 0) {
        cy.log('Asset details section found')
        
        // Controleer of er assignment formulieren zijn
        if ($body.find('#assignForm, form[action*="assign"]').length > 0) {
          cy.log('Asset assignment form found')
        }
      } else {
        cy.log('Asset search completed (asset may not exist)')
      }
    })
  })

  it('should maintain consistent navigation in asset management', () => {
    const assetPages = [
      '/admin/assets',
      '/admin/assets/manage'
    ]
    
    assetPages.forEach(page => {
      cy.visit(page)
      
      // Controleer dat de pagina laadt zonder errors
      cy.get('body').should('not.contain.text', '404')
      cy.get('body').should('not.contain.text', '403')
      cy.get('body').should('not.contain.text', '500')
      
      // Controleer dat er navigatie terug naar admin is
      cy.get('body').then(($body) => {
        const hasBackNavigation = $body.find('a[href="/admin"], a:contains("Terug"), a:contains("overzicht")').length > 0;
        
        if (hasBackNavigation) {
          cy.log(`Back navigation found on ${page}`)
        } else {
          cy.log(`Navigation structure may vary on ${page}`)
        }
      })
    })
  })

  it('should handle asset type management integration', () => {
    cy.visit('/admin/assets')
    
    // Controleer dat asset types link aanwezig is
    cy.get('a[href="/admin/asset_types"]').should('be.visible').click()
    
    // Controleer dat we op asset types pagina komen
    cy.url().should('include', '/admin/asset_types')
    
    // Controleer dat de pagina laadt
    cy.get('body').should('not.contain.text', '404')
    cy.get('body').should('not.contain.text', '403')
  })

  it('should display comprehensive asset information in management interface', () => {
    cy.visit('/admin/assets')
    
    // Controleer dat de asset tabel alle belangrijke kolommen heeft
    cy.get('table').should('be.visible')
    
    // Controleer belangrijke kolom headers
    const expectedColumns = ['UID', 'Naam', 'Status', 'Houder', 'Acties']
    
    expectedColumns.forEach(column => {
      cy.get('body').then(($body) => {
        if ($body.text().includes(column)) {
          cy.log(`Column "${column}" found in asset overview`)
        }
      })
    })
    
    // Controleer dat status indicators werken
    cy.get('body').then(($body) => {
      const hasStatusIndicators = $body.find('.bg-green-100, .bg-yellow-100, .bg-red-100').length > 0;
      
      if (hasStatusIndicators) {
        cy.log('Status indicators found in asset overview')
      } else {
        cy.log('Status display may use different styling')
      }
    })
  })
})
