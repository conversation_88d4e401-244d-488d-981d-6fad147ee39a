describe('Admin Dashboard Button Consistency', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should have consistent button styling across all dashboard sections', () => {
    cy.visit('/admin')
    
    // Controleer dat admin dashboard laadt
    cy.get('body').should('contain.text', 'Admin Dashboard')
    
    // Alle verwachte admin dashboard knoppen
    const expectedButtons = [
      { href: '/admin/users', text: 'Beheer gebruikers', color: 'bg-blue-600' },
      { href: '/admin/travel_records', text: 'Beheer reiskosten', color: 'bg-indigo-600' },
      { href: '/admin/declarations', text: 'Beheer declaraties', color: 'bg-green-600' },
      { href: '/admin/assets', text: 'Beheer assets', color: 'bg-yellow-600' },
      { href: '/logs', text: 'Bekijk logs', color: 'bg-purple-600' }
    ]
    
    expectedButtons.forEach(button => {
      cy.get(`a[href="${button.href}"]`).should('be.visible')
      cy.get(`a[href="${button.href}"]`).should('contain.text', button.text)
      
      // Controleer consistente styling
      cy.get(`a[href="${button.href}"]`).should('have.class', 'inline-block')
      cy.get(`a[href="${button.href}"]`).should('have.class', button.color)
      cy.get(`a[href="${button.href}"]`).should('have.class', 'text-white')
      cy.get(`a[href="${button.href}"]`).should('have.class', 'px-4')
      cy.get(`a[href="${button.href}"]`).should('have.class', 'py-2')
      cy.get(`a[href="${button.href}"]`).should('have.class', 'rounded')
      cy.get(`a[href="${button.href}"]`).should('have.class', 'transition-colors')
      
      // Controleer dat er GEEN text-sm class is (voor consistentie)
      cy.get(`a[href="${button.href}"]`).should('not.have.class', 'text-sm')
      
      cy.log(`Button "${button.text}" has consistent styling`)
    })
  })

  it('should have exactly one button per dashboard section', () => {
    cy.visit('/admin')
    
    // Controleer dat elke sectie precies één hoofdknop heeft
    const sections = [
      { name: 'Gebruikers', expectedButtons: 1 },
      { name: 'Reiskosten', expectedButtons: 1 },
      { name: 'Declaraties', expectedButtons: 1 },
      { name: 'Assets', expectedButtons: 1 },
      { name: 'Logs', expectedButtons: 1 }
    ]
    
    sections.forEach(section => {
      cy.get('body').should('contain.text', section.name)
      cy.log(`Section "${section.name}" found with expected button count`)
    })
    
    // Controleer specifiek dat Assets sectie geen extra knoppen heeft
    cy.get('body').should('not.contain.text', 'In-/Uitgifte')
    cy.get('a[href="/admin/assets/manage"]').should('not.exist')
    
    cy.log('Assets section has only one button (extra button removed)')
  })

  it('should have working navigation for all dashboard buttons', () => {
    const dashboardButtons = [
      { href: '/admin/users', expectedContent: 'Gebruikers beheren' },
      { href: '/admin/travel_records', expectedContent: 'Reiskosten' },
      { href: '/admin/declarations', expectedContent: 'declaraties' },
      { href: '/admin/assets', expectedContent: 'Asset beheer' },
      { href: '/logs', expectedContent: 'logs' }
    ]
    
    dashboardButtons.forEach(button => {
      cy.visit('/admin')
      
      cy.log(`Testing navigation to ${button.href}`)
      
      // Klik op de knop
      cy.get(`a[href="${button.href}"]`).click()
      
      // Controleer dat we op de juiste pagina zijn
      cy.url().should('include', button.href)
      cy.get('body').should('contain.text', button.expectedContent)
      
      // Controleer dat er geen errors zijn
      cy.get('body').should('not.contain.text', '404')
      cy.get('body').should('not.contain.text', '403')
      cy.get('body').should('not.contain.text', '500')
    })
  })

  it('should have consistent card layout across dashboard sections', () => {
    cy.visit('/admin')
    
    // Controleer dat alle secties dezelfde card structuur hebben
    const cardSections = [
      'Gebruikers',
      'Reiskosten', 
      'Declaraties',
      'Assets',
      'Logs'
    ]
    
    cardSections.forEach(section => {
      cy.get('body').should('contain.text', section)
      
      // Elke sectie zou een card met consistent styling moeten hebben
      cy.get('body').then(($body) => {
        const hasCardStyling = $body.find('.rounded-lg.p-6.shadow-sm').length >= cardSections.length
        
        if (hasCardStyling) {
          cy.log(`Card styling consistent for ${section}`)
        } else {
          cy.log(`Card styling may vary for ${section}`)
        }
      })
    })
  })

  it('should display asset statistics without extra management buttons', () => {
    cy.visit('/admin')
    
    // Controleer dat Assets sectie de juiste informatie toont
    cy.get('body').should('contain.text', 'Assets')
    cy.get('body').should('contain.text', 'Totaal aantal assets:')
    cy.get('body').should('contain.text', 'Beschikbaar:')
    cy.get('body').should('contain.text', 'Uitgegeven:')
    
    // Controleer dat er alleen één "Beheer assets" knop is
    cy.get('a[href="/admin/assets"]').should('have.length', 1)
    cy.get('a[href="/admin/assets"]').should('contain.text', 'Beheer assets')
    
    // Controleer dat er geen extra management knoppen zijn
    cy.get('a').should('not.contain.text', 'In-/Uitgifte')
    cy.get('a[href="/admin/assets/manage"]').should('not.exist')
    
    cy.log('Assets section has clean, consistent button layout')
  })

  it('should maintain visual hierarchy and spacing', () => {
    cy.visit('/admin')
    
    // Controleer dat de grid layout correct is
    cy.get('.grid').should('exist')
    cy.get('.grid-cols-1').should('exist')
    cy.get('.md\\:grid-cols-5').should('exist')
    
    // Controleer dat alle secties in de grid staan
    cy.get('.grid .bg-blue-50').should('exist')    // Gebruikers
    cy.get('.grid .bg-indigo-50').should('exist')  // Reiskosten  
    cy.get('.grid .bg-green-50').should('exist')   // Declaraties
    cy.get('.grid .bg-yellow-50').should('exist')  // Assets
    cy.get('.grid .bg-purple-50').should('exist')  // Logs
    
    cy.log('Dashboard maintains proper visual hierarchy and spacing')
  })

  it('should have accessible and keyboard-navigable buttons', () => {
    cy.visit('/admin')
    
    const buttonHrefs = [
      '/admin/users',
      '/admin/travel_records', 
      '/admin/declarations',
      '/admin/assets',
      '/logs'
    ]
    
    buttonHrefs.forEach(href => {
      // Test keyboard focus
      cy.get(`a[href="${href}"]`).focus()
      cy.focused().should('have.attr', 'href', href)
      
      // Test that button is not disabled
      cy.get(`a[href="${href}"]`).should('not.be.disabled')
      
      cy.log(`Button ${href} is keyboard accessible`)
    })
  })

  it('should have proper hover states for all buttons', () => {
    cy.visit('/admin')
    
    const hoverTests = [
      { href: '/admin/users', hoverClass: 'hover:bg-blue-700' },
      { href: '/admin/travel_records', hoverClass: 'hover:bg-indigo-700' },
      { href: '/admin/declarations', hoverClass: 'hover:bg-green-700' },
      { href: '/admin/assets', hoverClass: 'hover:bg-yellow-700' },
      { href: '/logs', hoverClass: 'hover:bg-purple-700' }
    ]
    
    hoverTests.forEach(test => {
      cy.get(`a[href="${test.href}"]`).should('have.class', test.hoverClass)
      cy.log(`Button ${test.href} has proper hover state`)
    })
  })
})
