describe('API Endpoints', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should calculate distance via API', () => {
    const testData = {
      addressA: 'Amsterdam Centraal, Amsterdam',
      addressB: 'Rotterdam Centraal, Rotterdam'
    };

    cy.request({
      method: 'POST',
      url: '/api/calculate-distance',
      body: testData,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      form: true,
      failOnStatusCode: false
    }).then((response) => {
      if (response.status === 200) {
        expect(response.body).to.have.property('distance');
        expect(response.body.distance).to.be.a('number');
        expect(response.body.distance).to.be.greaterThan(0);
        cy.log(`API distance calculated: ${response.body.distance} km`);
      } else {
        cy.log(`API returned status ${response.status}, mogelijk geen Google Maps API key`);
      }
    });
  })

  it('should calculate total distance for multiple addresses via API', () => {
    const testData = {
      addresses: [
        'Amsterdam, Nederland',
        'Utrecht, Nederland', 
        'Rotterdam, Nederland'
      ]
    };

    cy.request({
      method: 'POST',
      url: '/api/calculate-total-distance',
      body: { addresses: JSON.stringify(testData.addresses) },
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      form: true,
      failOnStatusCode: false
    }).then((response) => {
      if (response.status === 200) {
        expect(response.body).to.have.property('distance');
        expect(response.body.distance).to.be.a('number');
        expect(response.body.distance).to.be.greaterThan(0);
        cy.log(`API total distance calculated: ${response.body.distance} km`);
      } else {
        cy.log(`API returned status ${response.status}, mogelijk geen Google Maps API key`);
      }
    });
  })

  it('should handle invalid addresses in API', () => {
    const testData = {
      addressA: 'Ongeldig Adres 12345',
      addressB: 'Nog Een Ongeldig Adres 67890'
    };

    cy.request({
      method: 'POST',
      url: '/api/calculate-distance',
      body: testData,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      form: true,
      failOnStatusCode: false
    }).then((response) => {
      // API zou een 404 of error moeten retourneren voor ongeldige adressen
      if (response.status === 404) {
        expect(response.body).to.have.property('error');
        cy.log('API correctly handled invalid addresses with 404');
      } else if (response.status === 400) {
        expect(response.body).to.have.property('error');
        cy.log('API correctly handled invalid addresses with 400');
      } else {
        cy.log(`API returned unexpected status ${response.status} for invalid addresses`);
      }
    });
  })

  it('should handle missing parameters in API', () => {
    cy.request({
      method: 'POST',
      url: '/api/calculate-distance',
      body: { addressA: 'Amsterdam' }, // Mist addressB
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      form: true,
      failOnStatusCode: false
    }).then((response) => {
      expect(response.status).to.equal(400);
      expect(response.body).to.have.property('error');
      cy.log('API correctly handled missing parameters with 400');
    });
  })

  it('should require authentication for API endpoints', () => {
    // Log uit eerst
    cy.visit('/logout');
    
    // Probeer API aan te roepen zonder authenticatie
    cy.request({
      method: 'POST',
      url: '/api/calculate-distance',
      body: {
        addressA: 'Amsterdam',
        addressB: 'Rotterdam'
      },
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      form: true,
      failOnStatusCode: false
    }).then((response) => {
      // API zou een 401 of redirect naar login moeten geven
      if (response.status === 401) {
        cy.log('API correctly requires authentication (401)');
      } else if (response.status === 302 || response.status === 200) {
        // Mogelijk redirect naar login of andere authenticatie handling
        cy.log(`API authentication handling: status ${response.status}`);
      } else {
        cy.log(`Unexpected API response without auth: ${response.status}`);
      }
    });
  })

  it('should handle CORS correctly', () => {
    cy.request({
      method: 'OPTIONS',
      url: '/api/calculate-distance',
      failOnStatusCode: false
    }).then((response) => {
      // Controleer CORS headers indien aanwezig
      if (response.headers['access-control-allow-origin']) {
        cy.log('CORS headers found');
      } else {
        cy.log('No CORS headers (mogelijk niet nodig voor same-origin requests)');
      }
    });
  })

  it('should validate API response format', () => {
    const testData = {
      addressA: 'Amsterdam, Nederland',
      addressB: 'Rotterdam, Nederland'
    };

    cy.request({
      method: 'POST',
      url: '/api/calculate-distance',
      body: testData,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      form: true,
      failOnStatusCode: false
    }).then((response) => {
      if (response.status === 200) {
        // Controleer JSON response format
        expect(response.headers).to.have.property('content-type');
        expect(response.headers['content-type']).to.include('application/json');
        
        // Controleer response structure
        expect(response.body).to.be.an('object');
        expect(response.body).to.have.property('distance');
        
        cy.log('API response format is correct');
      } else {
        cy.log(`API test skipped due to status ${response.status}`);
      }
    });
  })
})
