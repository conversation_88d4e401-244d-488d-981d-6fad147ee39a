describe('Styling Consistency Final Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should verify consistent table styling across all pages', () => {
    const pages = ['/assets', '/travel_records', '/declarations']
    
    pages.forEach(page => {
      cy.visit(page)
      
      // All tables should have consistent base classes
      cy.get('table').should('have.class', 'min-w-full')
      cy.get('table').should('have.class', 'divide-y')
      cy.get('table').should('have.class', 'divide-gray-200')
      cy.get('table').should('have.class', 'text-sm')
      cy.get('table').should('have.class', 'text-left')
      
      // All table headers should have consistent styling
      cy.get('thead').should('have.class', 'bg-gray-100')
      
      // Table cells should use consistent padding
      cy.get('thead th').first().should('have.class', 'px-3')
      cy.get('thead th').first().should('have.class', 'py-2')
      cy.get('thead th').first().should('have.class', 'font-medium')
      cy.get('thead th').first().should('have.class', 'text-gray-700')
    })
    
    cy.log('✅ Table styling is consistent across all pages')
  })

  it('should verify professional appearance without excessive emojis', () => {
    // Check main page headers are clean
    cy.visit('/assets')
    cy.get('h1').should('contain.text', 'Asset overzicht')
    cy.get('h1').should('not.contain.text', '📊')
    cy.get('h1').should('not.contain.text', '🔍')
    
    cy.visit('/travel_records')
    cy.get('h1').should('contain.text', 'Mijn Reiskosten')
    cy.get('h1').should('not.contain.text', '📊')
    cy.get('h1').should('not.contain.text', '🔍')
    
    cy.visit('/declarations')
    cy.get('h1').should('contain.text', 'Mijn Declaraties')
    cy.get('h1').should('not.contain.text', '📊')
    cy.get('h1').should('not.contain.text', '🔍')
    
    // Check button text is clean
    cy.visit('/travel_records')
    cy.get('button:contains("Filter toepassen")').should('not.contain.text', '🔍')
    cy.get('button:contains("Geselecteerde reiskosten indienen")').should('not.contain.text', '✅')
    
    cy.visit('/declarations')
    cy.get('button:contains("Filter toepassen")').should('not.contain.text', '🔍')
    cy.get('button:contains("Geselecteerde declaraties indienen")').should('not.contain.text', '✅')
    
    cy.log('✅ Professional appearance maintained without excessive emojis')
  })

  it('should verify consistent button styling', () => {
    // Test travel records buttons
    cy.visit('/travel_records')
    cy.get('button:contains("Filter toepassen")').should('have.class', 'bg-blue-600')
    cy.get('a:contains("Filter resetten")').should('have.class', 'bg-gray-500')
    cy.get('button:contains("Geselecteerde reiskosten indienen")').should('have.class', 'bg-green-600')
    cy.get('button:contains("Nieuwe reiskostenregel toevoegen")').should('have.class', 'bg-blue-600')
    
    // Test declarations buttons
    cy.visit('/declarations')
    cy.get('button:contains("Filter toepassen")').should('have.class', 'bg-green-600')
    cy.get('a:contains("Filter resetten")').should('have.class', 'bg-gray-500')
    cy.get('button:contains("Geselecteerde declaraties indienen")').should('have.class', 'bg-green-600')
    cy.get('button:contains("Nieuwe declaratie toevoegen")').should('have.class', 'bg-blue-600')
    
    cy.log('✅ Button styling is consistent and appropriate')
  })

  it('should verify improved filter layout prevents confusion', () => {
    // Test travel records layout
    cy.visit('/travel_records')
    
    // Filter section should be clearly separated
    cy.get('h2:contains("Filters")').should('be.visible')
    cy.get('h2:contains("Filters")').parent().should('have.class', 'bg-blue-50')
    
    // Data section should be clearly separated
    cy.get('h2:contains("Mijn Reiskosten")').should('be.visible')
    cy.get('h2:contains("Mijn Reiskosten")').parent().should('have.class', 'bg-white')
    
    // Submit button should be clearly separated from filters
    cy.get('button:contains("Geselecteerde reiskosten indienen")').parent().should('have.class', 'border-t')
    
    // Test declarations layout
    cy.visit('/declarations')
    
    // Filter section should be clearly separated
    cy.get('h2:contains("Filters")').should('be.visible')
    cy.get('h2:contains("Filters")').parent().should('have.class', 'bg-green-50')
    
    // Data section should be clearly separated
    cy.get('h2:contains("Mijn Declaraties")').should('be.visible')
    cy.get('h2:contains("Mijn Declaraties")').parent().should('have.class', 'bg-white')
    
    // Submit button should be clearly separated from filters
    cy.get('button:contains("Geselecteerde declaraties indienen")').parent().should('have.class', 'border-t')
    
    cy.log('✅ Filter layout prevents confusion between filter and submit actions')
  })

  it('should verify assets page has consistent styling with other pages', () => {
    cy.visit('/assets')
    
    // Should have consistent section wrapper
    cy.get('h2:contains("Assets")').parent().should('have.class', 'bg-white')
    cy.get('h2:contains("Assets")').parent().should('have.class', 'border-gray-200')
    cy.get('h2:contains("Assets")').parent().should('have.class', 'rounded-lg')
    
    // Table should have consistent styling
    cy.get('table').should('have.class', 'min-w-full')
    cy.get('table').should('have.class', 'divide-y')
    cy.get('table').should('have.class', 'divide-gray-200')
    cy.get('table').should('have.class', 'text-sm')
    cy.get('table').should('have.class', 'text-left')
    
    cy.get('thead').should('have.class', 'bg-gray-100')
    
    // Table cells should use consistent padding (px-3 py-2 instead of px-6 py-4)
    cy.get('tbody td').first().should('have.class', 'px-3')
    cy.get('tbody td').first().should('have.class', 'py-2')
    
    cy.log('✅ Assets page styling is consistent with other pages')
  })

  it('should verify role-based views maintain consistent styling', () => {
    // Test admin view
    cy.visit('/assets')
    cy.get('table thead th').should('contain.text', 'UID')
    cy.get('table thead th').should('contain.text', 'Status')
    cy.get('table').should('have.class', 'text-sm') // Consistent base styling
    
    // Switch to regular user
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })
    
    cy.visit('/assets')
    cy.get('table thead th').should('not.contain.text', 'UID')
    cy.get('table thead th').should('not.contain.text', 'Status')
    cy.get('table').should('have.class', 'text-sm') // Same base styling
    cy.get('thead').should('have.class', 'bg-gray-100') // Same header styling
    
    cy.log('✅ Role-based views maintain consistent base styling')
  })

  it('should verify overall design consistency and professionalism', () => {
    const pages = ['/assets', '/travel_records', '/declarations']
    
    pages.forEach(page => {
      cy.visit(page)
      
      // Main container should be consistent
      cy.get('body').should('have.class', 'bg-gray-100')
      
      // Main content wrapper should be consistent
      cy.get('.max-w-7xl').should('exist')
      
      // No database errors should be visible
      cy.get('body').should('not.contain.text', 'SQLSTATE')
      cy.get('body').should('not.contain.text', 'Unknown column')
      cy.get('body').should('not.contain.text', 'Fatal error')
      
      // Tables should be properly wrapped
      cy.get('table').parent().should('have.class', 'overflow-x-auto')
    })
    
    cy.log('✅ Overall design is consistent and professional across all pages')
  })
})
