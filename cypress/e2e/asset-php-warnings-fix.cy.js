describe('Asset PHP Warnings Fix', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should display asset details without PHP warnings', () => {
    cy.visit('/assets')
    
    // Controleer dat er geen PHP warnings zijn op de assets pagina
    cy.get('body').should('not.contain.text', 'Warning: Undefined array key')
    cy.get('body').should('not.contain.text', 'Deprecated: htmlspecialchars()')
    
    // Zoek naar een details link en klik erop
    cy.get('body').then(($body) => {
      const detailsLinks = $body.find('a:contains("Details")')
      
      if (detailsLinks.length > 0) {
        cy.wrap(detailsLinks.first()).click()
        
        // Controleer dat de details pagina geen PHP warnings heeft
        cy.get('body').should('not.contain.text', 'Warning: Undefined array key')
        cy.get('body').should('not.contain.text', 'current_holder_name')
        cy.get('body').should('not.contain.text', 'Deprecated: htmlspecialchars()')
        
        // Controleer dat "Huidige houder" correct wordt getoond
        cy.get('body').should('contain.text', 'Huidige houder')
        
        cy.log('Asset details page displays without PHP warnings')
      } else {
        cy.log('No asset details links found - creating test asset')
        
        // Maak een test asset aan
        cy.visit('/admin/assets/create')
        
        const testAsset = {
          uid: 'PHP-TEST-' + Date.now(),
          name: 'PHP Warning Test Asset'
        }
        
        cy.get('#uid').type(testAsset.uid)
        cy.get('#name').type(testAsset.name)
        cy.get('form[action="/admin/assets/store"]').submit()
        
        // Ga naar de details van het nieuwe asset
        cy.visit('/assets')
        cy.get(`a:contains("${testAsset.uid}")`).should('exist')
        cy.get('a:contains("Details")').first().click()
        
        // Controleer dat er geen PHP warnings zijn
        cy.get('body').should('not.contain.text', 'Warning: Undefined array key')
        cy.get('body').should('not.contain.text', 'Deprecated: htmlspecialchars()')
      }
    })
  })

  it('should display admin assets manage page without PHP warnings', () => {
    cy.visit('/admin/assets/manage')
    
    // Controleer dat de pagina laadt zonder PHP warnings
    cy.get('body').should('not.contain.text', 'Warning: Undefined array key')
    cy.get('body').should('not.contain.text', 'Deprecated: htmlspecialchars()')
    cy.get('body').should('not.contain.text', 'current_holder_name')
    
    cy.log('Admin assets manage page loads without PHP warnings')
  })

  it('should display admin assets overview without PHP warnings', () => {
    cy.visit('/admin/assets')
    
    // Controleer dat de admin assets pagina laadt zonder PHP warnings
    cy.get('body').should('not.contain.text', 'Warning: Undefined array key')
    cy.get('body').should('not.contain.text', 'Deprecated: htmlspecialchars()')
    cy.get('body').should('not.contain.text', 'current_holder_name')
    
    cy.log('Admin assets overview displays without PHP warnings')
  })

  it('should handle asset search without PHP warnings', () => {
    cy.visit('/admin/assets/manage')
    
    // Test zoeken naar een asset
    cy.get('#uid').type('NONEXISTENT123')
    cy.get('#searchAsset').click()
    
    cy.wait(2000)
    
    // Controleer dat er geen PHP warnings zijn na zoeken
    cy.get('body').should('not.contain.text', 'Warning: Undefined array key')
    cy.get('body').should('not.contain.text', 'Deprecated: htmlspecialchars()')
    
    cy.log('Asset search works without PHP warnings')
  })

  it('should display assets with null status correctly', () => {
    cy.visit('/admin/assets/manage')
    
    // Controleer dat assets met null status correct worden getoond
    cy.get('body').should('not.contain.text', 'Deprecated: htmlspecialchars()')
    
    // Controleer dat de tabel wordt getoond
    cy.get('table').should('be.visible')
    
    cy.log('Assets with null status display correctly')
  })
})
