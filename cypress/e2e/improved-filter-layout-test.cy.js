describe('Improved Filter Layout Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })
  })

  it('should verify improved filter layout on travel records page', () => {
    cy.visit('/travel_records')

    // Check that page loads
    cy.get('h1').should('contain.text', 'Mijn Reiskosten')

    // ✅ Check filter section has clear visual separation
    cy.get('h2').should('contain.text', '🔍 Filters')

    // ✅ Check filter buttons are clearly labeled and positioned
    cy.get('button:contains("🔍 Filter toepassen")').should('be.visible')
    cy.get('a:contains("🔄 Filter resetten")').should('be.visible')

    // ✅ Check data section has clear visual separation
    cy.get('h2').should('contain.text', '📊 Mijn Reiskosten')

    // ✅ Check submit button is clearly labeled and separated
    cy.get('button:contains("✅ Geselecteerde reiskosten indienen")').should('be.visible')

    // ✅ Check add new button is clearly labeled
    cy.get('button:contains("➕ Nieuwe reiskostenregel toevoegen")').should('be.visible')

    cy.log('✅ Travel records page has improved filter layout')
  })

  it('should verify improved filter layout on declarations page', () => {
    cy.visit('/declarations')

    // Check that page loads
    cy.get('h1').should('contain.text', 'Mijn Declaraties')

    // ✅ Check filter section has clear visual separation
    cy.get('h2').should('contain.text', '🔍 Filters')

    // ✅ Check filter buttons are clearly labeled and positioned
    cy.get('button:contains("🔍 Filter toepassen")').should('be.visible')
    cy.get('a:contains("🔄 Filter resetten")').should('be.visible')

    // ✅ Check data section has clear visual separation
    cy.get('h2').should('contain.text', '📊 Mijn Declaraties')

    // ✅ Check submit button is clearly labeled and separated
    cy.get('button:contains("✅ Geselecteerde declaraties indienen")').should('be.visible')

    // ✅ Check add new button is clearly labeled
    cy.get('button:contains("➕ Nieuwe declaratie toevoegen")').should('be.visible')

    cy.log('✅ Declarations page has improved filter layout')
  })

  it('should test filter functionality works correctly', () => {
    cy.visit('/travel_records')

    // Test filter application
    cy.get('input[name="date_from"]').type('2024-01-01')
    cy.get('button:contains("🔍 Filter toepassen")').click()

    // Should stay on same page
    cy.url().should('include', '/travel_records')
    cy.url().should('include', 'date_from=2024-01-01')

    // Test filter reset
    cy.get('a:contains("🔄 Filter resetten")').click()

    // Should reset filters
    cy.url().should('not.include', 'date_from')

    cy.log('✅ Filter functionality works correctly')
  })

  it('should verify visual separation between sections', () => {
    cy.visit('/travel_records')

    // Check that filter section has different background
    cy.get('h2:contains("🔍 Filters")').parent().should('have.class', 'bg-blue-50')

    // Check that data section has different background
    cy.get('h2:contains("📊 Mijn Reiskosten")').parent().should('have.class', 'bg-white')

    // Check that submit section has visual separation
    cy.get('button:contains("✅ Geselecteerde reiskosten indienen")').parent().should('have.class', 'border-t')

    cy.log('✅ Visual separation between sections is clear')
  })

  it('should verify button styling and icons are consistent', () => {
    cy.visit('/declarations')

    // Check filter buttons have consistent styling
    cy.get('button:contains("🔍 Filter toepassen")').should('have.class', 'bg-green-600')
    cy.get('a:contains("🔄 Filter resetten")').should('have.class', 'bg-gray-500')

    // Check action buttons have consistent styling
    cy.get('button:contains("✅ Geselecteerde declaraties indienen")').should('have.class', 'bg-green-600')
    cy.get('button:contains("➕ Nieuwe declaratie toevoegen")').should('have.class', 'bg-blue-600')

    // Check all buttons have icons
    cy.get('button:contains("🔍")').should('exist')
    cy.get('a:contains("🔄")').should('exist')
    cy.get('button:contains("✅")').should('exist')
    cy.get('button:contains("➕")').should('exist')

    cy.log('✅ Button styling and icons are consistent')
  })

  it('should verify improved user experience', () => {
    cy.visit('/travel_records')

    // Check that filter section is clearly separated from data
    cy.get('h2:contains("🔍 Filters")').should('be.visible')
    cy.get('h2:contains("📊 Mijn Reiskosten")').should('be.visible')

    // Check that action buttons are clearly separated from data
    cy.get('button:contains("✅ Geselecteerde reiskosten indienen")').parent().should('have.class', 'pt-4')

    // Check that button labels are descriptive
    cy.get('button:contains("Filter toepassen")').should('exist')
    cy.get('a:contains("Filter resetten")').should('exist')
    cy.get('button:contains("Geselecteerde reiskosten indienen")').should('exist')

    cy.log('✅ User experience is improved with clear sections and labels')
  })

  it('should verify layout works on both pages consistently', () => {
    // Test travel records page
    cy.visit('/travel_records')
    cy.get('h2:contains("🔍 Filters")').should('be.visible')
    cy.get('h2:contains("📊 Mijn Reiskosten")').should('be.visible')
    cy.get('button:contains("✅ Geselecteerde reiskosten indienen")').should('be.visible')

    // Test declarations page
    cy.visit('/declarations')
    cy.get('h2:contains("🔍 Filters")').should('be.visible')
    cy.get('h2:contains("📊 Mijn Declaraties")').should('be.visible')
    cy.get('button:contains("✅ Geselecteerde declaraties indienen")').should('be.visible')

    cy.log('✅ Layout is consistent across both pages')
  })
})
