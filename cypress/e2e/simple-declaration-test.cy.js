describe('Simple Declaration Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should load declaration create form without errors', () => {
    cy.visit('/declarations/create')
    
    // Check that the form loads
    cy.get('#titel').should('be.visible')
    cy.get('#totaal_bedrag').should('be.visible')
    cy.get('#bedrag_excl_btw').should('be.visible')
    cy.get('#btw_percentage').should('be.visible')
    cy.get('#product_dienst').should('be.visible')
    cy.get('#bon_factuur').should('be.visible')
    
    // Check that BTW dropdown has correct options
    cy.get('#btw_percentage option[value="0"]').should('exist')
    cy.get('#btw_percentage option[value="9"]').should('exist')
    cy.get('#btw_percentage option[value="21"]').should('exist')
    
    // Check that file upload is required
    cy.get('#bon_factuur').should('have.attr', 'required')
    
    // Check that no database errors are shown
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'foreign key constraint')
    cy.get('body').should('not.contain.text', '404')
    
    cy.log('✅ Declaration create form loads correctly')
  })

  it('should show validation message for missing file', () => {
    cy.visit('/declarations/create')
    
    // Fill in form without file
    cy.get('#titel').type('Test without file')
    cy.get('#totaal_bedrag').type('100.00')
    cy.get('#bedrag_excl_btw').type('82.64')
    cy.get('#btw_percentage').select('21')
    cy.get('#product_dienst').type('Test product')
    
    // Try to submit
    cy.get('form').submit()
    
    // Should stay on create page
    cy.url().should('include', '/declarations/create')
    
    // Should not show foreign key errors
    cy.get('body').should('not.contain.text', 'foreign key constraint')
    cy.get('body').should('not.contain.text', 'SQLSTATE[23000]')
    
    cy.log('✅ Form validation works without foreign key errors')
  })

  it('should handle session correctly', () => {
    cy.visit('/declarations/create')
    
    // Should not redirect to login (indicates session is valid)
    cy.url().should('include', '/declarations/create')
    
    // Should not show session error messages
    cy.get('body').should('not.contain.text', 'Uw sessie is verlopen')
    cy.get('body').should('not.contain.text', 'Er is een probleem met uw account')
    
    // Should show the form
    cy.get('form').should('be.visible')
    
    cy.log('✅ Session handling works correctly')
  })

  it('should display declarations list without errors', () => {
    cy.visit('/declarations')
    
    // Should load declarations page
    cy.get('body').should('contain.text', 'Mijn Declaraties')
    
    // Should not show database errors
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'foreign key constraint')
    cy.get('body').should('not.contain.text', '404')
    
    // Should show BTW percentage column
    cy.get('table th').should('contain.text', 'BTW %')
    
    cy.log('✅ Declarations list loads correctly')
  })

  it('should verify user authentication works', () => {
    // Test that we can access protected pages
    cy.visit('/declarations')
    cy.url().should('include', '/declarations')
    
    cy.visit('/declarations/create')
    cy.url().should('include', '/declarations/create')
    
    // Should not be redirected to login
    cy.url().should('not.include', '/login')
    
    // Should show user info in header
    cy.get('header').should('contain.text', 'Welkom,')
    
    cy.log('✅ User authentication works correctly')
  })
})
