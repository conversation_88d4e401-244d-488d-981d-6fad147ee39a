describe('Assign to Me Functionality Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
  })

  it('should test assign to me functionality as regular user', () => {
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.visit('/assets')
    
    // Check that page loads without errors
    cy.get('h1').should('contain.text', 'Asset overzicht')
    
    // Should not show database errors
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    cy.get('body').should('not.contain.text', 'action_date')
    
    // Check for simplified user view
    cy.get('table thead tr th').should('contain.text', 'Naam')
    cy.get('table thead tr th').should('contain.text', 'Merk')
    cy.get('table thead tr th').should('contain.text', 'Model')
    cy.get('table thead tr th').should('contain.text', 'Type')
    cy.get('table thead tr th').should('contain.text', 'Huidige/Laatste houder')
    cy.get('table thead tr th').should('contain.text', 'Acties')
    
    // Should NOT see admin columns
    cy.get('table thead tr th').should('not.contain.text', 'UID')
    cy.get('table thead tr th').should('not.contain.text', 'Status')
    
    // Look for assign buttons or "not available" text
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        cy.get('table tbody tr').each(($row) => {
          const actionCell = $row.find('td').last()
          const actionText = actionCell.text()
          
          // Should see either assign button or not available text
          const hasAssignButton = actionText.includes('Toewijzen aan mij')
          const hasNotAvailable = actionText.includes('Niet beschikbaar')
          
          expect(hasAssignButton || hasNotAvailable).to.be.true
        })
      }
    })
    
    cy.log('✅ Assign to me functionality is working correctly')
  })

  it('should test actual assignment if available asset exists', () => {
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.visit('/assets')
    
    // Look for an available asset to assign
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        let foundAssignableAsset = false
        
        cy.get('table tbody tr').each(($row, index) => {
          const actionCell = $row.find('td').last()
          const assignButton = actionCell.find('button:contains("Toewijzen aan mij")')
          
          if (assignButton.length > 0 && !foundAssignableAsset) {
            foundAssignableAsset = true
            
            // Click the assign button
            cy.wrap(assignButton).click()
            
            // Should redirect back to assets page
            cy.url().should('include', '/assets')
            
            // Should show success message or no error
            cy.get('body').should('not.contain.text', 'SQLSTATE')
            cy.get('body').should('not.contain.text', 'Unknown column')
            cy.get('body').should('not.contain.text', 'action_date')
            
            return false // Break out of each loop
          }
        })
        
        if (!foundAssignableAsset) {
          cy.log('No assignable assets found - this is OK')
        }
      } else {
        cy.log('No assets found in table')
      }
    })
    
    cy.log('✅ Asset assignment functionality works without errors')
  })

  it('should verify admin sees full asset view', () => {
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    cy.visit('/assets')
    
    // Admin should see all columns
    cy.get('table thead tr th').should('contain.text', 'UID')
    cy.get('table thead tr th').should('contain.text', 'Naam')
    cy.get('table thead tr th').should('contain.text', 'Merk')
    cy.get('table thead tr th').should('contain.text', 'Model')
    cy.get('table thead tr th').should('contain.text', 'Type')
    cy.get('table thead tr th').should('contain.text', 'Status')
    cy.get('table thead tr th').should('contain.text', 'Huidige houder')
    cy.get('table thead tr th').should('contain.text', 'Acties')
    
    // Should not show database errors
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    
    cy.log('✅ Admin sees full asset view without errors')
  })

  it('should verify notification system works for admin', () => {
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    cy.visit('/')
    
    // Check notification badge exists
    cy.get('#notification-badge').should('exist')
    
    // Check notifications page works
    cy.visit('/notifications')
    cy.get('h1').should('contain.text', 'Admin Notificaties')
    
    // Should not show database errors
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    
    // Test notification API
    cy.request('/notifications/getUnread').then((response) => {
      expect(response.status).to.eq(200)
      expect(response.body).to.have.property('notifications')
      expect(response.body).to.have.property('count')
    })
    
    cy.log('✅ Notification system works correctly')
  })

  it('should verify system integration is complete', () => {
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    // Test main pages load without errors
    cy.visit('/')
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    
    cy.visit('/assets')
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    
    cy.visit('/notifications')
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    
    cy.visit('/travel_records')
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    
    cy.visit('/declarations')
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    
    cy.log('✅ All main pages load without database errors')
  })
})
