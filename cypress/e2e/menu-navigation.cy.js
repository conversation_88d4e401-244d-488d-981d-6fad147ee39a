describe('Menu Navigation Tests', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should navigate to Assets page successfully', () => {
    cy.visit('/')

    // Test specifiek de Assets link die we net hebben opgelost
    cy.get('a[href="/assets"]').should('be.visible')
    cy.get('a[href="/assets"]').should('contain.text', 'Assets')

    // Klik op Assets link
    cy.get('a[href="/assets"]').click()

    // Controleer dat we op de juiste pagina zijn
    cy.url().should('include', '/assets')
    cy.url().should('not.include', ':50002') // Geen poortnummer

    // Controleer dat de pagina correct laadt (geen 403 error)
    cy.get('body').should('not.contain.text', '403')
    cy.get('body').should('not.contain.text', 'Forbidden')

    // Controleer dat assets content wordt getoond
    cy.get('body').then(($body) => {
      const hasAssetsContent = $body.text().match(/assets|asset|overzicht|inventaris/i);

      if (hasAssetsContent) {
        cy.log('Assets page loaded successfully with content')
      } else {
        cy.log('Assets page loaded (content structure may vary)')
      }
    })
  })

  it('should navigate between all menu items in sequence', () => {
    cy.visit('/')

    // Test volledige menu navigatie cyclus
    const navigationSequence = [
      { link: '/declarations', name: 'Declaraties' },
      { link: '/travel_records', name: 'Reiskosten' },
      { link: '/assets', name: 'Assets' },
      { link: '/user/profile', name: 'Profiel' },
      { link: '/', name: 'Home' }
    ]

    navigationSequence.forEach((item, index) => {
      cy.log(`Step ${index + 1}: Navigating to ${item.name}`)

      cy.get(`a[href="${item.link}"]`).first().click()
      cy.url().should('include', item.link)

      // Wacht kort voor stabiliteit
      cy.wait(500)

      // Controleer dat pagina laadt zonder errors
      cy.get('body').should('not.contain.text', '404')
      cy.get('body').should('not.contain.text', '403')
      cy.get('body').should('not.contain.text', '500')
    })
  })

  it('should handle admin menu items for admin users', () => {
    cy.visit('/')

    // Controleer admin menu items
    cy.get('body').then(($body) => {
      if ($body.find('a[href="/admin"]').length > 0) {
        cy.log('Testing admin menu navigation')

        // Test admin dashboard
        cy.get('a[href="/admin"]').first().click()
        cy.url().should('include', '/admin')
        cy.get('body').should('not.contain.text', '403')

        // Ga terug naar home
        cy.get('a[href="/"]').first().click()

        // Test andere admin links als ze bestaan
        const adminLinks = [
          '/admin/users',
          '/admin/travel_records',
          '/admin/declarations',
          '/admin/assets'
        ]

        adminLinks.forEach(link => {
          if ($body.find(`a[href="${link}"]`).length > 0) {
            cy.get(`a[href="${link}"]`).click()
            cy.url().should('include', link)
            cy.get('body').should('not.contain.text', '403')
            cy.get('a[href="/"]').first().click() // Terug naar home
          }
        })
      } else {
        cy.log('No admin menu items found (user may not be admin)')
      }
    })
  })

  it('should maintain consistent menu styling and behavior', () => {
    cy.visit('/')

    // Test menu styling en hover effects
    const menuLinks = [
      '/declarations',
      '/travel_records',
      '/assets',
      '/user/profile'
    ]

    menuLinks.forEach(link => {
      cy.get(`a[href="${link}"]`).should('be.visible')
      cy.get(`a[href="${link}"]`).should('have.class', 'text-blue-600')

      // Test hover effect (als CSS classes correct zijn)
      cy.get(`a[href="${link}"]`).should('have.class', 'hover:text-blue-800')
    })

    // Test logout link styling (should be red)
    cy.get('a[href="/logout"]').should('have.class', 'text-red-600')
  })

  it('should handle menu navigation from different starting pages', () => {
    const startingPages = ['/declarations', '/travel_records', '/user/profile']

    startingPages.forEach(startPage => {
      cy.visit(startPage)
      cy.log(`Testing menu navigation from ${startPage}`)

      // Test navigatie naar assets vanuit verschillende startpagina's
      cy.get('a[href="/assets"]').click()
      cy.url().should('include', '/assets')
      cy.get('body').should('not.contain.text', '403')

      // Test terug naar home
      cy.get('a[href="/"]').first().click()
      cy.url().should('match', /\/$|\/index$/)
    })
  })

  it('should handle rapid menu navigation without errors', () => {
    cy.visit('/')

    // Test snelle navigatie tussen menu items
    const rapidNavigation = ['/assets', '/', '/declarations', '/travel_records', '/assets']

    rapidNavigation.forEach((link, index) => {
      cy.get(`a[href="${link}"]`).first().click()
      cy.url().should('include', link)

      // Korte wachttijd tussen navigaties
      cy.wait(200)
    })

    // Controleer dat we uiteindelijk op assets pagina zijn
    cy.url().should('include', '/assets')
    cy.get('body').should('not.contain.text', '403')
    cy.get('body').should('not.contain.text', 'error')
  })

  it('should display user information in menu', () => {
    cy.visit('/')

    // Controleer dat gebruikersinformatie wordt getoond
    cy.get('body').should('contain.text', 'Welkom')

    // Controleer dat username wordt getoond
    cy.get('body').then(($body) => {
      const welcomeText = $body.text();
      const hasUsername = welcomeText.match(/Welkom,\s+\w+/);

      if (hasUsername) {
        cy.log('User welcome message found in menu')
      } else {
        cy.log('User information display may vary')
      }
    })
  })
})
