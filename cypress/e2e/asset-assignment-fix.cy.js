describe('Asset Assignment Fix', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should successfully assign an asset without database errors', () => {
    // Ga naar asset management pagina
    cy.visit('/admin/assets/manage')
    
    // Controleer dat de pagina laadt
    cy.get('body').should('contain.text', 'Asset toewijzen/innemen')
    
    // Zoek naar een beschikbaar asset
    cy.get('body').then(($body) => {
      const assignButtons = $body.find('button:contains("Toewijzen")')
      
      if (assignButtons.length > 0) {
        // Klik op de eerste toewijzen knop
        cy.wrap(assignButtons.first()).click()
        
        // Controleer dat het toewijzingsformulier verschijnt
        cy.get('#assignForm').should('be.visible')
        
        // Selecteer een gebruiker
        cy.get('#assign_user_id').select(1) // Selecteer de eerste gebruiker
        
        // Voeg optionele opmerkingen toe
        cy.get('#comments').type('Test toewijzing via Cypress')
        
        // Submit het formulier
        cy.get('#assignForm form').submit()
        
        // Controleer dat er geen 404 of database errors zijn
        cy.get('body').should('not.contain.text', '404')
        cy.get('body').should('not.contain.text', 'Pagina niet gevonden')
        cy.get('body').should('not.contain.text', 'SQLSTATE')
        cy.get('body').should('not.contain.text', 'Unknown column')
        cy.get('body').should('not.contain.text', 'notes')
        
        // Controleer dat we terug zijn op de manage pagina
        cy.url().should('include', '/admin/assets/manage')
        
        cy.log('Asset assignment completed successfully without database errors')
      } else {
        cy.log('No available assets to assign found')
      }
    })
  })

  it('should handle asset search and assignment workflow', () => {
    cy.visit('/admin/assets/manage')
    
    // Zoek naar een specifiek asset
    cy.get('#uid').type('TESTCODE')
    cy.get('#searchAsset').click()
    
    cy.wait(2000)
    
    // Controleer dat er geen database errors zijn
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    
    // Als het asset gevonden wordt en beschikbaar is
    cy.get('body').then(($body) => {
      if ($body.find('#assignForm').is(':visible')) {
        // Selecteer een gebruiker
        cy.get('#assign_user_id').select(1)
        
        // Voeg opmerkingen toe
        cy.get('#comments').type('Test via asset search')
        
        // Submit
        cy.get('#assignForm form').submit()
        
        // Controleer dat er geen errors zijn
        cy.get('body').should('not.contain.text', '404')
        cy.get('body').should('not.contain.text', 'SQLSTATE')
        
        cy.log('Asset search and assignment workflow completed successfully')
      } else {
        cy.log('Asset not found or not available for assignment')
      }
    })
  })

  it('should handle asset unassignment without database errors', () => {
    cy.visit('/admin/assets/manage')
    
    // Zoek naar een uitgeleend asset
    cy.get('body').then(($body) => {
      const unassignButtons = $body.find('button:contains("Innemen")')
      
      if (unassignButtons.length > 0) {
        // Klik op de eerste innemen knop
        cy.wrap(unassignButtons.first()).click()
        
        // Controleer dat het innemen formulier verschijnt
        cy.get('#unassignForm').should('be.visible')
        
        // Submit het formulier
        cy.get('#unassignForm form').submit()
        
        // Controleer dat er geen database errors zijn
        cy.get('body').should('not.contain.text', '404')
        cy.get('body').should('not.contain.text', 'SQLSTATE')
        cy.get('body').should('not.contain.text', 'Unknown column')
        
        // Controleer dat we terug zijn op de manage pagina
        cy.url().should('include', '/admin/assets/manage')
        
        cy.log('Asset unassignment completed successfully without database errors')
      } else {
        cy.log('No assigned assets to unassign found')
      }
    })
  })

  it('should display asset logs without database errors', () => {
    // Ga naar assets overzicht
    cy.visit('/assets')
    
    // Zoek naar een geschiedenis link
    cy.get('body').then(($body) => {
      const historyLinks = $body.find('a:contains("Geschiedenis")')
      
      if (historyLinks.length > 0) {
        cy.wrap(historyLinks.first()).click()
        
        // Controleer dat de geschiedenis pagina laadt zonder errors
        cy.get('body').should('not.contain.text', 'SQLSTATE')
        cy.get('body').should('not.contain.text', 'Unknown column')
        cy.get('body').should('not.contain.text', '404')
        
        // Controleer dat de pagina geschiedenis informatie toont
        cy.get('body').should('contain.text', 'geschiedenis')
        
        cy.log('Asset history displays without database errors')
      } else {
        cy.log('No asset history links found')
      }
    })
  })

  it('should handle user asset assignment from assets page', () => {
    // Test de gebruiker-kant van asset assignment
    cy.visit('/assets')
    
    // Zoek naar een "Toewijzen aan mij" knop
    cy.get('body').then(($body) => {
      const assignToMeButtons = $body.find('button:contains("Toewijzen aan mij")')
      
      if (assignToMeButtons.length > 0) {
        cy.wrap(assignToMeButtons.first()).click()
        
        // Controleer dat er geen database errors zijn
        cy.get('body').should('not.contain.text', 'SQLSTATE')
        cy.get('body').should('not.contain.text', 'Unknown column')
        cy.get('body').should('not.contain.text', '404')
        
        cy.log('User asset assignment completed successfully')
      } else {
        cy.log('No available assets for user assignment found')
      }
    })
  })

  it('should verify database migration was successful', () => {
    // Test dat de database nu de juiste structuur heeft
    cy.visit('/admin/assets/manage')
    
    // Probeer een asset toe te wijzen om te testen of de database correct is
    cy.get('body').then(($body) => {
      const text = $body.text()
      
      // Controleer dat er geen database structure errors zijn
      expect(text).to.not.include('Unknown column \'notes\'')
      expect(text).to.not.include('Unknown column \'action\'')
      expect(text).to.not.include('SQLSTATE[42S22]')
      
      cy.log('Database migration verification successful')
    })
  })

  it('should handle asset assignment with comments correctly', () => {
    cy.visit('/admin/assets/manage')
    
    // Test dat comments/notes correct worden opgeslagen
    cy.get('body').then(($body) => {
      const assignButtons = $body.find('button:contains("Toewijzen")')
      
      if (assignButtons.length > 0) {
        cy.wrap(assignButtons.first()).click()
        
        cy.get('#assignForm').should('be.visible')
        
        // Selecteer gebruiker
        cy.get('#assign_user_id').select(1)
        
        // Voeg specifieke opmerkingen toe
        const testComment = 'Test comment for database fix - ' + Date.now()
        cy.get('#comments').type(testComment)
        
        // Submit
        cy.get('#assignForm form').submit()
        
        // Controleer dat er geen errors zijn
        cy.get('body').should('not.contain.text', 'SQLSTATE')
        cy.get('body').should('not.contain.text', 'Unknown column')
        
        cy.log('Asset assignment with comments handled correctly')
      }
    })
  })
})
