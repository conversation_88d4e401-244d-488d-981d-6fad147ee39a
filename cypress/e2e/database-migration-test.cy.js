describe('Database Migration Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should verify migration script is accessible', () => {
    // Test that migration script is accessible for admins
    cy.visit('/migrate-online-database.php')
    
    // Should show migration interface
    cy.get('h1').should('contain.text', 'Database Migratie')
    cy.get('body').should('contain.text', 'WAARSCHUWING')
    cy.get('body').should('contain.text', 'Maak eerst een backup')
    
    // Should have check and migrate buttons
    cy.get('button:contains("Check Database Status")').should('be.visible')
    cy.get('button:contains("V<PERSON>r <PERSON>")').should('be.visible')
    
    cy.log('✅ Migration script is accessible and shows correct interface')
  })

  it('should perform database status check', () => {
    cy.visit('/migrate-online-database.php')
    
    // Click check database status
    cy.get('button:contains("Check Database Status")').click()
    
    // Should show check results
    cy.get('h2').should('contain.text', 'Database Status Check')
    cy.get('table').should('be.visible')
    
    // Should show various checks
    cy.get('body').should('contain.text', 'assets.type_id')
    cy.get('body').should('contain.text', 'declaraties.project_number')
    cy.get('body').should('contain.text', 'asset_types table')
    
    cy.log('✅ Database status check works correctly')
  })

  it('should verify current database has expected structure', () => {
    // Test that current database works with new code
    cy.visit('/declarations')
    
    // Should load without errors
    cy.get('h1').should('contain.text', 'Mijn Declaraties')
    
    // Should have project number field (if migration already done)
    cy.get('body').then($body => {
      if ($body.find('input[name="project_number"]').length > 0) {
        cy.get('input[name="project_number"]').should('be.visible')
        cy.log('✅ Project number field exists - migration appears to be done')
      } else {
        cy.log('ℹ️ Project number field not found - migration may be needed')
      }
    })
  })

  it('should verify assets page works with current structure', () => {
    cy.visit('/assets')
    
    // Should load without errors
    cy.get('h1').should('contain.text', 'Asset overzicht')
    
    // Should show assets table
    cy.get('table').should('be.visible')
    
    // Check if new structure is present
    cy.get('body').then($body => {
      const bodyText = $body.text()
      
      // Look for signs of new structure
      if (bodyText.includes('Type') || bodyText.includes('type_name')) {
        cy.log('✅ Assets table appears to have type information')
      } else {
        cy.log('ℹ️ Assets table may need migration for type support')
      }
    })
  })

  it('should verify travel records work correctly', () => {
    cy.visit('/travel_records')
    
    // Should load without errors
    cy.get('h1').should('contain.text', 'Mijn Reiskosten')
    
    // Should have project number functionality
    cy.get('input[name="project_number"]').should('be.visible')
    cy.get('table thead th').should('contain.text', 'Projectnummer')
    
    cy.log('✅ Travel records have project number support')
  })

  it('should test creating new declaration with project number', () => {
    cy.visit('/declarations/create')
    
    // Should load create form
    cy.get('h1').should('contain.text', 'Nieuwe Declaratie')
    
    // Check if project number field exists
    cy.get('body').then($body => {
      if ($body.find('input[name="project_number"]').length > 0) {
        // Project number field exists - test it
        cy.get('input[name="project_number"]').should('be.visible')
        cy.get('input[name="project_number"]').should('have.attr', 'required')
        
        // Fill in minimal form to test validation
        cy.get('input[name="titel"]').type('Test Migration Declaration')
        cy.get('input[name="project_number"]').type('MIGRATION-TEST-001')
        cy.get('input[name="totaal_bedrag"]').type('50.00')
        cy.get('input[name="bedrag_excl_btw"]').type('41.32')
        
        // Don't submit, just verify form structure
        cy.log('✅ Declaration form has project number field and validation')
      } else {
        cy.log('ℹ️ Project number field not found - migration needed')
      }
    })
  })

  it('should verify admin asset management works', () => {
    cy.visit('/admin/assets')
    
    // Should load admin assets page
    cy.get('h1').should('contain.text', 'Asset Beheer')
    
    // Should show assets table
    cy.get('table').should('be.visible')
    
    // Check for asset management functionality
    cy.get('body').then($body => {
      if ($body.find('a:contains("Asset toevoegen")').length > 0) {
        cy.get('a:contains("Asset toevoegen")').should('be.visible')
        cy.log('✅ Admin asset management interface is working')
      } else {
        cy.log('ℹ️ Asset management interface may need updates')
      }
    })
  })

  it('should verify no critical database errors', () => {
    // Test various pages to ensure no SQL errors
    const pages = ['/assets', '/declarations', '/travel_records', '/admin/dashboard']
    
    pages.forEach(page => {
      cy.visit(page)
      
      // Should not show database errors
      cy.get('body').should('not.contain.text', 'SQLSTATE')
      cy.get('body').should('not.contain.text', 'Unknown column')
      cy.get('body').should('not.contain.text', 'Table doesn\'t exist')
      cy.get('body').should('not.contain.text', 'Fatal error')
      
      cy.log(`✅ Page ${page} loads without database errors`)
    })
  })

  it('should verify migration readiness', () => {
    // This test checks if the system is ready for migration
    cy.visit('/migrate-online-database.php')
    
    // Perform status check
    cy.get('button:contains("Check Database Status")').click()
    
    // Analyze results
    cy.get('body').then($body => {
      const bodyText = $body.text()
      
      // Count missing vs existing features
      const missingCount = (bodyText.match(/❌ Ontbreekt/g) || []).length
      const existingCount = (bodyText.match(/✅ Bestaat/g) || []).length
      
      if (missingCount > 0) {
        cy.log(`ℹ️ Migration needed: ${missingCount} features missing, ${existingCount} already exist`)
      } else {
        cy.log(`✅ Migration appears complete: ${existingCount} features exist`)
      }
      
      // Should not have critical errors
      cy.get('body').should('not.contain.text', 'Fout:')
      cy.get('body').should('not.contain.text', 'ERROR:')
    })
  })

  it('should verify rollback script exists', () => {
    // Verify that rollback instructions are available
    cy.visit('/migrate-online-database.php')
    
    // Should show rollback information
    cy.get('body').should('contain.text', 'Rollback Instructies')
    cy.get('body').should('contain.text', 'rollback_migration.sql')
    
    cy.log('✅ Rollback instructions are available')
  })

  it('should test migration safety features', () => {
    cy.visit('/migrate-online-database.php')
    
    // Should have confirmation for migration
    cy.get('button:contains("Voer Migratie Uit")').should('have.attr', 'onclick')
    
    // Should show warnings
    cy.get('.warning').should('be.visible')
    cy.get('body').should('contain.text', 'Maak eerst een backup')
    
    cy.log('✅ Migration has appropriate safety warnings and confirmations')
  })
})
