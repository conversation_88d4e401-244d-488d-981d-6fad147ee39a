describe('Complete System Test - All Features', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
  })

  it('should verify complete role-based asset system works', () => {
    // Test as regular user first
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.visit('/assets')
    
    // ✅ User sees simplified view
    cy.get('table thead tr th').should('contain.text', 'Naam')
    cy.get('table thead tr th').should('contain.text', 'Merk')
    cy.get('table thead tr th').should('contain.text', 'Model')
    cy.get('table thead tr th').should('contain.text', 'Type')
    cy.get('table thead tr th').should('contain.text', 'Huidige/Laatste houder')
    cy.get('table thead tr th').should('contain.text', 'Acties')
    
    // ✅ User does NOT see admin columns
    cy.get('table thead tr th').should('not.contain.text', 'UID')
    cy.get('table thead tr th').should('not.contain.text', 'Status')
    
    // ✅ User does NOT see notification system
    cy.get('#notification-badge').should('not.exist')
    cy.get('header').should('not.contain.text', 'Alerts')
    
    // ✅ User sees assign to me functionality
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        cy.get('table tbody tr').each(($row) => {
          const actionCell = $row.find('td').last()
          const actionText = actionCell.text()
          
          const hasAssignButton = actionText.includes('Toewijzen aan mij')
          const hasNotAvailable = actionText.includes('Niet beschikbaar')
          
          expect(hasAssignButton || hasNotAvailable).to.be.true
        })
      }
    })
    
    cy.log('✅ Regular user functionality: PASSED')
    
    // Now test as admin
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    cy.visit('/assets')
    
    // ✅ Admin sees full view
    cy.get('table thead tr th').should('contain.text', 'UID')
    cy.get('table thead tr th').should('contain.text', 'Naam')
    cy.get('table thead tr th').should('contain.text', 'Merk')
    cy.get('table thead tr th').should('contain.text', 'Model')
    cy.get('table thead tr th').should('contain.text', 'Type')
    cy.get('table thead tr th').should('contain.text', 'Status')
    cy.get('table thead tr th').should('contain.text', 'Huidige houder')
    cy.get('table thead tr th').should('contain.text', 'Acties')
    
    // ✅ Admin sees notification system
    cy.get('#notification-badge').should('exist')
    cy.get('header').should('contain.text', 'Alerts')
    
    cy.log('✅ Admin functionality: PASSED')
  })

  it('should verify notification system is fully functional', () => {
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    // ✅ Test notification badge in header
    cy.visit('/')
    cy.get('#notification-badge').should('exist')
    cy.get('a[href="/notifications"]').should('be.visible')
    
    // ✅ Test notifications page
    cy.visit('/notifications')
    cy.get('h1').should('contain.text', 'Admin Notificaties')
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    
    // ✅ Test notification API
    cy.request('/notifications/getUnread').then((response) => {
      expect(response.status).to.eq(200)
      expect(response.body).to.have.property('notifications')
      expect(response.body).to.have.property('count')
    })
    
    cy.log('✅ Notification system: PASSED')
  })

  it('should verify all main pages work without errors', () => {
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    // ✅ Test all main pages
    const pages = ['/', '/assets', '/notifications', '/travel_records', '/declarations', '/admin']
    
    pages.forEach(page => {
      cy.visit(page)
      cy.get('body').should('not.contain.text', 'SQLSTATE')
      cy.get('body').should('not.contain.text', 'Unknown column')
      cy.get('body').should('not.contain.text', 'Fatal error')
      cy.get('body').should('not.contain.text', 'action_date')
    })
    
    cy.log('✅ All pages load without errors: PASSED')
  })

  it('should verify role-based access control works', () => {
    // ✅ Test regular user cannot access notifications
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.request({
      url: '/notifications',
      failOnStatusCode: false
    }).then((response) => {
      // Should be redirected or get error (not 200)
      expect(response.status).to.not.eq(200)
    })
    
    // ✅ Test admin can access notifications
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    cy.request('/notifications').then((response) => {
      expect(response.status).to.eq(200)
    })
    
    cy.log('✅ Role-based access control: PASSED')
  })

  it('should verify asset assignment functionality works', () => {
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.visit('/assets')
    
    // ✅ Test that assignment functionality exists and works
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        // Look for assign buttons
        cy.get('button:contains("Toewijzen aan mij")').then(($buttons) => {
          if ($buttons.length > 0) {
            // Click first available assign button
            cy.wrap($buttons.first()).click()
            
            // Should redirect back to assets
            cy.url().should('include', '/assets')
            
            // Should not show errors
            cy.get('body').should('not.contain.text', 'SQLSTATE')
            cy.get('body').should('not.contain.text', 'Unknown column')
            cy.get('body').should('not.contain.text', 'action_date')
            
            cy.log('✅ Asset assignment executed successfully')
          } else {
            cy.log('✅ No assignable assets available (this is OK)')
          }
        })
      } else {
        cy.log('✅ No assets in table (this is OK)')
      }
    })
    
    cy.log('✅ Asset assignment functionality: PASSED')
  })

  it('should verify all implemented features are working', () => {
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    // ✅ Feature checklist verification
    cy.visit('/')
    
    // Check admin features
    cy.get('header').should('contain.text', 'Alerts') // ✅ Admin notification badge
    cy.get('#notification-badge').should('exist') // ✅ Real-time notification badge
    
    // Check assets
    cy.visit('/assets')
    cy.get('table').should('be.visible') // ✅ Asset management
    cy.get('table thead tr th').should('contain.text', 'UID') // ✅ Admin sees full view
    
    // Check notifications
    cy.visit('/notifications')
    cy.get('h1').should('contain.text', 'Admin Notificaties') // ✅ Notification dashboard
    
    // Switch to regular user
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })
    
    // Check user features
    cy.visit('/assets')
    cy.get('table').should('be.visible') // ✅ Asset view for users
    cy.get('table thead tr th').should('not.contain.text', 'UID') // ✅ Simplified view
    cy.get('#notification-badge').should('not.exist') // ✅ No notifications for users
    
    // Check assign functionality exists
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        cy.get('table tbody tr').first().within(() => {
          cy.get('td').last().then(($cell) => {
            const text = $cell.text()
            const hasAssignButton = text.includes('Toewijzen aan mij')
            const hasNotAvailable = text.includes('Niet beschikbaar')
            expect(hasAssignButton || hasNotAvailable).to.be.true // ✅ Assign functionality
          })
        })
      }
    })
    
    cy.log('✅ ALL IMPLEMENTED FEATURES ARE WORKING CORRECTLY!')
  })
})
