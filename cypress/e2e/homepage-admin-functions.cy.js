describe('Homepage Admin Functions', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should display Assets beheren button in admin functions section', () => {
    cy.visit('/')

    // Controleer dat we op de homepage zijn
    cy.get('body').should('contain.text', 'Dashboard')

    // Controleer dat Admin functies sectie zichtbaar is voor admin
    cy.get('body').should('contain.text', 'Admin functies')

    // Controleer dat "Assets beheren" knop aanwezig is
    cy.get('a[href="/admin/assets"]').should('be.visible')
    cy.get('a[href="/admin/assets"]').should('contain.text', 'Assets beheren')

    // Controleer dat de knop de juiste styling heeft (geel thema)
    cy.get('a[href="/admin/assets"]').should('have.class', 'bg-yellow-600')
  })

  it('should navigate to admin assets page from homepage', () => {
    cy.visit('/')

    // Klik op "Assets beheren" knop
    cy.get('a[href="/admin/assets"]').click()

    // Controleer dat we op de admin assets pagina zijn
    cy.url().should('include', '/admin/assets')
    cy.get('body').should('contain.text', 'Asset beheer')

    // Controleer dat de pagina correct laadt
    cy.get('body').should('not.contain.text', '404')
    cy.get('body').should('not.contain.text', '403')
  })

  it('should display all admin function buttons for admin users', () => {
    cy.visit('/')

    // Controleer dat alle admin functie knoppen aanwezig zijn
    const expectedAdminButtons = [
      { href: '/admin', text: 'Admin Dashboard' },
      { href: '/admin/users', text: 'Gebruikers beheren' },
      { href: '/admin/travel_records', text: 'Reiskosten beheren' },
      { href: '/admin/declarations', text: 'Declaraties beheren' },
      { href: '/admin/assets', text: 'Assets beheren' }
    ]

    expectedAdminButtons.forEach(button => {
      cy.get(`a[href="${button.href}"]`).should('be.visible')
      cy.get(`a[href="${button.href}"]`).should('contain.text', button.text)
      cy.log(`Admin button "${button.text}" found`)
    })
  })

  it('should test all admin function navigation from homepage', () => {
    const adminFunctions = [
      { href: '/admin', expectedContent: 'Admin Dashboard' },
      { href: '/admin/users', expectedContent: 'Gebruikers beheren' },
      { href: '/admin/travel_records', expectedContent: 'Reiskosten' },
      { href: '/admin/declarations', expectedContent: 'declaraties' },
      { href: '/admin/assets', expectedContent: 'Asset beheer' }
    ]

    adminFunctions.forEach(func => {
      cy.visit('/')

      cy.log(`Testing navigation to ${func.href}`)

      // Klik op de admin functie knop
      cy.get(`a[href="${func.href}"]`).first().click()

      // Controleer dat we op de juiste pagina zijn
      cy.url().should('include', func.href)
      cy.get('body').should('contain.text', func.expectedContent)

      // Controleer dat er geen errors zijn
      cy.get('body').should('not.contain.text', '404')
      cy.get('body').should('not.contain.text', '403')
      cy.get('body').should('not.contain.text', '500')
    })
  })

  it('should not display admin functions for non-admin users', () => {
    // Logout en login als gewone gebruiker
    cy.visit('/logout')

    cy.fixture('users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.visit('/')

    // Controleer dat Admin functies sectie NIET zichtbaar is
    cy.get('body').should('not.contain.text', 'Admin functies')
    cy.get('body').should('not.contain.text', 'Assets beheren')

    // Controleer dat admin links niet aanwezig zijn
    cy.get('a[href="/admin/assets"]').should('not.exist')
    cy.get('a[href="/admin/users"]').should('not.exist')
  })

  it('should maintain consistent styling across admin function buttons', () => {
    cy.visit('/')

    // Controleer dat alle admin knoppen consistent gestyled zijn
    const adminButtons = [
      '/admin',
      '/admin/users',
      '/admin/travel_records',
      '/admin/declarations',
      '/admin/assets'
    ]

    adminButtons.forEach(href => {
      cy.get(`a[href="${href}"]`).should('have.class', 'text-white')
      cy.get(`a[href="${href}"]`).should('have.class', 'px-4')
      cy.get(`a[href="${href}"]`).should('have.class', 'py-2')
      cy.get(`a[href="${href}"]`).should('have.class', 'rounded')

      cy.log(`Button ${href} has consistent styling`)
    })

    // Controleer dat Assets knop de juiste kleur heeft
    cy.get('a[href="/admin/assets"]').should('have.class', 'bg-yellow-600')
    cy.get('a[href="/admin/assets"]').should('have.class', 'hover:bg-yellow-700')
  })

  it('should verify assets button placement and accessibility', () => {
    cy.visit('/')

    // Controleer dat Assets knop op de juiste plek staat (na Declaraties beheren)
    cy.get('a[href="/admin/declarations"]').should('exist')
    cy.get('a[href="/admin/assets"]').should('exist')

    // Test keyboard accessibility
    cy.get('a[href="/admin/assets"]').focus()
    cy.focused().should('contain.text', 'Assets beheren')

    // Test dat de knop clickable is
    cy.get('a[href="/admin/assets"]').should('not.be.disabled')

    cy.log('Assets button is properly placed and accessible')
  })

  it('should integrate well with existing homepage layout', () => {
    cy.visit('/')

    // Controleer dat de homepage nog steeds alle verwachte secties heeft
    cy.get('body').should('contain.text', 'Dashboard')
    cy.get('body').should('contain.text', 'Reiskosten')
    cy.get('body').should('contain.text', 'Declaraties')
    cy.get('body').should('contain.text', 'Admin functies')

    // Controleer dat de layout niet verstoord is
    cy.get('.grid').should('exist') // Dashboard grid layout
    cy.get('.flex').should('exist')  // Admin functions flex layout

    // Controleer dat Assets knop de layout niet breekt
    cy.get('a[href="/admin/assets"]').should('be.visible')
    cy.get('a[href="/admin/assets"]').parent().should('have.class', 'flex')

    cy.log('Assets button integrates well with existing homepage layout')
  })
})
