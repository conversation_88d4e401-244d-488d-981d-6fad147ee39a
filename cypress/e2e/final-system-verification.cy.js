describe('Final System Verification', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
  })

  it('should verify admin notification system works', () => {
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    cy.visit('/')
    
    // Check notification badge exists for admin
    cy.get('#notification-badge').should('exist')
    cy.get('a[href="/notifications"]').should('be.visible')
    
    // Visit notifications page
    cy.visit('/notifications')
    cy.get('h1').should('contain.text', 'Admin Notificaties')
    
    cy.log('✅ Admin notification system works')
  })

  it('should verify admin sees full asset view', () => {
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    cy.visit('/assets')
    
    // Admin should see all columns
    cy.get('table thead tr th').should('contain.text', 'UID')
    cy.get('table thead tr th').should('contain.text', 'Type')
    cy.get('table thead tr th').should('contain.text', 'Status')
    
    cy.log('✅ Admin sees full asset view')
  })

  it('should verify regular user sees simplified asset view', () => {
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.visit('/assets')
    
    // User should see simplified columns
    cy.get('table thead tr th').should('contain.text', 'Naam')
    cy.get('table thead tr th').should('contain.text', 'Merk')
    cy.get('table thead tr th').should('contain.text', 'Model')
    cy.get('table thead tr th').should('contain.text', 'Type')
    cy.get('table thead tr th').should('contain.text', 'Huidige/Laatste houder')
    
    // Should NOT see admin columns
    cy.get('table thead tr th').should('not.contain.text', 'UID')
    cy.get('table thead tr th').should('not.contain.text', 'Status')
    
    // Should not see notification system
    cy.get('#notification-badge').should('not.exist')
    
    cy.log('✅ Regular user sees simplified asset view')
  })

  it('should verify "assign to me" functionality exists', () => {
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.visit('/assets')
    
    // Look for assign to me buttons or "not available" text
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        // Should see either "Toewijzen aan mij" buttons or "Niet beschikbaar" text
        cy.get('table tbody tr').each(($row) => {
          const actionCell = $row.find('td').last()
          const actionText = actionCell.text()
          
          const hasAssignButton = actionText.includes('Toewijzen aan mij')
          const hasNotAvailable = actionText.includes('Niet beschikbaar')
          
          expect(hasAssignButton || hasNotAvailable).to.be.true
        })
      }
    })
    
    cy.log('✅ Assign to me functionality is present')
  })

  it('should verify notification API works', () => {
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    // Test notification API
    cy.request('/notifications/getUnread').then((response) => {
      expect(response.status).to.eq(200)
      expect(response.body).to.have.property('notifications')
      expect(response.body).to.have.property('count')
    })
    
    cy.log('✅ Notification API works')
  })

  it('should verify role-based access control', () => {
    // Test regular user cannot access notifications
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })

    cy.request({
      url: '/notifications',
      failOnStatusCode: false
    }).then((response) => {
      // Should be redirected or get error (not 200)
      expect(response.status).to.not.eq(200)
    })
    
    // Test admin can access notifications
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    cy.request('/notifications').then((response) => {
      expect(response.status).to.eq(200)
    })
    
    cy.log('✅ Role-based access control works')
  })

  it('should verify system integration is complete', () => {
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    // Test main navigation
    cy.visit('/')
    cy.get('a[href="/assets"]').click()
    cy.url().should('include', '/assets')
    
    cy.get('a[href="/notifications"]').click()
    cy.url().should('include', '/notifications')
    
    cy.get('a[href="/admin"]').click()
    cy.url().should('include', '/admin')
    
    // No database errors anywhere
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    cy.get('body').should('not.contain.text', 'Table \'')
    
    cy.log('✅ System integration is complete')
  })

  it('should verify all key features are implemented', () => {
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })

    cy.visit('/')
    
    // Check admin features
    cy.get('header').should('contain.text', 'Alerts')
    cy.get('#notification-badge').should('exist')
    
    // Check assets
    cy.visit('/assets')
    cy.get('table').should('be.visible')
    
    // Check notifications
    cy.visit('/notifications')
    cy.get('h1').should('contain.text', 'Admin Notificaties')
    
    // Switch to regular user
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })
    
    // Check user features
    cy.visit('/assets')
    cy.get('table').should('be.visible')
    cy.get('#notification-badge').should('not.exist')
    
    cy.log('✅ All key features are implemented and working')
  })
})
