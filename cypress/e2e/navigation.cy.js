describe('Navigation & UI', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should navigate to all main menu items correctly', () => {
    cy.visit('/')

    // Test alle hoofdmenu items
    const menuItems = [
      {
        text: 'Declaraties',
        url: '/declarations',
        expectedContent: ['declaraties', 'nieuwe declaratie', 'totaalbedrag']
      },
      {
        text: 'Reiskosten',
        url: '/travel_records',
        expectedContent: ['reiskosten', 'nieuwe reiskostenregel', 'kilometers']
      },
      {
        text: 'Assets',
        url: '/assets',
        expectedContent: ['assets', 'asset', 'overzicht']
      }
    ];

    menuItems.forEach(item => {
      cy.log(`Testing navigation to ${item.text}`)

      // Klik op menu item
      cy.get(`a[href="${item.url}"]`).should('be.visible').click()

      // Controleer URL
      cy.url().should('include', item.url)

      // Controleer dat de pagina correct laadt
      cy.get('body').should('contain.text', item.expectedContent[0])

      // Ga terug naar home voor volgende test
      cy.get('a[href="/"]').first().click()
      cy.url().should('not.include', item.url)
    })
  })

  it('should navigate to user profile', () => {
    cy.visit('/')

    // Test navigatie naar gebruikersprofiel
    cy.get('a[href="/user/profile"]').should('be.visible').click()
    cy.url().should('include', '/user/profile')

    // Controleer dat profiel pagina laadt
    cy.get('body').should('contain.text', 'Profiel')
  })

  it('should navigate to admin section for admin users', () => {
    cy.visit('/')

    // Controleer of admin link zichtbaar is (alleen voor admin gebruikers)
    cy.get('body').then(($body) => {
      if ($body.find('a[href="/admin"]').length > 0) {
        cy.get('a[href="/admin"]').first().should('be.visible').click()
        cy.url().should('include', '/admin')
        cy.get('body').should('contain.text', 'Admin')
        cy.log('Admin navigation successful')
      } else {
        cy.log('Admin link not visible (user might not be admin)')
      }
    })
  })

  it('should handle logout correctly', () => {
    cy.visit('/')

    // Test uitloggen
    cy.get('a[href="/logout"]').should('be.visible').click()

    // Controleer dat we naar login pagina worden geleid
    cy.url().should('include', '/login')
    cy.get('body').should('contain.text', 'Inloggen')
  })

  it('should display correct menu structure', () => {
    cy.visit('/')

    // Controleer dat alle verwachte menu items aanwezig zijn
    const expectedMenuItems = [
      'Declaraties',
      'Reiskosten',
      'Assets',
      'Mijn Profiel',
      'Uitloggen'
    ]

    expectedMenuItems.forEach(item => {
      cy.get('body').should('contain.text', item)
      cy.log(`Menu item "${item}" found`)
    })
  })

  it('should have working logo navigation', () => {
    cy.visit('/declarations')

    // Klik op logo om terug naar home te gaan
    cy.get('a[href="/"]').first().click()
    cy.url().should('not.include', '/declarations')
    cy.url().should('match', /\/$|\/index$/)
  })

  it('should maintain menu visibility across pages', () => {
    const pages = ['/declarations', '/travel_records', '/assets', '/user/profile']

    pages.forEach(page => {
      cy.visit(page)

      // Controleer dat menu items zichtbaar zijn op elke pagina
      cy.get('a[href="/declarations"]').should('be.visible')
      cy.get('a[href="/travel_records"]').should('be.visible')
      cy.get('a[href="/assets"]').should('be.visible')
      cy.get('a[href="/user/profile"]').should('be.visible')
      cy.get('a[href="/logout"]').should('be.visible')

      cy.log(`Menu visibility confirmed on ${page}`)
    })
  })

  it('should handle menu navigation without port numbers', () => {
    cy.visit('/')

    // Test dat menu navigatie geen poortnummers toevoegt
    cy.get('a[href="/assets"]').click()

    cy.url().then((url) => {
      // Controleer dat URL geen onverwachte poortnummers bevat
      const hasUnexpectedPort = url.match(/:(\d+)/) &&
                               !url.includes(':80') &&
                               !url.includes(':443')

      if (hasUnexpectedPort) {
        cy.log(`Warning: URL contains port number: ${url}`)
      } else {
        cy.log('Menu navigation works without unexpected port numbers')
      }
    })
  })

  it('should handle responsive navigation', () => {
    // Test mobiele weergave
    cy.viewport(375, 667); // iPhone SE size
    cy.visit('/');

    // Zoek naar een hamburger menu of mobiele navigatie
    cy.get('body').then(($body) => {
      const hasMobileMenu = $body.find('.hamburger, .mobile-menu, .menu-toggle, button[aria-label*="menu"]').length > 0;

      if (hasMobileMenu) {
        cy.get('.hamburger, .mobile-menu, .menu-toggle, button[aria-label*="menu"]').first().click();
        cy.log('Mobile menu toggled');
      } else {
        cy.log('No mobile menu found, responsive test skipped');
      }
    });

    // Reset naar desktop weergave
    cy.viewport(1280, 720);
  })

  it('should display correct page titles', () => {
    const pages = [
      { url: '/', expectedTitle: /dashboard|home|ecosprong/i },
      { url: '/travel_records', expectedTitle: /reiskosten|travel/i },
      { url: '/declarations', expectedTitle: /declaraties|declarations/i },
      { url: '/assets', expectedTitle: /assets|inventaris/i }
    ];

    pages.forEach(page => {
      cy.visit(page.url);

      // Controleer page title in document head
      cy.title().should('match', page.expectedTitle);

      // Controleer ook h1 of hoofdtitel op de pagina
      cy.get('h1, .page-title, .title').first().should('exist');

      cy.log(`Page title verified for ${page.url}`);
    });
  })

  it('should handle form validation messages', () => {
    cy.visit('/travel_records/create');

    // Probeer formulier in te dienen zonder verplichte velden
    cy.get('form').then(($form) => {
      if ($form.find('button[type="submit"], input[type="submit"]').length > 0) {
        cy.get('button[type="submit"], input[type="submit"]').first().click();

        // Controleer op validatie berichten
        cy.get('body').then(($body) => {
          const hasValidationMessages = $body.text().match(/verplicht|required|invalid|ongeldig/i);

          if (hasValidationMessages) {
            cy.log('Form validation messages displayed');
          } else {
            cy.log('No validation messages found (mogelijk client-side validatie)');
          }
        });
      }
    });
  })

  it('should handle success and error messages', () => {
    cy.visit('/travel_records/create');

    // Vul een geldig formulier in
    cy.get('#date').type('2024-01-15');
    cy.get('#project_number').type('TEST-001');
    cy.get('#address_a').type('Amsterdam, Nederland');
    cy.get('#address_b').type('Rotterdam, Nederland');
    cy.get('#total_km').type('75');

    // Dien formulier in
    cy.get('button[type="submit"], input[type="submit"]').first().click();

    // Controleer op success of error berichten
    cy.get('body').then(($body) => {
      const hasMessages = $body.text().match(/success|error|toegevoegd|opgeslagen|fout/i);

      if (hasMessages) {
        cy.log('Success/error messages displayed');
      } else {
        cy.log('No explicit success/error messages found');
      }
    });
  })

  it('should handle loading states', () => {
    cy.visit('/travel_records/create');

    // Vul adressen in om Google Maps API te triggeren
    cy.get('#address_a').type('Amsterdam, Nederland');
    cy.get('#address_b').type('Rotterdam, Nederland');

    // Zoek naar loading indicators
    cy.get('body').then(($body) => {
      const hasLoadingIndicator = $body.find('.loading, .spinner, .loader, [data-loading]').length > 0;

      if (hasLoadingIndicator) {
        cy.log('Loading indicator found');
      } else {
        cy.log('No loading indicator found (mogelijk te snel of niet geïmplementeerd)');
      }
    });
  })

  it('should handle URL without port numbers', () => {
    // Test dat URLs geen poortnummers bevatten na navigatie
    cy.visit('/');

    cy.url().then((url) => {
      const hasPort = url.match(/:(\d+)/);

      if (hasPort && hasPort[1] !== '80' && hasPort[1] !== '443') {
        cy.log(`URL contains port number: ${hasPort[1]}`);
      } else {
        cy.log('URL does not contain unexpected port numbers');
      }
    });

    // Test navigatie via JavaScript (zoals de navigateTo functie)
    cy.get('body').then(($body) => {
      if ($body.find('a[onclick*="navigateTo"]').length > 0) {
        cy.get('a[onclick*="navigateTo"]').first().click();

        cy.url().then((newUrl) => {
          const hasPort = newUrl.match(/:(\d+)/);

          if (hasPort && hasPort[1] !== '80' && hasPort[1] !== '443') {
            cy.log(`URL after JavaScript navigation contains port: ${hasPort[1]}`);
          } else {
            cy.log('JavaScript navigation correctly removes port numbers');
          }
        });
      }
    });
  })
})
