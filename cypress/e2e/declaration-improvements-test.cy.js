describe('Declaration Improvements Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should have BTW percentage dropdown in create form', () => {
    cy.visit('/declarations/create')
    
    // Check that BTW percentage dropdown exists
    cy.get('#btw_percentage').should('exist')
    cy.get('#btw_percentage').should('be.visible')
    
    // Check that all BTW options are available
    cy.get('#btw_percentage option[value="0"]').should('contain.text', '0% BTW')
    cy.get('#btw_percentage option[value="9"]').should('contain.text', '9% BTW')
    cy.get('#btw_percentage option[value="21"]').should('contain.text', '21% BTW')
    
    // Check that 21% is selected by default
    cy.get('#btw_percentage').should('have.value', '21')
    
    cy.log('✅ BTW percentage dropdown works correctly')
  })

  it('should require file upload for new declarations', () => {
    cy.visit('/declarations/create')
    
    // Check that file upload is required
    cy.get('#bon_factuur').should('have.attr', 'required')
    
    // Check that only image formats are accepted
    cy.get('#bon_factuur').should('have.attr', 'accept', '.jpg,.jpeg,.png,.gif,.webp')
    
    // Check that the label indicates it's required
    cy.get('label[for="bon_factuur"]').should('contain.text', '*')
    
    // Check that help text mentions only images
    cy.get('body').should('contain.text', 'Alleen afbeeldingen toegestaan')
    cy.get('body').should('contain.text', 'Maximaal 5MB')
    
    cy.log('✅ File upload requirements are correct')
  })

  it('should not show "bon gemaild" option anymore', () => {
    cy.visit('/declarations/create')
    
    // Check that the "bon gemaild" checkbox is not present
    cy.get('#bon_factuur_gemaild').should('not.exist')
    cy.get('body').should('not.contain.text', 'Bon/factuur gemaild naar Jordi')
    
    cy.log('✅ "Bon gemaild" option is removed')
  })

  it('should show BTW percentage in declarations list', () => {
    cy.visit('/declarations')
    
    // Check that BTW percentage column exists
    cy.get('table th').should('contain.text', 'BTW %')
    
    // Check that existing declarations show BTW percentage
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        // Check that BTW percentage is displayed (should show as number with %)
        cy.get('table tbody tr').first().should('contain.text', '%')
        cy.log('✅ BTW percentage is displayed in table')
      } else {
        cy.log('ℹ️ No declarations found to test BTW display')
      }
    })
  })

  it('should validate file upload attempts', () => {
    cy.visit('/declarations/create')
    
    // Fill in required fields
    cy.get('#titel').type('Test Declaratie')
    cy.get('#totaal_bedrag').type('100.00')
    cy.get('#bedrag_excl_btw').type('82.64')
    cy.get('#btw_percentage').select('21')
    cy.get('#product_dienst').type('Test product')
    
    // Try to submit without file
    cy.get('form').submit()
    
    // Should not proceed due to required file upload
    cy.url().should('include', '/declarations/create')
    
    cy.log('✅ Form validation prevents submission without file')
  })

  it('should test admin view with BTW percentage', () => {
    cy.visit('/admin/declarations')
    
    cy.get('body').then(($body) => {
      if ($body.find('a[href*="/admin/declarations/user/"]').length > 0) {
        cy.get('a[href*="/admin/declarations/user/"]').first().click()
        
        // Check that BTW percentage column exists in admin view
        cy.get('table th').should('contain.text', 'BTW %')
        
        // Check that declarations show BTW percentage
        cy.get('table tbody tr').then(($rows) => {
          if ($rows.length > 0) {
            cy.get('table tbody tr').first().should('contain.text', '%')
            cy.log('✅ BTW percentage is displayed in admin view')
          }
        })
        
        // Check that "bon gemaild" references are removed
        cy.get('body').should('not.contain.text', 'Gemaild')
        
      } else {
        cy.log('ℹ️ No users with declarations found for admin test')
      }
    })
  })

  it('should test edit form with BTW percentage', () => {
    cy.visit('/declarations')
    
    cy.get('body').then(($body) => {
      if ($body.find('a:contains("Bewerken")').length > 0) {
        cy.get('a:contains("Bewerken")').first().click()
        
        // Check that BTW percentage dropdown exists in edit form
        cy.get('#btw_percentage').should('exist')
        cy.get('#btw_percentage').should('be.visible')
        
        // Check that all BTW options are available
        cy.get('#btw_percentage option[value="0"]').should('exist')
        cy.get('#btw_percentage option[value="9"]').should('exist')
        cy.get('#btw_percentage option[value="21"]').should('exist')
        
        // Check that file upload accepts only images
        cy.get('#bon_factuur').should('have.attr', 'accept', '.jpg,.jpeg,.png,.gif,.webp')
        
        // Check that "bon gemaild" checkbox is not present
        cy.get('#bon_factuur_gemaild').should('not.exist')
        
        cy.log('✅ Edit form has correct BTW and file upload settings')
      } else {
        cy.log('ℹ️ No editable declarations found')
      }
    })
  })

  it('should verify database structure supports BTW percentage', () => {
    // This test verifies that the application can handle BTW percentage
    cy.visit('/declarations/create')
    
    // Test that different BTW percentages can be selected
    cy.get('#btw_percentage').select('0')
    cy.get('#btw_percentage').should('have.value', '0')
    
    cy.get('#btw_percentage').select('9')
    cy.get('#btw_percentage').should('have.value', '9')
    
    cy.get('#btw_percentage').select('21')
    cy.get('#btw_percentage').should('have.value', '21')
    
    cy.log('✅ BTW percentage selection works correctly')
  })

  it('should show improved file upload messaging', () => {
    cy.visit('/declarations/create')
    
    // Check for improved messaging about file requirements
    cy.get('body').should('contain.text', 'Verplicht')
    cy.get('body').should('contain.text', 'Alleen afbeeldingen toegestaan')
    cy.get('body').should('contain.text', 'JPG, JPEG, PNG, GIF, WEBP')
    cy.get('body').should('contain.text', 'Maximaal 5MB')
    
    // Check that PDF is no longer mentioned as allowed
    cy.get('body').should('not.contain.text', 'PDF')
    
    cy.log('✅ File upload messaging is improved and accurate')
  })
})
