describe('Compact Header Verification', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should have very compact header with minimal whitespace', () => {
    cy.visit('/')

    // Check that header has minimal padding (py-1)
    cy.get('header > div').should('have.class', 'py-1')

    // Verify header doesn't have excessive padding
    cy.get('header > div').should('not.have.class', 'py-2')
    cy.get('header > div').should('not.have.class', 'py-4')
    cy.get('header > div').should('not.have.class', 'py-6')

    cy.log('✅ Header has minimal padding (py-1)')
  })

  it('should display new logo from root directory', () => {
    cy.visit('/')

    // Check that new logo path is used
    cy.get('header img').should('have.attr', 'src', '/Logo%20Green%20&%20Black.svg')

    // Check that logo is visible
    cy.get('header img').should('be.visible')

    // Check that logo has large size
    cy.get('header img').should('have.class', 'h-16')

    cy.log('✅ New logo from root directory is displayed')
  })

  it('should maintain large logo size with compact header', () => {
    cy.visit('/')

    // Logo should be large (h-16 = 64px)
    cy.get('header img').should('have.class', 'h-16')

    // Header should be compact (py-1 = 4px top/bottom)
    cy.get('header > div').should('have.class', 'py-1')

    // Logo should have proper margin
    cy.get('header img').should('have.class', 'mr-3')

    cy.log('✅ Large logo with compact header achieved')
  })

  it('should verify header layout is still functional', () => {
    cy.visit('/')

    // Check that header has proper flex layout
    cy.get('header > div').should('have.class', 'flex')
    cy.get('header > div').should('have.class', 'justify-between')
    cy.get('header > div').should('have.class', 'items-center')

    // Check that navigation is still visible
    cy.get('header').should('contain.text', 'Welkom,')
    cy.get('header').should('contain.text', 'Uitloggen')

    // Check that logo is clickable
    cy.get('header a[href="/"]').should('be.visible')

    cy.log('✅ Header layout remains functional')
  })

  it('should test header across different pages', () => {
    const pages = ['/', '/declarations', '/travel_records', '/admin']

    pages.forEach(page => {
      cy.visit(page)

      // Check consistent compact padding
      cy.get('header > div').should('have.class', 'py-1')

      // Check consistent logo size
      cy.get('header img').should('have.class', 'h-16')

      // Check consistent logo source
      cy.get('header img').should('have.attr', 'src', '/Logo%20Green%20&%20Black.svg')

      cy.log(`✅ Header is consistent on ${page}`)
    })
  })

  it('should verify minimal whitespace around logo', () => {
    cy.visit('/')

    // Take screenshot to verify visual appearance
    cy.get('header').screenshot('compact-header-with-large-logo')

    // Check that header height is reasonable
    cy.get('header').then(($header) => {
      const headerHeight = $header.height()

      // Header should be compact but accommodate the large logo
      expect(headerHeight).to.be.lessThan(100) // Should be compact
      expect(headerHeight).to.be.greaterThan(60) // But accommodate h-16 logo

      cy.log(`Header height: ${headerHeight}px`)
    })

    cy.log('✅ Minimal whitespace achieved around logo')
  })

  it('should confirm logo prominence and visibility', () => {
    cy.visit('/')

    // Logo should be prominent and visible
    cy.get('header img').should('be.visible')

    // Logo should load successfully
    cy.get('header img').should(($img) => {
      expect($img[0].complete).to.be.true
    })

    // Logo should have proper alt text
    cy.get('header img').should('have.attr', 'alt').and('not.be.empty')

    cy.log('✅ Logo is prominent and properly configured')
  })

  it('should verify the improvement from original design', () => {
    cy.visit('/')

    // Current state should have:
    // - Large logo (h-16 instead of h-10)
    cy.get('header img').should('have.class', 'h-16')
    cy.get('header img').should('not.have.class', 'h-10')

    // - Compact header (py-1 instead of py-4 or py-6)
    cy.get('header > div').should('have.class', 'py-1')
    cy.get('header > div').should('not.have.class', 'py-4')
    cy.get('header > div').should('not.have.class', 'py-6')

    // - New logo source
    cy.get('header img').should('have.attr', 'src', '/Logo%20Green%20&%20Black.svg')
    cy.get('header img').should('not.have.attr', 'src', '/images/logo.svg')

    cy.log('✅ All improvements successfully implemented')
  })
})
