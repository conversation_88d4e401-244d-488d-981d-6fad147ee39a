describe('Logs User Verification', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should show user column in logs table', () => {
    cy.visit('/logs')
    
    // Check that logs page loads
    cy.get('h1').should('contain.text', 'Logviewer')
    
    // Check that user column exists
    cy.get('table thead tr th').should('contain.text', 'Gebruiker')
    
    // Check that table has 4 columns
    cy.get('table thead tr th').should('have.length', 4)
    
    // Verify column order: Tijd, Level, Gebruiker, Bericht
    cy.get('table thead tr th').eq(0).should('contain.text', 'Tijd')
    cy.get('table thead tr th').eq(1).should('contain.text', 'Level')
    cy.get('table thead tr th').eq(2).should('contain.text', 'Gebruiker')
    cy.get('table thead tr th').eq(3).should('contain.text', 'Bericht')
    
    cy.log('✅ User column is properly displayed in logs table')
  })

  it('should generate log entry with user info when creating declaration', () => {
    // Create a declaration to generate a log entry
    cy.visit('/declarations/create')
    
    // Fill in form
    cy.get('#titel').type('User Info Log Test')
    cy.get('#totaal_bedrag').type('25.00')
    cy.get('#bedrag_excl_btw').type('20.66')
    cy.get('#btw_percentage').select('21')
    cy.get('#product_dienst').type('Test voor user logs')
    
    // Add file
    cy.fixture('test-image.png', 'base64').then(fileContent => {
      cy.get('#bon_factuur').selectFile({
        contents: Cypress.Buffer.from(fileContent, 'base64'),
        fileName: 'user-log-test.png',
        mimeType: 'image/png'
      })
    })
    
    // Submit form
    cy.get('form').submit()
    
    // Should redirect successfully
    cy.url().should('include', '/declarations')
    
    // Check logs for the new entry
    cy.visit('/logs')
    
    // Look for recent declaration log
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        cy.log(`Found ${$rows.length} log entries`)
        
        // Check first few rows for declaration-related logs
        cy.get('table tbody tr').first().then(($row) => {
          const messageCell = $row.find('td').eq(3) // Fourth column (Bericht)
          const userCell = $row.find('td').eq(2) // Third column (Gebruiker)
          
          const messageText = messageCell.text()
          const userText = userCell.text()
          
          cy.log('Latest log entry:')
          cy.log('Message: ' + messageText)
          cy.log('User: ' + userText)
          
          // If this is a declaration log, it should have user info
          if (messageText.toLowerCase().includes('declaratie')) {
            expect(userText).to.not.be.empty
            expect(userText).to.not.include('Onbekend')
            expect(userText).to.not.include('null')
          }
        })
      }
    })
    
    cy.log('✅ Declaration creation generates log with user info')
  })

  it('should parse user information correctly from log messages', () => {
    cy.visit('/logs')
    
    // Check that logs are displayed
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        cy.log('Checking log parsing...')
        
        // Check each log entry
        cy.get('table tbody tr').each(($row) => {
          const messageCell = $row.find('td').eq(3) // Fourth column (Bericht)
          const userCell = $row.find('td').eq(2) // Third column (Gebruiker)
          
          const messageText = messageCell.text()
          const userText = userCell.text()
          
          // Message should not contain user prefixes
          expect(messageText).to.not.match(/\[User \d+:[^\]]+\]:/)
          expect(messageText).to.not.match(/User \d+:[^\s]+/)
          
          // User cell should either show user info or "Systeem"
          expect(userText).to.not.be.empty
          
          // If user info is shown, it should be properly formatted
          if (!userText.includes('Systeem') && !userText.includes('guest')) {
            // Should not contain raw user IDs or malformed data
            expect(userText).to.not.match(/User \d+/)
            expect(userText).to.not.include('null')
            expect(userText).to.not.include('undefined')
          }
        })
      } else {
        cy.log('No logs found to test parsing')
      }
    })
    
    cy.log('✅ Log parsing works correctly')
  })

  it('should verify logs functionality is working', () => {
    cy.visit('/logs')
    
    // Basic functionality checks
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    cy.get('body').should('not.contain.text', 'Parse error')
    
    // Check that search field exists
    cy.get('#search').should('be.visible')
    
    // Check that level filter exists
    cy.get('#level').should('be.visible')
    
    // Check that test log button exists
    cy.get('#test-log-btn').should('be.visible')
    
    // Check that clear logs button exists
    cy.get('button[type="submit"]').should('contain.text', 'Leeg Logbestand')
    
    cy.log('✅ Logs functionality is working correctly')
  })

  it('should verify user info enhancement is complete', () => {
    cy.visit('/logs')
    
    // Verify the enhancement is working
    cy.get('table').should('be.visible')
    cy.get('table thead tr th').should('have.length', 4)
    cy.get('table thead tr th').eq(2).should('contain.text', 'Gebruiker')
    
    // Check that empty state shows correct colspan
    cy.get('table tbody').then(($tbody) => {
      const rows = $tbody.find('tr')
      if (rows.length === 1 && rows.text().includes('Geen logs gevonden')) {
        // Empty state should span 4 columns
        cy.get('table tbody tr td').should('have.attr', 'colspan', '4')
      }
    })
    
    cy.log('✅ User info enhancement is complete and working')
  })
})
