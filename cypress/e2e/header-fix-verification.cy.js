describe('Header Fix Verification', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should display header without PHP warnings or errors', () => {
    cy.visit('/')
    
    // Check that there are no PHP warnings in the page
    cy.get('body').should('not.contain.text', 'Warning: Trying to access array offset on false')
    cy.get('body').should('not.contain.text', 'Deprecated: htmlspecialchars(): Passing null')
    cy.get('body').should('not.contain.text', 'Warning:')
    cy.get('body').should('not.contain.text', 'Deprecated:')
    
    cy.log('✅ No PHP warnings or errors in header')
  })

  it('should display welcome message correctly', () => {
    cy.visit('/')
    
    // Check that the welcome message is displayed correctly
    cy.get('header').should('contain.text', 'Welkom,')
    
    // Should not show "Welkom, " with nothing after it
    cy.get('header').should('not.contain.text', 'Welkom,  ')
    cy.get('header').should('not.contain.text', 'Welkom, undefined')
    cy.get('header').should('not.contain.text', 'Welkom, null')
    
    cy.log('✅ Welcome message displays correctly')
  })

  it('should have working logo image', () => {
    cy.visit('/')
    
    // Check that the logo image exists and has correct attributes
    cy.get('header img[alt*="Reiskosten"]').should('exist')
    cy.get('header img[src="/images/logo.svg"]').should('exist')
    
    // Test that the logo image loads successfully
    cy.get('header img[src="/images/logo.svg"]').should('be.visible')
    
    // Test direct access to logo file
    cy.request('/images/logo.svg').then((response) => {
      expect(response.status).to.equal(200)
      expect(response.headers['content-type']).to.include('image/svg')
    })
    
    cy.log('✅ Logo image loads correctly')
  })

  it('should display admin menu correctly', () => {
    cy.visit('/')
    
    // Check that admin menu is visible for admin user
    cy.get('header').should('contain.text', 'Admin')
    cy.get('header a[href="/admin"]').should('exist')
    
    // Check that all menu items are present
    cy.get('header').should('contain.text', 'Declaraties')
    cy.get('header').should('contain.text', 'Reiskosten')
    cy.get('header').should('contain.text', 'Assets')
    cy.get('header').should('contain.text', 'Mijn Profiel')
    cy.get('header').should('contain.text', 'Uitloggen')
    
    cy.log('✅ Admin menu displays correctly')
  })

  it('should handle user data safely', () => {
    cy.visit('/')
    
    // Check that the page loads without any JavaScript errors
    cy.window().then((win) => {
      // Check for any console errors
      cy.wrap(win.console).should('exist')
    })
    
    // Check that user-related elements are properly escaped
    cy.get('header').then(($header) => {
      const headerText = $header.text()
      
      // Should not contain any unescaped HTML or script tags
      expect(headerText).to.not.include('<script')
      expect(headerText).to.not.include('javascript:')
      expect(headerText).to.not.include('&lt;')
      expect(headerText).to.not.include('&gt;')
      
      cy.log('✅ User data is properly escaped')
    })
  })

  it('should test with regular user', () => {
    // Test with regular user to ensure no admin-specific errors
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })
    
    cy.visit('/')
    
    // Check that there are no PHP warnings for regular user
    cy.get('body').should('not.contain.text', 'Warning:')
    cy.get('body').should('not.contain.text', 'Deprecated:')
    
    // Check that welcome message works for regular user
    cy.get('header').should('contain.text', 'Welkom,')
    
    // Check that admin menu is NOT visible for regular user
    cy.get('header').should('not.contain.text', 'Admin')
    cy.get('header a[href="/admin"]').should('not.exist')
    
    cy.log('✅ Regular user header works correctly')
  })

  it('should verify View::escape function works correctly', () => {
    cy.visit('/')
    
    // Test that the page renders without errors
    cy.get('body').should('be.visible')
    
    // Check that special characters are properly handled
    cy.get('header').then(($header) => {
      const headerHtml = $header.html()
      
      // Should not contain raw null or undefined values
      expect(headerHtml).to.not.include('null')
      expect(headerHtml).to.not.include('undefined')
      
      cy.log('✅ View::escape function works correctly')
    })
  })
})
