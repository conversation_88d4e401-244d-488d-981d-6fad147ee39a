describe('Declarations Project Number Simple Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.user.username, users.user.password)
    })
  })

  it('should display project number functionality in declarations', () => {
    // Test index page
    cy.visit('/declarations')
    cy.get('h1').should('contain.text', 'Mijn Declaraties')
    
    // Check project number filter field
    cy.get('input[name="project_number"]').should('be.visible')
    cy.get('label[for="project_number"]').should('contain.text', 'Projectnummer')
    
    // Check table header includes project number
    cy.get('table thead th').should('contain.text', 'Projectnummer')
    
    cy.log('✅ Project number filter and table header are present')
  })

  it('should display project number field in create form', () => {
    cy.visit('/declarations/create')
    cy.get('h1').should('contain.text', '<PERSON>eu<PERSON> Declaratie')
    
    // Check project number field exists and is required
    cy.get('input[name="project_number"]').should('be.visible')
    cy.get('input[name="project_number"]').should('have.attr', 'required')
    cy.get('label[for="project_number"]').should('contain.text', 'Projectnummer')
    cy.get('input[name="project_number"]').should('have.attr', 'placeholder', 'Bijv. 2024-001')
    
    cy.log('✅ Project number field is present and properly configured in create form')
  })

  it('should test project number filtering functionality', () => {
    cy.visit('/declarations')
    
    // Test filter input
    cy.get('input[name="project_number"]').type('TEST-PROJECT')
    cy.get('button:contains("Filter toepassen")').click()
    
    // Should apply filter to URL
    cy.url().should('include', 'project_number=TEST-PROJECT')
    
    // Test filter reset
    cy.get('a:contains("Filter resetten")').click()
    cy.url().should('not.include', 'project_number')
    cy.get('input[name="project_number"]').should('have.value', '')
    
    cy.log('✅ Project number filtering works correctly')
  })

  it('should verify table structure includes project number column', () => {
    cy.visit('/declarations')
    
    // Count table headers to ensure project number column is added
    cy.get('table thead th').should('have.length', 10) // Should be 10 columns now (was 9 before)
    
    // Verify specific headers exist in correct order
    cy.get('table thead th').eq(1).should('contain.text', 'Titel')
    cy.get('table thead th').eq(2).should('contain.text', 'Projectnummer')
    cy.get('table thead th').eq(3).should('contain.text', 'Totaal Bedrag')
    
    cy.log('✅ Table structure correctly includes project number column')
  })

  it('should verify edit form includes project number', () => {
    cy.visit('/declarations')
    
    // Check if there are any declarations to edit
    cy.get('table tbody tr').then($rows => {
      if ($rows.length > 1 || !$rows.first().find('td').text().includes('Geen declaraties gevonden')) {
        // Look for edit link
        cy.get('body').then($body => {
          if ($body.find('a:contains("Bewerken")').length > 0) {
            cy.get('a:contains("Bewerken")').first().click()
            
            // Should be on edit page
            cy.url().should('include', '/declarations/edit/')
            cy.get('h1').should('contain.text', 'Declaratie Bewerken')
            
            // Check project number field
            cy.get('input[name="project_number"]').should('be.visible')
            cy.get('input[name="project_number"]').should('have.attr', 'required')
            cy.get('label[for="project_number"]').should('contain.text', 'Projectnummer')
            
            cy.log('✅ Project number field is present in edit form')
          } else {
            cy.log('ℹ️ No editable declarations found')
          }
        })
      } else {
        cy.log('ℹ️ No declarations found to test edit functionality')
      }
    })
  })

  it('should verify consistency with travel records', () => {
    // Check declarations create form
    cy.visit('/declarations/create')
    cy.get('input[name="project_number"]').should('have.attr', 'placeholder', 'Bijv. 2024-001')
    
    // Check travel records create form for consistency
    cy.visit('/travel_records/create')
    cy.get('input[name="project_number"]').should('have.attr', 'placeholder', 'Bijv. 2024-001')
    
    // Both should have the same placeholder format
    cy.log('✅ Project number format is consistent between declarations and travel records')
  })

  it('should verify database integration works', () => {
    cy.visit('/declarations')
    
    // The fact that the page loads without errors means:
    // 1. Database column was added successfully
    // 2. Model queries work correctly
    // 3. Views render without SQL errors
    
    cy.get('h1').should('contain.text', 'Mijn Declaraties')
    cy.get('input[name="project_number"]').should('be.visible')
    cy.get('table thead th').should('contain.text', 'Projectnummer')
    
    // No database errors should be visible
    cy.get('body').should('not.contain.text', 'SQLSTATE')
    cy.get('body').should('not.contain.text', 'Unknown column')
    cy.get('body').should('not.contain.text', 'Fatal error')
    
    cy.log('✅ Database integration works correctly - no SQL errors')
  })

  it('should verify complete feature implementation', () => {
    // Test all key components are working together
    
    // 1. Index page with filter and table
    cy.visit('/declarations')
    cy.get('input[name="project_number"]').should('be.visible')
    cy.get('table thead th').should('contain.text', 'Projectnummer')
    
    // 2. Create form
    cy.visit('/declarations/create')
    cy.get('input[name="project_number"]').should('be.visible')
    cy.get('input[name="project_number"]').should('have.attr', 'required')
    
    // 3. Filter functionality
    cy.visit('/declarations')
    cy.get('input[name="project_number"]').type('TEST')
    cy.get('button:contains("Filter toepassen")').click()
    cy.url().should('include', 'project_number=TEST')
    
    cy.log('✅ Complete project number feature is implemented and working')
  })
})
