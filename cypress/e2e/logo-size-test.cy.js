describe('Logo Size Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should display larger logo with compact header', () => {
    cy.visit('/')
    
    // Check that logo exists and is visible
    cy.get('header img[src="/images/logo.svg"]').should('be.visible')
    
    // Check that logo has the larger size class (h-16)
    cy.get('header img[src="/images/logo.svg"]').should('have.class', 'h-16')
    
    // Verify logo is not the old smaller size (h-10)
    cy.get('header img[src="/images/logo.svg"]').should('not.have.class', 'h-10')
    
    // Check that logo has proper margin (mr-3)
    cy.get('header img[src="/images/logo.svg"]').should('have.class', 'mr-3')
    
    cy.log('✅ Logo has correct larger size (h-16)')
  })

  it('should have compact header with minimal padding', () => {
    cy.visit('/')
    
    // Check that header container has compact padding (py-2)
    cy.get('header > div').should('have.class', 'py-2')
    
    // Verify header doesn't have excessive padding
    cy.get('header > div').should('not.have.class', 'py-4')
    cy.get('header > div').should('not.have.class', 'py-6')
    cy.get('header > div').should('not.have.class', 'py-8')
    
    cy.log('✅ Header has compact padding (py-2)')
  })

  it('should verify logo is clickable and navigates to home', () => {
    cy.visit('/declarations')
    
    // Click on logo to navigate home
    cy.get('header a[href="/"] img').click()
    
    // Should navigate to home page
    cy.url().should('match', /\/$|\/index$/)
    
    cy.log('✅ Logo navigation works correctly')
  })

  it('should check logo proportions and visibility', () => {
    cy.visit('/')
    
    // Get logo element and check its computed height
    cy.get('header img[src="/images/logo.svg"]').then(($img) => {
      const img = $img[0]
      const computedStyle = window.getComputedStyle(img)
      const height = computedStyle.height
      
      // h-16 should be 64px (4rem)
      expect(height).to.equal('64px')
      
      cy.log(`Logo height: ${height}`)
    })
    
    // Check that logo loads successfully
    cy.get('header img[src="/images/logo.svg"]')
      .should('be.visible')
      .and(($img) => {
        expect($img[0].naturalHeight).to.be.greaterThan(0)
      })
    
    cy.log('✅ Logo has correct dimensions and loads properly')
  })

  it('should verify header layout remains balanced', () => {
    cy.visit('/')
    
    // Check that header has proper flex layout
    cy.get('header > div').should('have.class', 'flex')
    cy.get('header > div').should('have.class', 'justify-between')
    cy.get('header > div').should('have.class', 'items-center')
    
    // Check that logo area and navigation area are both visible
    cy.get('header h1').should('be.visible')
    cy.get('header div').contains('Welkom,').should('be.visible')
    
    // Verify navigation links are still accessible
    const expectedLinks = ['Declaraties', 'Reiskosten', 'Assets', 'Mijn Profiel', 'Uitloggen']
    expectedLinks.forEach(linkText => {
      cy.get('header').should('contain.text', linkText)
    })
    
    cy.log('✅ Header layout remains balanced with larger logo')
  })

  it('should test logo on different pages', () => {
    const pages = ['/', '/declarations', '/travel_records', '/assets', '/admin']
    
    pages.forEach(page => {
      cy.visit(page)
      
      // Check logo is consistently sized across all pages
      cy.get('header img[src="/images/logo.svg"]').should('be.visible')
      cy.get('header img[src="/images/logo.svg"]').should('have.class', 'h-16')
      
      // Check header padding is consistent
      cy.get('header > div').should('have.class', 'py-2')
      
      cy.log(`✅ Logo and header consistent on ${page}`)
    })
  })

  it('should verify logo alt text and accessibility', () => {
    cy.visit('/')
    
    // Check that logo has proper alt text
    cy.get('header img[src="/images/logo.svg"]')
      .should('have.attr', 'alt')
      .and('not.be.empty')
    
    // Check that logo is within a clickable link
    cy.get('header a[href="/"] img').should('exist')
    
    // Verify the link is keyboard accessible
    cy.get('header a[href="/"]').should('be.visible')
    
    cy.log('✅ Logo has proper accessibility attributes')
  })

  it('should compare logo size visually', () => {
    cy.visit('/')
    
    // Take a screenshot to visually verify the logo size
    cy.get('header').screenshot('header-with-larger-logo')
    
    // Check that logo takes up appropriate space in header
    cy.get('header img[src="/images/logo.svg"]').then(($logo) => {
      const logoHeight = $logo.height()
      const logoWidth = $logo.width()
      
      // Logo should have reasonable dimensions (not too small, not too large)
      expect(logoHeight).to.be.greaterThan(50) // Should be larger than before
      expect(logoHeight).to.be.lessThan(100)   // But not excessively large
      expect(logoWidth).to.be.greaterThan(100) // Should have reasonable width
      
      cy.log(`Logo dimensions: ${logoWidth}x${logoHeight}px`)
    })
    
    cy.log('✅ Logo size is visually appropriate')
  })
})
