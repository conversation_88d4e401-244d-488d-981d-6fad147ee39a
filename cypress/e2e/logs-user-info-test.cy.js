describe('Logs User Info Test', () => {
  beforeEach(() => {
    cy.fixture('users').as('users')
    cy.get('@users').then(users => {
      cy.login(users.admin.username, users.admin.password)
    })
  })

  it('should display user information in logs', () => {
    cy.visit('/logs')
    
    // Check that logs page loads
    cy.get('h1').should('contain.text', 'Logviewer')
    
    // Check that user column exists
    cy.get('table th').should('contain.text', 'Gebruiker')
    
    // Check that logs table has 4 columns (Tijd, Level, Gebruiker, Bericht)
    cy.get('table thead tr th').should('have.length', 4)
    
    cy.log('✅ Logs page has user column')
  })

  it('should generate logs with user info by performing actions', () => {
    // First, create a declaration to generate logs
    cy.visit('/declarations/create')
    
    // Fill in form
    cy.get('#titel').type('Test Log Declaration')
    cy.get('#totaal_bedrag').type('50.00')
    cy.get('#bedrag_excl_btw').type('41.32')
    cy.get('#btw_percentage').select('21')
    cy.get('#product_dienst').type('Test voor logs')
    
    // Add file
    cy.fixture('test-image.png', 'base64').then(fileContent => {
      cy.get('#bon_factuur').selectFile({
        contents: Cypress.Buffer.from(fileContent, 'base64'),
        fileName: 'log-test.png',
        mimeType: 'image/png'
      })
    })
    
    // Submit form
    cy.get('form').submit()
    
    // Should redirect to declarations list
    cy.url().should('include', '/declarations')
    
    // Now check logs
    cy.visit('/logs')
    
    // Look for recent log entries
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        cy.log(`Found ${$rows.length} log entries`)
        
        // Check if any logs show user information
        cy.get('table tbody tr').each(($row) => {
          const rowText = $row.text()
          
          // Look for user information in the user column
          const userCell = $row.find('td').eq(2) // Third column (Gebruiker)
          const userCellText = userCell.text()
          
          if (userCellText && !userCellText.includes('Systeem')) {
            cy.log('Found log with user info: ' + userCellText)
            
            // Should not be empty or "Onbekend"
            expect(userCellText).to.not.be.empty
            expect(userCellText).to.not.include('Onbekend')
            expect(userCellText).to.not.include('null')
            expect(userCellText).to.not.include('undefined')
          }
        })
      } else {
        cy.log('No log entries found')
      }
    })
    
    cy.log('✅ User information is displayed in logs')
  })

  it('should test log filtering and search', () => {
    cy.visit('/logs')
    
    // Test search functionality
    cy.get('#search').type('declaratie')
    cy.get('button[type="submit"]').click()
    
    // Should filter logs
    cy.url().should('include', 'search=declaratie')
    
    // Test level filtering
    cy.get('#level').select('info')
    cy.get('button[type="submit"]').click()
    
    // Should filter by level
    cy.url().should('include', 'level=info')
    
    cy.log('✅ Log filtering works')
  })

  it('should test log generation with asset management', () => {
    cy.visit('/admin/assets/manage')
    
    // Search for an asset
    cy.get('#uid').type('HDH16234')
    cy.get('#searchAsset').click()
    
    cy.wait(2000)
    
    // Check if asset is found and has actions
    cy.get('body').then(($body) => {
      if ($body.text().includes('Asset gevonden')) {
        cy.log('Asset found, checking for assign/unassign actions')
        
        // If there are assign/unassign buttons, this will generate logs
        if ($body.find('button:contains("Toewijzen")').length > 0) {
          cy.log('Asset can be assigned (will generate logs)')
        }
        if ($body.find('button:contains("Innemen")').length > 0) {
          cy.log('Asset can be unassigned (will generate logs)')
        }
      }
    })
    
    // Check logs for asset-related entries
    cy.visit('/logs')
    
    // Search for asset-related logs
    cy.get('#search').clear().type('asset')
    cy.get('button[type="submit"]').click()
    
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        cy.log('Found asset-related logs')
        
        // Check that asset logs show user information
        cy.get('table tbody tr').each(($row) => {
          const messageCell = $row.find('td').eq(3) // Fourth column (Bericht)
          const messageCellText = messageCell.text().toLowerCase()
          
          if (messageCellText.includes('asset')) {
            const userCell = $row.find('td').eq(2) // Third column (Gebruiker)
            const userCellText = userCell.text()
            
            cy.log('Asset log entry found with user: ' + userCellText)
            
            // Should show user info for asset operations
            if (!userCellText.includes('Systeem')) {
              expect(userCellText).to.not.be.empty
            }
          }
        })
      }
    })
    
    cy.log('✅ Asset management logs show user information')
  })

  it('should verify log parsing works correctly', () => {
    cy.visit('/logs')
    
    // Generate a test log entry
    cy.get('#test-log-btn').click()
    
    // Fill in test log form (if modal appears)
    cy.get('body').then(($body) => {
      if ($body.find('#test-log-modal').length > 0) {
        cy.get('#test-message').type('Test log entry for user parsing')
        cy.get('#test-level').select('info')
        cy.get('#submit-test-log').click()
      }
    })
    
    cy.wait(1000)
    
    // Refresh to see new log
    cy.reload()
    
    // Check that the test log appears with user information
    cy.get('table tbody tr').first().then(($row) => {
      const userCell = $row.find('td').eq(2) // Third column (Gebruiker)
      const messageCell = $row.find('td').eq(3) // Fourth column (Bericht)
      
      const userText = userCell.text()
      const messageText = messageCell.text()
      
      cy.log('Latest log entry:')
      cy.log('User: ' + userText)
      cy.log('Message: ' + messageText)
      
      // Should show user information
      if (!userText.includes('Systeem')) {
        expect(userText).to.not.be.empty
        expect(userText).to.not.include('Onbekend')
      }
    })
    
    cy.log('✅ Log parsing works correctly')
  })

  it('should verify logs show clean messages without user prefixes', () => {
    cy.visit('/logs')
    
    // Check that messages don't contain user prefixes in the message column
    cy.get('table tbody tr').then(($rows) => {
      if ($rows.length > 0) {
        cy.get('table tbody tr').each(($row) => {
          const messageCell = $row.find('td').eq(3) // Fourth column (Bericht)
          const messageText = messageCell.text()
          
          // Message should not contain user prefixes like [User 1:admin]:
          expect(messageText).to.not.match(/\[User \d+:[^\]]+\]:/)
          expect(messageText).to.not.match(/User \d+:[^\s]+/)
          
          // But user info should be in the separate user column
          const userCell = $row.find('td').eq(2) // Third column (Gebruiker)
          const userText = userCell.text()
          
          if (messageText.includes('Declaratie') || messageText.includes('Asset')) {
            // These should have user information
            if (!userText.includes('Systeem')) {
              expect(userText).to.not.be.empty
            }
          }
        })
      }
    })
    
    cy.log('✅ Messages are clean and user info is properly separated')
  })
})
