<?php
/**
 * Test Runner voor de Ecosprong applicatie
 * 
 * Dit script voert alle beschikbare tests uit
 */

// Controleer of we in CLI mode zijn
if (php_sapi_name() !== 'cli') {
    die("Dit script kan alleen worden uitgevoerd via de command line.\n");
}

echo "Ecosprong Application Test Runner\n";
echo "=================================\n\n";

// Controleer of alle benodigde bestanden bestaan
$requiredFiles = [
    'bootstrap.php',
    'tests/BasicTests.php'
];

foreach ($requiredFiles as $file) {
    if (!file_exists($file)) {
        die("Vereist bestand niet gevonden: $file\n");
    }
}

// Laad en voer de tests uit
try {
    require_once 'tests/BasicTests.php';
    
    $tests = new BasicTests();
    $tests->runAllTests();
    
} catch (Exception $e) {
    echo "Fout bij het uitvoeren van tests: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\nTest run completed.\n";
