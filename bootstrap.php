<?php
/**
 * Bootstrap bestand
 *
 * Dit bestand laadt alle benodigde bestanden en initialiseert de applicatie.
 */

// Start de sessie
session_start();

// Laad de modellen
require_once 'models/Config.php';
require_once 'models/Database.php';
require_once 'models/Model.php';
require_once 'models/User.php';
require_once 'models/Declaration.php';
require_once 'models/TravelRecord.php';
require_once 'models/Auth.php';
require_once 'models/View.php';
require_once 'models/Router.php';
require_once 'models/GoogleMapsService.php';
require_once 'models/LogModel.php';
require_once 'models/Asset.php';
require_once 'models/AssetLog.php';
require_once 'models/AssetType.php';
require_once 'models/Url.php';
require_once 'models/UrlHelper.php';

// Laad de controllers
require_once 'controllers/Controller.php';
require_once 'controllers/HomeController.php';
require_once 'controllers/AuthController.php';
require_once 'controllers/DeclarationController.php';
require_once 'controllers/TravelRecordController.php';
require_once 'controllers/AdminController.php';
require_once 'controllers/AdminDeclarationController.php';
require_once 'controllers/AdminTravelRecordController.php';
require_once 'controllers/ApiController.php';
require_once 'controllers/LogController.php';
require_once 'controllers/UserController.php';
require_once 'controllers/AssetController.php';
require_once 'controllers/AdminAssetController.php';
require_once 'controllers/AdminAssetTypeController.php';
require_once 'controllers/ApiAssetController.php';
require_once 'controllers/UploadController.php';

// Stel de tijdzone in
date_default_timezone_set(Config::get('app.timezone', 'Europe/Amsterdam'));

// Stel de locale in
setlocale(LC_ALL, Config::get('app.locale', 'nl_NL') . '.utf8');

// Stel error reporting in
if (Config::get('app.debug', false)) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Functie om een waarde te debuggen
function dd($value)
{
    echo '<pre>';
    var_dump($value);
    echo '</pre>';
    die();
}
