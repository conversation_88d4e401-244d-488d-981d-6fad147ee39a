<?php
/**
 * Database Migratie Script voor Online Omgeving
 * 
 * Dit script past de online database aan zodat deze compatibel is
 * met de nieuwe code structuur.
 */

// Alleen toegankelijk voor admins
session_start();
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    die('Toegang geweigerd. Alleen admins kunnen dit script uitvoeren.');
}

require_once '../config/database.php';

$config = require '../config/database.php';

// Extract database configuration
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];
$charset = $config['charset'];
$options = $config['options'];

// Create DSN
$dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";

?>
<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Migratie - Online Omgeving</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .btn { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background-color: #0056b3; }
        .btn-danger { background-color: #dc3545; }
        .btn-danger:hover { background-color: #c82333; }
        .btn-success { background-color: #28a745; }
        .btn-success:hover { background-color: #218838; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .log { background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Database Migratie - Online Omgeving</h1>
        
        <div class="warning">
            <strong>⚠️ WAARSCHUWING:</strong> Dit script past de database structuur aan voor compatibiliteit met de nieuwe code.
            <br><strong>Maak eerst een backup van de database!</strong>
        </div>

        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
            try {
                $pdo = new PDO($dsn, $username, $password, $options);
                
                if ($_POST['action'] === 'migrate') {
                    echo "<h2>🚀 Migratie wordt uitgevoerd...</h2>";
                    
                    // Lees het migratie script
                    $migrationScript = file_get_contents('../sql/migrate_online_database.sql');
                    
                    if ($migrationScript === false) {
                        throw new Exception('Kan migratie script niet lezen');
                    }
                    
                    // Split het script in individuele statements
                    $statements = explode(';', $migrationScript);
                    $log = [];
                    $successCount = 0;
                    $errorCount = 0;
                    
                    foreach ($statements as $statement) {
                        $statement = trim($statement);
                        if (empty($statement) || strpos($statement, '--') === 0) {
                            continue;
                        }
                        
                        try {
                            $pdo->exec($statement);
                            $log[] = "✅ SUCCESS: " . substr($statement, 0, 100) . "...";
                            $successCount++;
                        } catch (PDOException $e) {
                            // Sommige statements mogen falen (bijv. als kolom al bestaat)
                            if (strpos($e->getMessage(), 'Duplicate column name') !== false ||
                                strpos($e->getMessage(), 'already exists') !== false ||
                                strpos($e->getMessage(), 'IF NOT EXISTS') !== false) {
                                $log[] = "ℹ️ SKIPPED: " . substr($statement, 0, 100) . "... (already exists)";
                            } else {
                                $log[] = "❌ ERROR: " . $e->getMessage() . " - " . substr($statement, 0, 100) . "...";
                                $errorCount++;
                            }
                        }
                    }
                    
                    echo "<div class='success'>";
                    echo "<h3>✅ Migratie voltooid!</h3>";
                    echo "<p><strong>Succesvol:</strong> $successCount statements</p>";
                    echo "<p><strong>Fouten:</strong> $errorCount statements</p>";
                    echo "</div>";
                    
                    echo "<h3>📋 Migratie Log:</h3>";
                    echo "<div class='log'>" . implode("\n", $log) . "</div>";
                    
                    // Toon database structuur na migratie
                    echo "<h3>📊 Database Structuur na Migratie:</h3>";
                    
                    $tables = ['assets', 'declaraties', 'travel_records', 'users', 'asset_types'];
                    foreach ($tables as $table) {
                        try {
                            $stmt = $pdo->query("DESCRIBE $table");
                            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            
                            echo "<h4>Tabel: $table</h4>";
                            echo "<table>";
                            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
                            foreach ($columns as $column) {
                                echo "<tr>";
                                echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
                                echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
                                echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
                                echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
                                echo "<td>" . htmlspecialchars($column['Default'] ?? '') . "</td>";
                                echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
                                echo "</tr>";
                            }
                            echo "</table>";
                        } catch (PDOException $e) {
                            echo "<p>Tabel $table niet gevonden of fout: " . htmlspecialchars($e->getMessage()) . "</p>";
                        }
                    }
                    
                } elseif ($_POST['action'] === 'check') {
                    echo "<h2>🔍 Database Status Check</h2>";
                    
                    // Check huidige database structuur
                    $checks = [
                        'assets.type_id' => "SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'assets' AND COLUMN_NAME = 'type_id'",
                        'assets.description' => "SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'assets' AND COLUMN_NAME = 'description'",
                        'declaraties.project_number' => "SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'declaraties' AND COLUMN_NAME = 'project_number'",
                        'declaraties.btw_percentage' => "SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'declaraties' AND COLUMN_NAME = 'btw_percentage'",
                        'declaraties.bestand_pad' => "SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'declaraties' AND COLUMN_NAME = 'bestand_pad'",
                        'users.default_address' => "SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'users' AND COLUMN_NAME = 'default_address'",
                        'asset_types table' => "SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'asset_types'"
                    ];
                    
                    echo "<table>";
                    echo "<tr><th>Check</th><th>Status</th><th>Actie Nodig</th></tr>";
                    
                    foreach ($checks as $checkName => $query) {
                        try {
                            $stmt = $pdo->query($query);
                            $exists = $stmt->fetchColumn() > 0;
                            
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($checkName) . "</td>";
                            echo "<td>" . ($exists ? "✅ Bestaat" : "❌ Ontbreekt") . "</td>";
                            echo "<td>" . ($exists ? "Geen" : "Migratie nodig") . "</td>";
                            echo "</tr>";
                        } catch (PDOException $e) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($checkName) . "</td>";
                            echo "<td>❌ Fout: " . htmlspecialchars($e->getMessage()) . "</td>";
                            echo "<td>Onderzoek nodig</td>";
                            echo "</tr>";
                        }
                    }
                    echo "</table>";
                }
                
            } catch (PDOException $e) {
                echo "<div class='error'>";
                echo "<h3>❌ Database Fout:</h3>";
                echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div class='error'>";
                echo "<h3>❌ Fout:</h3>";
                echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
        }
        ?>

        <div class="info">
            <h3>📋 Wat doet deze migratie?</h3>
            <ul>
                <li>✅ Voegt <code>project_number</code> kolom toe aan declaraties</li>
                <li>✅ Voegt <code>btw_percentage</code> kolom toe aan declaraties</li>
                <li>✅ Voegt <code>bestand_pad</code> kolom toe aan declaraties</li>
                <li>✅ Voegt <code>type_id</code> kolom toe aan assets (vervangt type)</li>
                <li>✅ Maakt <code>asset_types</code> tabel aan</li>
                <li>✅ Voegt <code>default_address</code> kolom toe aan users</li>
                <li>✅ Voegt timestamp kolommen toe waar nodig</li>
                <li>✅ Voegt foreign keys en indexes toe</li>
                <li>✅ Wijzigt visibility van tinyint naar varchar</li>
            </ul>
        </div>

        <form method="POST" style="margin: 20px 0;">
            <button type="submit" name="action" value="check" class="btn">
                🔍 Check Database Status
            </button>
            
            <button type="submit" name="action" value="migrate" class="btn btn-success" 
                    onclick="return confirm('Weet je zeker dat je de migratie wilt uitvoeren? Maak eerst een backup!')">
                🚀 Voer Migratie Uit
            </button>
        </form>

        <div class="warning">
            <h3>🔄 Rollback Instructies:</h3>
            <p>Als er problemen zijn na de migratie, kun je het rollback script uitvoeren:</p>
            <code>mysql -u username -p database_name &lt; sql/rollback_migration.sql</code>
        </div>

        <p><a href="/">← Terug naar hoofdpagina</a></p>
    </div>
</body>
</html>
