<?php
// Setup script voor projectnummer kolom in declaraties
require_once '../config/database.php';

$config = require '../config/database.php';

// Extract database configuration
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];
$charset = $config['charset'];
$options = $config['options'];

// Create DSN
$dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";

try {
    $pdo = new PDO($dsn, $username, $password, $options);
    
    echo "<h2>Adding Project Number to Declarations</h2>";
    echo "<p>Connected to database: <strong>$dbname</strong></p>";
    
    // Check if project_number column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM declaraties LIKE 'project_number'");
    $columnExists = $stmt->rowCount() > 0;
    
    if ($columnExists) {
        echo "<p>✅ Project number column already exists</p>";
    } else {
        echo "<p>Adding project_number column...</p>";
        
        // Add project_number column
        $sql = "ALTER TABLE declaraties ADD COLUMN project_number VARCHAR(50) NOT NULL DEFAULT '' AFTER user_id";
        $pdo->exec($sql);
        echo "<p>✅ Project number column added successfully</p>";
        
        // Update existing records
        echo "<p>Updating existing records with default project number...</p>";
        $sql = "UPDATE declaraties SET project_number = 'Algemeen' WHERE project_number = ''";
        $pdo->exec($sql);
        echo "<p>✅ Existing records updated</p>";
    }
    
    // Show current structure
    echo "<h3>Current Declarations Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE declaraties");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($columns) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th style='padding: 8px; border: 1px solid #ccc;'>Field</th>";
        echo "<th style='padding: 8px; border: 1px solid #ccc;'>Type</th>";
        echo "<th style='padding: 8px; border: 1px solid #ccc;'>Null</th>";
        echo "<th style='padding: 8px; border: 1px solid #ccc;'>Key</th>";
        echo "<th style='padding: 8px; border: 1px solid #ccc;'>Default</th>";
        echo "<th style='padding: 8px; border: 1px solid #ccc;'>Extra</th>";
        echo "</tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td style='padding: 8px; border: 1px solid #ccc;'>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #ccc;'>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #ccc;'>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #ccc;'>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #ccc;'>" . htmlspecialchars($column['Default'] ?? '') . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #ccc;'>" . htmlspecialchars($column['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>✅ Project Number Setup Complete!</h3>";
    echo "<p><a href='/declarations'>Go to declarations page</a></p>";
    echo "<p><a href='/'>Go to homepage</a></p>";
    
} catch (PDOException $e) {
    echo "<h3>❌ Database error:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
