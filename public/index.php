<?php
/**
 * Front controller
 *
 * Dit bestand is het startpunt van de applicatie.
 */

// Laad het bootstrap bestand
require_once __DIR__ . '/../bootstrap.php';

// Maak een nieuwe router
$router = new Router();

// Definieer de routes
$router->get('', 'HomeController@index');
$router->get('index', 'HomeController@index');
$router->get('login', 'AuthController@showLoginForm');
$router->post('login', 'AuthController@login');
$router->get('logout', 'AuthController@logout');

// Declaratie routes
$router->get('declarations', 'DeclarationController@index');
$router->get('declarations/create', 'DeclarationController@create');
$router->post('declarations/store', 'DeclarationController@store');
$router->get('declarations/edit/:id', 'DeclarationController@edit');
$router->post('declarations/update/:id', 'DeclarationController@update');
$router->get('declarations/delete/:id', 'DeclarationController@destroy');
$router->post('declarations/submit', 'DeclarationController@submit');

// Reiskosten routes
$router->get('travel_records', 'TravelRecordController@index');
$router->get('travel_records/create', 'TravelRecordController@create');
$router->post('travel_records/store', 'TravelRecordController@store');
$router->get('travel_records/edit/:id', 'TravelRecordController@edit');
$router->post('travel_records/update/:id', 'TravelRecordController@update');
$router->get('travel_records/delete/:id', 'TravelRecordController@destroy');
$router->post('travel_records/submit', 'TravelRecordController@submit');

// Admin routes
$router->get('admin', 'AdminController@index');
$router->get('admin/users', 'AdminController@users');
$router->get('admin/users/create', 'AdminController@createUser');
$router->post('admin/users/store', 'AdminController@storeUser');
$router->get('admin/users/edit/:id', 'AdminController@editUser');
$router->post('admin/users/update/:id', 'AdminController@updateUser');
$router->get('admin/users/delete/:id', 'AdminController@destroyUser');

// Admin declaratie routes
$router->get('admin/declarations', 'AdminDeclarationController@index');
$router->get('admin/declarations/user/:id', 'AdminDeclarationController@show');
$router->post('admin/declarations/update-status/:id', 'AdminDeclarationController@updateStatus');

// Upload file routes
$router->get('uploads/declaraties/:filename', 'UploadController@serveDeclarationFile');
$router->get('admin/uploads/declaraties/:filename', 'UploadController@serveDeclarationFile');

// Admin reiskosten routes
$router->get('admin/travel_records', 'AdminTravelRecordController@index');
$router->get('admin/travel_records/user/:id', 'AdminTravelRecordController@show');
$router->post('admin/travel_records/update-status/:id', 'AdminTravelRecordController@updateStatus');

// API routes
$router->post('api/calculate-distance', 'ApiController@calculateDistance');
$router->post('api/calculate-total-distance', 'ApiController@calculateTotalDistance');
$router->get('api/assets/find-by-uid', 'ApiAssetController@findByUID');

// User routes
$router->get('user/profile', 'UserController@profile');
$router->post('user/update-password', 'UserController@updatePassword');
$router->post('user/update-default-address', 'UserController@updateDefaultAddress');

// Asset routes
$router->get('assets', 'AssetController@index');
$router->get('assets/show/:id', 'AssetController@show');
$router->get('assets/history/:id', 'AssetController@history');
$router->post('assets/assign', 'AssetController@assign');

// Admin asset routes
$router->get('admin/assets', 'AdminController@assets');
$router->get('admin/assets/create', 'AdminController@createAsset');
$router->post('admin/assets/store', 'AdminController@storeAsset');
$router->get('admin/assets/edit/:id', 'AdminController@editAsset');
$router->post('admin/assets/update/:id', 'AdminController@updateAsset');
$router->post('admin/assets/destroy/:id', 'AdminController@deleteAsset');
$router->get('admin/assets/manage', 'AdminController@manageAssets');
$router->post('admin/assets/assign', 'AdminController@assignAsset');
$router->post('admin/assets/unassign', 'AdminController@unassignAsset');
$router->get('admin/assets/manage', 'AdminAssetController@manage');
$router->post('admin/assets/assign-to-user', 'AdminAssetController@assignToUser');
$router->post('admin/assets/unassign-from-user', 'AdminAssetController@unassignFromUser');

// Admin asset type routes
$router->get('admin/asset_types', 'AdminAssetTypeController@index');
$router->get('admin/asset_types/create', 'AdminAssetTypeController@create');
$router->post('admin/asset_types/store', 'AdminAssetTypeController@store');
$router->get('admin/asset_types/edit/:id', 'AdminAssetTypeController@edit');
$router->post('admin/asset_types/update/:id', 'AdminAssetTypeController@update');
$router->post('admin/asset_types/destroy/:id', 'AdminAssetTypeController@destroy');

// Log routes
$router->get('logs', 'LogController@index');
$router->post('logs/clear', 'LogController@clear');
$router->post('logs/test', 'LogController@test');

// Notification routes (admin only)
$router->get('notifications', 'NotificationController@index');
$router->get('notifications/getUnread', 'NotificationController@getUnread');
$router->post('notifications/markAsRead', 'NotificationController@markAsRead');
$router->post('notifications/markAllAsRead', 'NotificationController@markAllAsRead');
$router->post('notifications/cleanup', 'NotificationController@cleanup');

// Asset assignment routes
$router->post('assets/assignToMe', 'AssetController@assignToMe');

// Dispatch de route
try {
    echo $router->dispatch();
} catch (Exception $e) {
    // Toon een 404-pagina
    http_response_code(404);
    echo View::render('errors/404', [
        'message' => $e->getMessage()
    ]);
}
