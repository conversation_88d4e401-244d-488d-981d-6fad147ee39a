<?php
session_start();
require __DIR__ . '/../../includes/db.php';
require __DIR__ . '/../../includes/functions.php';
require __DIR__ . '/../../includes/LogController.php';

$pageTitle = "Asset geschiedenis";

// Controleer login
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

$logController = new LogController($pdo);
$assetId = $_GET['asset_id'] ?? null;

// Als asset_id niet is meegegeven, terug naar overzicht
if (!$assetId) {
    header("Location: view_assets.php");
    exit();
}

// 1) Haal de asset op, om naam te kunnen tonen
$asset = getAssetById($pdo, $assetId);
// Voor de logs
$logs = $logController->getLogsByAssetId($assetId);

include '../../templates/header.php';
?>

<div class="max-w-3xl mx-auto p-4 mt-6">
    <div class="bg-white shadow rounded p-6">
        <!-- 
          h1: Toon zowel ID als Naam
          Let op: als $asset null kan zijn, geef fallback mee 
        -->
        <h1 class="text-2xl font-semibold mb-6">
            Loggeschiedenis voor Asset ID: 
            <?php echo htmlspecialchars($assetId ?? '', ENT_QUOTES, 'UTF-8'); ?>
            <?php if ($asset && !empty($asset['name'])): ?>
                (<?php echo htmlspecialchars($asset['name'], ENT_QUOTES, 'UTF-8'); ?>)
            <?php endif; ?>
        </h1>

        <div class="overflow-x-auto">
            <table class="min-w-full border-collapse divide-y divide-gray-200 text-sm">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-left font-medium text-gray-700">Gebruiker</th>
                        <th class="px-4 py-2 text-left font-medium text-gray-700">Startdatum</th>
                        <th class="px-4 py-2 text-left font-medium text-gray-700">Einddatum</th>
                        <th class="px-4 py-2 text-left font-medium text-gray-700">Opmerkingen</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-100">
                    <?php if (!empty($logs)): ?>
                        <?php foreach ($logs as $log): ?>
                            <tr class="whitespace-nowrap">
                                <td class="px-4 py-2 text-gray-700">
                                    <?php echo htmlspecialchars($log['username'] ?? 'Onbekend', ENT_QUOTES, 'UTF-8'); ?>
                                </td>
                                <td class="px-4 py-2 text-gray-700">
                                    <?php echo htmlspecialchars($log['start_date'] ?? '', ENT_QUOTES, 'UTF-8'); ?>
                                </td>
                                <td class="px-4 py-2 text-gray-700">
                                    <?php echo htmlspecialchars($log['end_date'] ?? '', ENT_QUOTES, 'UTF-8'); ?>
                                </td>
                                <td class="px-4 py-2 text-gray-700">
                                    <?php echo nl2br(htmlspecialchars($log['comments'] ?? '', ENT_QUOTES, 'UTF-8')); ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="4" class="px-4 py-2 text-center text-gray-500">
                                Geen log-items gevonden.
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div> <!-- /overflow-x-auto -->
    </div> <!-- /bg-white shadow rounded p-6 -->
</div> <!-- /max-w-3xl mx-auto p-4 mt-6 -->

<?php include '../../templates/footer.php'; ?>
