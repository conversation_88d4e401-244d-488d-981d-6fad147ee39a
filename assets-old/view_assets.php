<?php
session_start();
require __DIR__ . '/../../includes/db.php';
require __DIR__ . '/../../includes/functions.php';
require __DIR__ . '/../../includes/LogController.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

$pageTitle = "Asset overzicht";
$userId = $_SESSION['user_id'];

/**
 * Deze functie moet in je functions.php een JOIN uitvoeren om
 * a.current_holder_name op te halen. Zorg dat dit in de DB en function is geregeld.
 */
$assets = getAllAssets($pdo);

// POST: wijs een asset toe aan deze gebruiker
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['asset_id'])) {
    assignAssetToUser($pdo, $_POST['asset_id'], $userId);
    header("Location: view_assets.php");
    exit();
}
?>

<?php include '../../templates/header.php'; ?>

<!-- Buitenste container die de content centreert -->
<div class="p-4 mt-6 flex justify-center">
    <!-- 
      'inline-block': container is zo breed als de inhoud (hier de tabel) 
      bg-white + shadow + rounded p-6: standaard card-look
    -->
    <div class="inline-block bg-white shadow rounded p-6">
        <h1 class="text-2xl font-semibold mb-4">Alle Assets</h1>

        <!-- 
          Tabel zonder vaste breedte: w-auto, 
          zodat de container en de tabel even breed zijn.
        -->
        <table class="table-auto border-collapse w-auto">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-4 py-2 text-left align-middle font-semibold text-gray-700">UID</th>
                    <th class="px-4 py-2 text-left align-middle font-semibold text-gray-700">Naam</th>
                    <th class="px-4 py-2 text-left align-middle font-semibold text-gray-700">Merk</th>
                    <th class="px-4 py-2 text-left align-middle font-semibold text-gray-700">Model</th>
                    <th class="px-4 py-2 text-left align-middle font-semibold text-gray-700">Type</th>
                    <th class="px-4 py-2 text-left align-middle font-semibold text-gray-700">Aankoopdatum</th>
                    <th class="px-4 py-2 text-left align-middle font-semibold text-gray-700">Serienummer</th>
                    <th class="px-4 py-2 text-left align-middle font-semibold text-gray-700">Status</th>
                    <th class="px-4 py-2 text-left align-middle font-semibold text-gray-700">Aanschafwaarde</th>
                    <th class="px-4 py-2 text-left align-middle font-semibold text-gray-700">Aantal</th>
                    <th class="px-4 py-2 text-left align-middle font-semibold text-gray-700">Huidige houder</th>
                    <th class="px-4 py-2 text-left align-middle font-semibold text-gray-700">Houder sinds</th>
                    <th class="px-4 py-2 text-left align-middle font-semibold text-gray-700">Acties</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($assets as $asset): ?>
                    <tr class="border-b">
                        <td class="px-4 py-2 text-left align-middle"><?php echo htmlspecialchars($asset['uid'] ?? ''); ?></td>
                        <td class="px-4 py-2 text-left align-middle"><?php echo htmlspecialchars($asset['name'] ?? ''); ?></td>
                        <td class="px-4 py-2 text-left align-middle"><?php echo htmlspecialchars($asset['brand'] ?? ''); ?></td>
                        <td class="px-4 py-2 text-left align-middle"><?php echo htmlspecialchars($asset['model'] ?? ''); ?></td>
                        <td class="px-4 py-2 text-left align-middle"><?php echo htmlspecialchars($asset['type'] ?? ''); ?></td>
                        <td class="px-4 py-2 text-left align-middle"><?php echo htmlspecialchars($asset['purchase_date'] ?? ''); ?></td>
                        <td class="px-4 py-2 text-left align-middle"><?php echo htmlspecialchars($asset['serial_number'] ?? ''); ?></td>
                        <td class="px-4 py-2 text-left align-middle"><?php echo htmlspecialchars($asset['status'] ?? ''); ?></td>
                        <td class="px-4 py-2 text-left align-middle"><?php echo htmlspecialchars($asset['purchase_value'] ?? ''); ?></td>
                        <td class="px-4 py-2 text-left align-middle"><?php echo htmlspecialchars($asset['quantity'] ?? ''); ?></td>

                        <!-- Let op: in functions.php hebben we 'SELECT a.*, u.username AS current_holder_name' -->
                        <td class="px-4 py-2 text-left align-middle">
                            <?php echo htmlspecialchars($asset['current_holder_name'] ?? ''); ?>
                        </td>

                        <td class="px-4 py-2 text-left align-middle">
                            <?php echo htmlspecialchars($asset['holder_since'] ?? ''); ?>
                        </td>

                        <td class="px-4 py-2 text-left align-middle space-x-2">
                            <a 
                                href="history.php?asset_id=<?php echo $asset['id']; ?>" 
                                class="text-blue-600 hover:text-blue-800 underline"
                            >
                                Bekijk geschiedenis
                            </a>
                            <a 
                                href="edit_asset.php?id=<?php echo $asset['id']; ?>" 
                                class="text-indigo-600 hover:text-indigo-800 underline"
                            >
                                Bewerken
                            </a>

                            <!-- **Verwijderen** knop -->
                            <form 
                                method="POST" 
                                action="delete_asset.php" 
                                class="inline-block ml-2"
                                onsubmit="return confirm('Weet je zeker dat je het asset “<?php echo addslashes(htmlspecialchars($asset['name'])); ?>” wilt verwijderen?');"
                            >
                                <input type="hidden" name="asset_id" value="<?php echo $asset['id']; ?>">
                                <button 
                                    type="submit"
                                    class="text-red-600 hover:text-red-800 underline text-xs"
                                >
                                    Verwijderen
                                </button>
                            </form>

                            <form 
                                method="POST" 
                                class="inline-block ml-2"
                                onsubmit="return confirm('Weet je zeker dat je dit asset aan jezelf wilt toewijzen?');"
                            >
                                <input type="hidden" name="asset_id" value="<?php echo $asset['id']; ?>">
                                <button 
                                    type="submit"
                                    class="bg-green-600 text-white text-xs px-3 py-1 rounded hover:bg-green-700"
                                >
                                    Claim
                                </button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div><!-- /inline-block bg-white shadow rounded p-6 -->
</div><!-- /flex justify-center p-4 mt-6 -->

<?php include '../../templates/footer.php'; ?>
