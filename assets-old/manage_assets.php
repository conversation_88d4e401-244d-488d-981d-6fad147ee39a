<?php
session_start();
require __DIR__ . '/../../includes/db.php';
require __DIR__ . '/../../includes/functions.php';
require __DIR__ . '/../../includes/LogController.php';


// Controleer of de gebruiker een admin is (optioneel).
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.php");
    exit();
}

$logController = new LogController($pdo);

/**
 * Verwerk het formulier bij POST
 */
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['assign'])) {
        // Koppelen (uitgifte)
        $assetId  = $_POST['asset_id'];
        $userId   = $_POST['user_id'];
        $comments = $_POST['comments'] ?? '';

        // 1) Update de assets-tabel: zet 'current_holder', 'holder_since' etc.
        assignAssetToUser($pdo, $assetId, $userId);

        // 2) Voeg de log-entry toe in 'asset_logs'
        $logController->addLog($assetId, $userId, $comments);

    } elseif (isset($_POST['unassign'])) {
        // Ontkoppelen (inname)
        $assetId = $_POST['asset_id'];

        // 1) Update 'assets' om 'current_holder' te resetten
        unassignAssetFromUser($pdo, $assetId);

        // 2) Eventueel log afsluiten of nieuwe log-entry. 
        //    Hier laten we het simpel: je zou addLog(..., 'ingeleverd') kunnen doen, 
        //    of updateLogEndDate(...) afhankelijk van je flow.
    }
    header("Location: manage_assets.php");
    exit();
}

// Haal data voor de dropdown-lijsten (users, etc.)
$allAssets = getAllAssets($pdo);
$allUsers  = getAllUsers($pdo);

include '../../templates/header.php';
?>

<!-- Container, net als elders -->
<div class="max-w-3xl mx-auto p-4 mt-6">
    <div class="bg-white shadow rounded p-6">
        <h1 class="text-2xl font-bold mb-4">In- en uitgifte Assets</h1>

        <!-- Zoek op UID -->
        <div class="mb-6">
            <label for="uid" class="block mb-2 text-sm font-medium text-gray-700">
                Unieke code (UID):
            </label>
            <div class="flex space-x-2">
                <input
                    type="text"
                    id="uid"
                    name="uid"
                    autofocus
                    class="flex-1 block w-full rounded-md border border-gray-300 
                           bg-gray-50 text-gray-800 focus:ring-indigo-500 
                           focus:border-indigo-500 sm:text-sm"
                />
                <button
                    id="searchAsset"
                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-white 
                           bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none 
                           focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                    Zoek Asset
                </button>
            </div>
        </div>

        <!-- Asset Details (AJAX-laadbaar) -->
        <div id="assetDetails" class="hidden">
            <h2 class="text-xl font-semibold mb-2">Asset Details</h2>
            <div id="assetInfo" class="mb-4 p-4 bg-white shadow rounded-md"></div>
            <div id="assetActions" class="space-y-6"></div>
        </div>
    </div>
</div>

<?php include '../../templates/footer.php'; ?>

<script>
/**
 * Press Enter => auto "zoek"
 */
document.getElementById('uid').addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        document.getElementById('searchAsset').click();
    }
});

/**
 * Zoekknop => haal get_asset.php op
 */
document.getElementById('searchAsset').addEventListener('click', function() {
    const uid = document.getElementById('uid').value;
    fetch(`../../api/get_asset.php?uid=${uid}`)
        .then(response => response.json())
        .then(data => {
            const assetDetails = document.getElementById('assetDetails');
            const assetInfo    = document.getElementById('assetInfo');
            const assetActions = document.getElementById('assetActions');

            if (data) {
                // Toon asset info
                assetInfo.innerHTML = `
                    <p><strong>Naam:</strong> ${data.name}</p>
                    <p><strong>Merk:</strong> ${data.brand}</p>
                    <p><strong>Model:</strong> ${data.model}</p>
                    <p><strong>Type:</strong> ${data.type}</p>
                    <p><strong>Aankoopdatum:</strong> ${data.purchase_date}</p>
                    <p><strong>Serienummer:</strong> ${data.serial_number}</p>
                    <p><strong>Status:</strong> ${data.status}</p>
                    <p><strong>Aanschafwaarde:</strong> ${data.purchase_value}</p>
                    <p><strong>Aantal:</strong> ${data.quantity}</p>
                    <p><strong>Algemene Opmerking:</strong> ${data.comments ?? ''}</p>
                    <p><strong>Huidige houder:</strong> ${data.current_holder_name ?? 'Geen'}</p>
                    <p><strong>Houder sinds:</strong> ${data.holder_since ?? 'N/A'}</p>
                `;

                // Als er al een current_holder is
                if (data.current_holder) {
                    assetActions.innerHTML = `
                        <p class="font-medium">
                            Dit product is gekoppeld aan gebruiker 
                            ${data.current_holder_name ?? data.current_holder}
                        </p>
                        <form method="post" class="space-y-4">
                            <input type="hidden" name="asset_id" value="${data.id}" />
                            <button
                                type="submit"
                                name="unassign"
                                class="inline-flex items-center px-4 py-2 text-sm font-medium text-white 
                                       bg-red-600 rounded-md hover:bg-red-700 focus:outline-none 
                                       focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            >
                                Loskoppelen
                            </button>
                        </form>
                        <a 
                          href="history.php?asset_id=${data.id}"
                          class="block text-blue-600 underline mt-4"
                        >
                          Bekijk log/geschiedenis
                        </a>
                    `;
                } else {
                    // Anders: geef user keus om te koppelen
                    let userOptions = `
                    <?php foreach ($allUsers as $u): ?>
                        <option value="<?php echo $u['id']; ?>">
                            <?php echo htmlspecialchars($u['username']); ?>
                        </option>
                    <?php endforeach; ?>
                    `;

                    assetActions.innerHTML = `
                        <form method="post" class="space-y-4">
                            <input type="hidden" name="asset_id" value="${data.id}" />
                            <div>
                                <label for="user_id" class="block mb-1 text-sm font-medium text-gray-700">
                                    Gebruiker:
                                </label>
                                <select
                                    id="user_id"
                                    name="user_id"
                                    class="block w-full mt-1 rounded-md border-gray-300 bg-gray-50 
                                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                >
                                    ${userOptions}
                                </select>
                            </div>
                            <div>
                                <label for="comments" class="block mb-1 text-sm font-medium text-gray-700">
                                    Opmerkingen (uitgifte):
                                </label>
                                <textarea
                                    id="comments"
                                    name="comments"
                                    rows="3"
                                    class="block w-full mt-1 rounded-md border-gray-300 bg-gray-50
                                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                ></textarea>
                            </div>
                            <div>
                                <button
                                    type="submit"
                                    name="assign"
                                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-white 
                                           bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none 
                                           focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                    Koppelen
                                </button>
                            </div>
                        </form>
                        <a 
                          href="history.php?asset_id=${data.id}"
                          class="block text-blue-600 underline mt-4"
                        >
                          Bekijk log/geschiedenis
                        </a>
                    `;
                }

                assetDetails.classList.remove('hidden');
            } else {
                assetDetails.classList.add('hidden');
                alert('Asset niet gevonden');
            }
        });
});
</script>
