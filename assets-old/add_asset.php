<?php
session_start();
require __DIR__ . '/../../includes/db.php';
require __DIR__ . '/../../includes/functions.php';
require __DIR__ . '/../../includes/LogController.php';

$pageTitle = "Asset toevoegen";

// Controleer of de gebruiker een admin is
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $uid  = trim($_POST['uid'] ?? '');
    $name = trim($_POST['name'] ?? '');

    if ($uid === '' || $name === '') {
        $_SESSION['error'] = "UID en Naam zijn verplichte velden!";
        header("Location: add_asset.php");
        exit();
    }

    $brand          = trim($_POST['brand'] ?? '') ?: null;
    $model          = trim($_POST['model'] ?? '') ?: null;
    $type           = trim($_POST['type'] ?? '') ?: null;
    $purchase_date  = (!empty($_POST['purchase_date'])) ? $_POST['purchase_date'] : null;
    $serial_number  = trim($_POST['serial_number'] ?? '') ?: null;
    $status         = trim($_POST['status'] ?? '') ?: null;
    $purchase_value = (isset($_POST['purchase_value']) && $_POST['purchase_value'] !== '')
        ? floatval($_POST['purchase_value'])
        : null;
    $quantity       = (isset($_POST['quantity']) && $_POST['quantity'] !== '')
        ? intval($_POST['quantity'])
        : null;
    $product_link   = trim($_POST['product_link'] ?? '') ?: null;
    $comments       = trim($_POST['comments'] ?? '') ?: null;
    $visibility     = isset($_POST['visibility']) ? 1 : 0;
    $user_group     = trim($_POST['user_group'] ?? '') ?: null;

    $sql = "
        INSERT INTO assets (
            uid,
            name,
            brand,
            model,
            type,
            purchase_date,
            serial_number,
            status,
            purchase_value,
            quantity,
            product_link,
            comments,
            visibility,
            user_group
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ";

    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $uid,
            $name,
            $brand,
            $model,
            $type,
            $purchase_date,
            $serial_number,
            $status,
            $purchase_value,
            $quantity,
            $product_link,
            $comments,
            $visibility,
            $user_group
        ]);

        $_SESSION['message'] = "Asset '$uid' succesvol toegevoegd!";
        header("Location: view_assets.php");
        exit();
    } catch (PDOException $e) {
        if ($e->getCode() == 23000) {
            $_SESSION['error'] = "De UID '$uid' bestaat al. Kies een andere.";
            header("Location: add_asset.php");
            exit();
        } else {
            throw $e;
        }
    }
}
?>

<?php include '../../templates/header.php'; ?>

<div class="max-w-3xl mx-auto p-4 mt-6">
    <div class="bg-white shadow rounded p-6">
        <!-- Eventuele foutmelding -->
        <?php if (!empty($_SESSION['error'])): ?>
            <div class="mb-4 p-3 bg-red-100 text-red-700 rounded">
                <?php 
                    echo $_SESSION['error'];
                    unset($_SESSION['error']);
                ?>
            </div>
        <?php endif; ?>

        <!-- Eventuele succesmelding -->
        <?php if (!empty($_SESSION['message'])): ?>
            <div class="mb-4 p-3 bg-green-100 text-green-700 rounded">
                <?php 
                    echo $_SESSION['message'];
                    unset($_SESSION['message']);
                ?>
            </div>
        <?php endif; ?>

        <h1 class="text-2xl font-semibold mb-6">Voeg een nieuw asset toe</h1>

        <form method="post" class="space-y-4">
            
            <!-- UID + AJAX validatie -->
            <div>
                <label for="uid" class="block mb-1 text-sm font-medium text-gray-700">
                    Unieke code (UID) <span class="text-red-500">*</span>
                </label>
                <input 
                    type="text" 
                    id="uid" 
                    name="uid" 
                    required
                    class="block w-full rounded-md border border-gray-300 bg-gray-50
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                <p id="uidError" class="text-red-600 text-sm mt-1 hidden">
                    Deze UID is al in gebruik. Kies een andere.
                </p>
            </div>

            <!-- Naam (verplicht) -->
            <div>
                <label for="name" class="block mb-1 text-sm font-medium text-gray-700">
                    Naam <span class="text-red-500">*</span>
                </label>
                <input 
                    type="text" 
                    id="name" 
                    name="name" 
                    required
                    class="block w-full rounded-md border border-gray-300 bg-gray-50
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Merk -->
            <div>
                <label for="brand" class="block mb-1 text-sm font-medium text-gray-700">
                    Merk
                </label>
                <input 
                    type="text" 
                    id="brand" 
                    name="brand"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50 
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Model -->
            <div>
                <label for="model" class="block mb-1 text-sm font-medium text-gray-700">
                    Model
                </label>
                <input 
                    type="text" 
                    id="model" 
                    name="model"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Type -->
            <div>
                <label for="type" class="block mb-1 text-sm font-medium text-gray-700">
                    Type
                </label>
                <select 
                    id="type" 
                    name="type"
                    class="block w-full mt-1 rounded-md border border-gray-300 bg-gray-50
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                    <option value="">- Selecteer type -</option>
                    <option value="Laptop">Laptop</option>
                    <option value="SD-kaart">SD-kaart</option>
                    <option value="Telefoon">Telefoon</option>
                    <option value="Batlogger">Batlogger</option>
                    <option value="Overig">Overig</option>
                </select>
            </div>

            <!-- Aankoopdatum -->
            <div>
                <label for="purchase_date" class="block mb-1 text-sm font-medium text-gray-700">
                    Aankoopdatum
                </label>
                <input 
                    type="date" 
                    id="purchase_date" 
                    name="purchase_date"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Serienummer -->
            <div>
                <label for="serial_number" class="block mb-1 text-sm font-medium text-gray-700">
                    Serienummer
                </label>
                <input 
                    type="text" 
                    id="serial_number" 
                    name="serial_number"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Status -->
            <div>
                <label for="status" class="block mb-1 text-sm font-medium text-gray-700">
                    Status
                </label>
                <select 
                    id="status" 
                    name="status" 
                    class="block w-full mt-1 rounded-md border border-gray-300 bg-gray-50
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                    <option value="">- Selecteer status -</option>
                    <option value="In gebruik">In gebruik</option>
                    <option value="Op voorraad">Op voorraad</option>
                    <option value="Defect">Defect</option>
                </select>
            </div>

            <!-- Aanschafwaarde -->
            <div>
                <label for="purchase_value" class="block mb-1 text-sm font-medium text-gray-700">
                    Aanschafwaarde (€)
                </label>
                <input 
                    type="number" 
                    step="0.01" 
                    id="purchase_value" 
                    name="purchase_value"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Aantal -->
            <div>
                <label for="quantity" class="block mb-1 text-sm font-medium text-gray-700">
                    Aantal
                </label>
                <input 
                    type="number" 
                    id="quantity" 
                    name="quantity"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Link naar productwebsite -->
            <div>
                <label for="product_link" class="block mb-1 text-sm font-medium text-gray-700">
                    Link naar productwebsite
                </label>
                <input 
                    type="url" 
                    id="product_link" 
                    name="product_link"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Opmerkingen -->
            <div>
                <label for="comments" class="block mb-1 text-sm font-medium text-gray-700">
                    Opmerkingen
                </label>
                <textarea 
                    id="comments" 
                    name="comments" 
                    rows="3"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                ></textarea>
            </div>

            <!-- Zichtbaarheid -->
            <div class="flex items-center space-x-2">
                <input 
                    type="checkbox" 
                    id="visibility" 
                    name="visibility"
                    class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                >
                <label for="visibility" class="text-sm font-medium text-gray-700">
                    Zichtbaar
                </label>
            </div>

            <!-- Gebruikersgroep -->
            <div>
                <label for="user_group" class="block mb-1 text-sm font-medium text-gray-700">
                    Gebruikersgroep
                </label>
                <input 
                    type="text" 
                    id="user_group" 
                    name="user_group"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Submit -->
            <div>
                <button 
                    id="submitBtn"
                    type="submit"
                    class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700
                           focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    Toevoegen
                </button>
            </div>
        </form>
    </div><!-- /bg-white -->
</div><!-- /max-w-3xl -->

<?php include '../templates/footer.php'; ?>

<script>
/**
 * AJAX-validatie van UID met absoluut pad
 * Vervang 'https://mijnsite.nl/api' door je eigen domein/locatie
 */
const uidInput  = document.getElementById('uid');
const uidError  = document.getElementById('uidError');
const submitBtn = document.getElementById('submitBtn');
let typingTimer = null;

// Wacht 500ms na stoppen typen
uidInput.addEventListener('keyup', () => {
    clearTimeout(typingTimer);
    typingTimer = setTimeout(checkUID, 500);
});

// Ook bij 'blur' (veld verlaten)
uidInput.addEventListener('blur', checkUID);

function checkUID() {
    const uidValue = uidInput.value.trim();
    if (uidValue === '') {
        hideError();
        return;
    }

    // Gebruik absoluut pad, bijv:
    fetch(`reiskosten/api/check_uid.php?uid=${encodeURIComponent(uidValue)}`)
        .then(resp => resp.json())
        .then(data => {
            if (data.exists) {
                showError();
            } else {
                hideError();
            }
        })
        .catch(err => {
            console.error('Fout bij check_uid:', err);
        });
}

function showError() {
    uidError.classList.remove('hidden');
    submitBtn.disabled = true;
    submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
}

function hideError() {
    uidError.classList.add('hidden');
    submitBtn.disabled = false;
    submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
}
</script>
