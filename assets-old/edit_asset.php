<?php
session_start();
require __DIR__ . '/../../includes/db.php';
require __DIR__ . '/../../includes/functions.php';
require __DIR__ . '/../../includes/LogController.php';

// Controleer of de gebruiker een admin is
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.php");
    exit();
}
$pageTitle = "Asset bewerken";
// Haal 'id' op uit de URL (als het bestaat).
$assetId = $_GET['id'] ?? null;
$asset   = null;

if ($assetId) {
    // Probeer het bestaande asset op te halen
    $stmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
    $stmt->execute([$assetId]);
    $asset = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$asset) {
        // Als het ID niet bestaat, redirect
        $_SESSION['error'] = "Asset niet gevonden om te bewerken.";
        header("Location: view_assets.php");
        exit();
    }
}

// Verwerk het formulier bij een POST-request
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Haal velden op; alleen $uid en $name zijn verplicht
    $uid            = trim($_POST['uid'] ?? '');
    $name           = trim($_POST['name'] ?? '');
    if ($uid === '' || $name === '') {
        die("UID en Naam zijn verplichte velden!");
    }

    $brand          = trim($_POST['brand'] ?? '') ?: null;
    $model          = trim($_POST['model'] ?? '') ?: null;
    $type           = trim($_POST['type'] ?? '') ?: null;
    $purchase_date  = !empty($_POST['purchase_date']) ? $_POST['purchase_date'] : null;
    $serial_number  = trim($_POST['serial_number'] ?? '') ?: null;
    $status         = trim($_POST['status'] ?? '') ?: null;
    $purchase_value = (isset($_POST['purchase_value']) && $_POST['purchase_value'] !== '')
        ? floatval($_POST['purchase_value'])
        : null;
    $quantity       = (isset($_POST['quantity']) && $_POST['quantity'] !== '')
        ? intval($_POST['quantity'])
        : null;
    $product_link   = trim($_POST['product_link'] ?? '') ?: null;
    $comments       = trim($_POST['comments'] ?? '') ?: null;
    $visibility     = isset($_POST['visibility']) ? 1 : 0;
    $user_group     = trim($_POST['user_group'] ?? '') ?: null;

    if ($asset) {
        // Asset bestaat, dus we doen een UPDATE
        $stmt = $pdo->prepare("
            UPDATE assets SET
                uid             = ?,
                name            = ?,
                brand           = ?,
                model           = ?,
                type            = ?,
                purchase_date   = ?,
                serial_number   = ?,
                status          = ?,
                purchase_value  = ?,
                quantity        = ?,
                product_link    = ?,
                comments        = ?,
                visibility      = ?,
                user_group      = ?
            WHERE id = ?
        ");
        $stmt->execute([
            $uid,
            $name,
            $brand,
            $model,
            $type,
            $purchase_date,
            $serial_number,
            $status,
            $purchase_value,
            $quantity,
            $product_link,
            $comments,
            $visibility,
            $user_group,
            $assetId  // WHERE
        ]);
    } else {
        // Geen $asset, dus nieuw asset -> INSERT
        $stmt = $pdo->prepare("
            INSERT INTO assets (
                uid,
                name,
                brand,
                model,
                type,
                purchase_date,
                serial_number,
                status,
                purchase_value,
                quantity,
                product_link,
                comments,
                visibility,
                user_group
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $uid,
            $name,
            $brand,
            $model,
            $type,
            $purchase_date,
            $serial_number,
            $status,
            $purchase_value,
            $quantity,
            $product_link,
            $comments,
            $visibility,
            $user_group
        ]);
    }

    // Na succesvol toevoegen/bijwerken doorsturen
    header("Location: view_assets.php");
    exit();
}
?>

<?php include '../../templates/header.php'; ?>

<!-- 
  Buitenste container: max-w-3xl voor smalle weergave 
  en mx-auto voor centrering 
  p-4 mt-6 voor voldoende ruimte 
-->
<div class="max-w-3xl mx-auto p-4 mt-6">
    <!-- 
      Witte container met schaduw en afgeronde hoeken 
      p-6: royale binnenpadding
    -->
    <div class="bg-white shadow rounded p-6">
        <?php if ($asset): ?>
            <h1 class="text-2xl font-semibold mb-6">Asset bewerken</h1>
        <?php else: ?>
            <h1 class="text-2xl font-semibold mb-6">Nieuw asset toevoegen</h1>
        <?php endif; ?>

        <form method="post" class="space-y-4">
            <!-- UID (verplicht) -->
            <div>
                <label for="uid" class="block mb-1 text-sm font-medium text-gray-700">
                    Unieke code (UID) <span class="text-red-500">*</span>
                </label>
                <input 
                    type="text" 
                    id="uid" 
                    name="uid" 
                    required
                    value="<?php echo htmlspecialchars($asset['uid'] ?? ''); ?>"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50 
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Naam (verplicht) -->
            <div>
                <label for="name" class="block mb-1 text-sm font-medium text-gray-700">
                    Naam <span class="text-red-500">*</span>
                </label>
                <input 
                    type="text" 
                    id="name" 
                    name="name" 
                    required
                    value="<?php echo htmlspecialchars($asset['name'] ?? ''); ?>"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50 
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Merk -->
            <div>
                <label for="brand" class="block mb-1 text-sm font-medium text-gray-700">
                    Merk
                </label>
                <input 
                    type="text" 
                    id="brand" 
                    name="brand"
                    value="<?php echo htmlspecialchars($asset['brand'] ?? ''); ?>"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50 
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Model -->
            <div>
                <label for="model" class="block mb-1 text-sm font-medium text-gray-700">
                    Model
                </label>
                <input 
                    type="text" 
                    id="model" 
                    name="model"
                    value="<?php echo htmlspecialchars($asset['model'] ?? ''); ?>"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50 
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Type -->
            <div>
                <label for="type" class="block mb-1 text-sm font-medium text-gray-700">
                    Type
                </label>
                <select 
                    id="type" 
                    name="type"
                    class="block w-full mt-1 rounded-md border border-gray-300 bg-gray-50 
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                    <option value="">- Selecteer type -</option>
                    <option value="Laptop"    <?php if(($asset['type'] ?? '') === 'Laptop') echo 'selected'; ?>>Laptop</option>
                    <option value="SD-kaart"  <?php if(($asset['type'] ?? '') === 'SD-kaart') echo 'selected'; ?>>SD-kaart</option>
                    <option value="Telefoon"  <?php if(($asset['type'] ?? '') === 'Telefoon') echo 'selected'; ?>>Telefoon</option>
                    <option value="Batlogger" <?php if(($asset['type'] ?? '') === 'Batlogger') echo 'selected'; ?>>Batlogger</option>
                    <option value="Overig"    <?php if(($asset['type'] ?? '') === 'Overig') echo 'selected'; ?>>Overig</option>
                </select>
            </div>

            <!-- Aankoopdatum -->
            <div>
                <label for="purchase_date" class="block mb-1 text-sm font-medium text-gray-700">
                    Aankoopdatum
                </label>
                <input 
                    type="date" 
                    id="purchase_date" 
                    name="purchase_date"
                    value="<?php echo htmlspecialchars($asset['purchase_date'] ?? ''); ?>"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50 
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Serienummer -->
            <div>
                <label for="serial_number" class="block mb-1 text-sm font-medium text-gray-700">
                    Serienummer
                </label>
                <input 
                    type="text" 
                    id="serial_number" 
                    name="serial_number"
                    value="<?php echo htmlspecialchars($asset['serial_number'] ?? ''); ?>"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50 
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Status -->
            <div>
                <label for="status" class="block mb-1 text-sm font-medium text-gray-700">
                    Status
                </label>
                <select 
                    id="status" 
                    name="status" 
                    class="block w-full mt-1 rounded-md border border-gray-300 bg-gray-50
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                    <option value="">- Selecteer status -</option>
                    <option value="In gebruik" <?php if(($asset['status'] ?? '') === 'In gebruik') echo 'selected'; ?>>In gebruik</option>
                    <option value="Op voorraad" <?php if(($asset['status'] ?? '') === 'Op voorraad') echo 'selected'; ?>>Op voorraad</option>
                    <option value="Defect"      <?php if(($asset['status'] ?? '') === 'Defect') echo 'selected'; ?>>Defect</option>
                </select>
            </div>

            <!-- Aanschafwaarde -->
            <div>
                <label for="purchase_value" class="block mb-1 text-sm font-medium text-gray-700">
                    Aanschafwaarde (€)
                </label>
                <input 
                    type="number" 
                    step="0.01" 
                    id="purchase_value" 
                    name="purchase_value"
                    value="<?php echo htmlspecialchars($asset['purchase_value'] ?? ''); ?>"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50 
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Aantal -->
            <div>
                <label for="quantity" class="block mb-1 text-sm font-medium text-gray-700">
                    Aantal
                </label>
                <input 
                    type="number" 
                    id="quantity" 
                    name="quantity"
                    value="<?php echo htmlspecialchars($asset['quantity'] ?? ''); ?>"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50 
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Link naar productwebsite -->
            <div>
                <label for="product_link" class="block mb-1 text-sm font-medium text-gray-700">
                    Link naar productwebsite
                </label>
                <input 
                    type="url" 
                    id="product_link" 
                    name="product_link" 
                    value="<?php echo htmlspecialchars($asset['product_link'] ?? ''); ?>"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Opmerkingen -->
            <div>
                <label for="comments" class="block mb-1 text-sm font-medium text-gray-700">
                    Opmerkingen
                </label>
                <textarea 
                    id="comments" 
                    name="comments" 
                    rows="3"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50 
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                ><?php echo htmlspecialchars($asset['comments'] ?? ''); ?></textarea>
            </div>

            <!-- Zichtbaarheid -->
            <div class="flex items-center space-x-2">
                <input 
                    type="checkbox" 
                    id="visibility" 
                    name="visibility"
                    <?php if(!empty($asset['visibility'])) echo 'checked'; ?>
                    class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                >
                <label for="visibility" class="text-sm font-medium text-gray-700">
                    Zichtbaar
                </label>
            </div>

            <!-- Gebruikersgroep -->
            <div>
                <label for="user_group" class="block mb-1 text-sm font-medium text-gray-700">
                    Gebruikersgroep
                </label>
                <input 
                    type="text" 
                    id="user_group" 
                    name="user_group"
                    value="<?php echo htmlspecialchars($asset['user_group'] ?? ''); ?>"
                    class="block w-full rounded-md border border-gray-300 bg-gray-50 
                           text-gray-800 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
            </div>

            <!-- Knop opslaan -->
            <div>
                <button 
                    type="submit"
                    class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 
                           focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    <?php echo $asset ? 'Bijwerken' : 'Toevoegen'; ?>
                </button>
            </div>
        </form>
    </div><!-- /bg-white shadow rounded p-6 -->
</div><!-- /max-w-3xl mx-auto p-4 mt-6 -->

<?php include '../../templates/footer.php'; ?>
