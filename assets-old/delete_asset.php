<?php
// pages/assets/delete_asset.php

session_start();
require __DIR__ . '/../../includes/db.php';
require __DIR__ . '/../../includes/functions.php';

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    // Alleen ingelogde admins mogen verwijderen
    header("Location: login.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['asset_id'])) {
    $assetId = (int) $_POST['asset_id'];

    // Optioneel: haal de naam op voor logging of berichtgeving
    $stmt = $pdo->prepare("SELECT name FROM assets WHERE id = ?");
    $stmt->execute([$assetId]);
    $asset = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($asset) {
        // Verwijder asset
        $del = $pdo->prepare("DELETE FROM assets WHERE id = ?");
        $del->execute([$assetId]);

        // Stel een succesmelding in
        $_SESSION['message'] = "Asset “" . htmlspecialchars($asset['name'], ENT_QUOTES) . "” is succesvol verwijderd.";
    } else {
        $_SESSION['error'] = "Asset niet gevonden, verwijderen mislukt.";
    }
}

header("Location: view_assets.php");
exit();
