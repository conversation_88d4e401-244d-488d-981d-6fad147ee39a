<?php
/**
 * AssetLog model
 */
class AssetLog extends Model
{
    protected $table = 'asset_logs';

    /**
     * Haal alle logs op voor een asset
     *
     * @param int $assetId
     * @return array
     */
    public function getLogsByAssetId($assetId)
    {
        try {
            // Probeer eerst met de action kolom
            $sql = "
                SELECT l.*,
                       u.username,
                       IFNULL(l.action, '') as action
                  FROM {$this->table} l
                  LEFT JOIN users u ON l.user_id = u.id
                 WHERE l.asset_id = :asset_id
                 ORDER BY l.start_date DESC
            ";
            $stmt = $this->db->prepare($sql);
            $stmt->execute(['asset_id' => $assetId]);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            // Als de action kolom niet bestaat, gebruik een fallback
            if (strpos($e->getMessage(), "Unknown column 'l.action'") !== false) {
                $sql = "
                    SELECT l.*,
                           u.username,
                           'uitgeleend' as action
                      FROM {$this->table} l
                      LEFT JOIN users u ON l.user_id = u.id
                     WHERE l.asset_id = :asset_id
                     ORDER BY l.start_date DESC
                ";
                $stmt = $this->db->prepare($sql);
                $stmt->execute(['asset_id' => $assetId]);
                return $stmt->fetchAll();
            } else {
                // Als het een andere fout is, gooi deze door
                throw $e;
            }
        }
    }

    /**
     * Voeg een nieuwe log toe
     *
     * @param int $assetId
     * @param int $userId
     * @param string $action
     * @param string $notes
     * @return int
     */
    public function addLog($assetId, $userId, $action, $notes = '')
    {
        try {
            // Probeer eerst met de action kolom
            return $this->create([
                'asset_id' => $assetId,
                'user_id' => $userId,
                'action' => $action,
                'notes' => $notes,
                'start_date' => date('Y-m-d H:i:s')
            ]);
        } catch (PDOException $e) {
            // Als de action kolom niet bestaat, gebruik een fallback zonder action
            if (strpos($e->getMessage(), "Unknown column 'action'") !== false) {
                return $this->create([
                    'asset_id' => $assetId,
                    'user_id' => $userId,
                    'notes' => $notes . ' (Actie: ' . $action . ')',
                    'start_date' => date('Y-m-d H:i:s')
                ]);
            } else {
                // Als het een andere fout is, gooi deze door
                throw $e;
            }
        }
    }

    /**
     * Update de einddatum van een log
     *
     * @param int $assetId
     * @return bool
     */
    public function updateEndDate($assetId)
    {
        $sql = "UPDATE {$this->table} SET end_date = NOW() WHERE asset_id = :asset_id AND end_date IS NULL";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute(['asset_id' => $assetId]);
    }

    /**
     * Haal de huidige actieve log op voor een asset
     *
     * @param int $assetId
     * @return array|null
     */
    public function getCurrentLog($assetId)
    {
        try {
            // Probeer eerst met de action kolom
            $sql = "
                SELECT l.*,
                       u.username,
                       IFNULL(l.action, '') as action
                  FROM {$this->table} l
                  LEFT JOIN users u ON l.user_id = u.id
                 WHERE l.asset_id = :asset_id
                   AND l.end_date IS NULL
                 ORDER BY l.start_date DESC
                 LIMIT 1
            ";
            $stmt = $this->db->prepare($sql);
            $stmt->execute(['asset_id' => $assetId]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            // Als de action kolom niet bestaat, gebruik een fallback
            if (strpos($e->getMessage(), "Unknown column 'l.action'") !== false) {
                $sql = "
                    SELECT l.*,
                           u.username,
                           'uitgeleend' as action
                      FROM {$this->table} l
                      LEFT JOIN users u ON l.user_id = u.id
                     WHERE l.asset_id = :asset_id
                       AND l.end_date IS NULL
                     ORDER BY l.start_date DESC
                     LIMIT 1
                ";
                $stmt = $this->db->prepare($sql);
                $stmt->execute(['asset_id' => $assetId]);
                return $stmt->fetch();
            } else {
                // Als het een andere fout is, gooi deze door
                throw $e;
            }
        }
    }
}
