<?php
/**
 * Basismodel dat alle modellen kunnen uitbreiden
 */
abstract class Model
{
    /**
     * De database-verbinding
     *
     * @var PDO
     */
    protected $db;

    /**
     * De tabelnaam
     *
     * @var string
     */
    protected $table;

    /**
     * De primaire sleutel
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * Vind een record op basis van de primaire sleutel
     *
     * @param int $id
     * @return array|null
     */
    public function find($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id LIMIT 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['id' => $id]);
        return $stmt->fetch();
    }

    /**
     * Krijg alle records
     *
     * @return array
     */
    public function all()
    {
        $sql = "SELECT * FROM {$this->table}";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Maak een nieuw record
     *
     * @param array $data
     * @return int Het ID van het nieuwe record
     */
    public function create(array $data)
    {
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));

        $sql = "INSERT INTO {$this->table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->db->prepare($sql);
        $stmt->execute($data);

        return $this->db->lastInsertId();
    }

    /**
     * Update een record
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update($id, array $data)
    {
        try {
            $fields = [];
            foreach (array_keys($data) as $key) {
                $fields[] = "{$key} = :{$key}";
            }

            $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE {$this->primaryKey} = :id";

            $data['id'] = $id;
            $stmt = $this->db->prepare($sql);
            return $stmt->execute($data);
        } catch (PDOException $e) {
            // Als de updated_at kolom niet bestaat, verwijder deze uit de data en probeer opnieuw
            if (strpos($e->getMessage(), "Unknown column 'updated_at'") !== false && isset($data['updated_at'])) {
                unset($data['updated_at']);

                $fields = [];
                foreach (array_keys($data) as $key) {
                    $fields[] = "{$key} = :{$key}";
                }

                $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE {$this->primaryKey} = :id";

                $data['id'] = $id;
                $stmt = $this->db->prepare($sql);
                return $stmt->execute($data);
            } else {
                // Als het een andere fout is, gooi deze door
                throw $e;
            }
        }
    }

    /**
     * Verwijder een record
     *
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = :id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute(['id' => $id]);
    }

    /**
     * Vind records op basis van voorwaarden
     *
     * @param array $conditions
     * @return array
     */
    public function where(array $conditions)
    {
        $fields = [];
        foreach (array_keys($conditions) as $key) {
            $fields[] = "{$key} = :{$key}";
        }

        $sql = "SELECT * FROM {$this->table} WHERE " . implode(' AND ', $fields);
        $stmt = $this->db->prepare($sql);
        $stmt->execute($conditions);
        return $stmt->fetchAll();
    }

    /**
     * Vind één record op basis van voorwaarden
     *
     * @param array $conditions
     * @return array|null
     */
    public function whereOne(array $conditions)
    {
        $fields = [];
        foreach (array_keys($conditions) as $key) {
            $fields[] = "{$key} = :{$key}";
        }

        $sql = "SELECT * FROM {$this->table} WHERE " . implode(' AND ', $fields) . " LIMIT 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute($conditions);
        return $stmt->fetch();
    }

    /**
     * Voer een aangepaste query uit
     *
     * @param string $sql
     * @param array $params
     * @return array
     */
    public function query($sql, $params = [])
    {
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
}
