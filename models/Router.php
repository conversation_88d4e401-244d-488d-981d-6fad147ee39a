<?php
/**
 * Router klasse voor het afhandelen van routes
 */
class Router
{
    /**
     * De routes
     *
     * @var array
     */
    private $routes = [
        'GET' => [],
        'POST' => []
    ];

    /**
     * Voeg een GET-route toe
     *
     * @param string $uri
     * @param string $controller
     * @return void
     */
    public function get($uri, $controller)
    {
        $this->routes['GET'][$uri] = $controller;
    }

    /**
     * Voeg een POST-route toe
     *
     * @param string $uri
     * @param string $controller
     * @return void
     */
    public function post($uri, $controller)
    {
        $this->routes['POST'][$uri] = $controller;
    }

    /**
     * Dispatch de route
     *
     * @return mixed
     */
    public function dispatch()
    {
        $uri = $this->getUri();
        $method = $_SERVER['REQUEST_METHOD'];

        // Debug informatie
        error_log("URI: {$uri}");
        error_log("Method: {$method}");
        error_log("Script Name: " . $_SERVER['SCRIPT_NAME']);
        error_log("Script Filename: " . $_SERVER['SCRIPT_FILENAME']);
        error_log("Document Root: " . $_SERVER['DOCUMENT_ROOT']);
        error_log("Request URI: " . $_SERVER['REQUEST_URI']);
        error_log("PHP_SELF: " . $_SERVER['PHP_SELF']);

        if (array_key_exists($uri, $this->routes[$method])) {
            error_log("Route gevonden: {$uri} => {$this->routes[$method][$uri]}");
            return $this->callAction(
                ...explode('@', $this->routes[$method][$uri])
            );
        }

        // Debug informatie
        error_log("Geen exacte route gevonden, zoeken naar dynamische route...");

        // Probeer een dynamische route te vinden
        foreach ($this->routes[$method] as $route => $controller) {
            error_log("Controleren route: {$route}");

            if (strpos($route, ':') !== false) {
                $pattern = preg_replace('/:[a-zA-Z0-9]+/', '([a-zA-Z0-9]+)', $route);
                $pattern = '#^' . $pattern . '$#';

                error_log("Pattern: {$pattern}");

                if (preg_match($pattern, $uri, $matches)) {
                    array_shift($matches); // Verwijder de volledige match

                    error_log("Dynamische route gevonden: {$route} => {$controller}");
                    error_log("Parameters: " . json_encode($matches));

                    return $this->callAction(
                        ...explode('@', $controller),
                        ...$matches
                    );
                }
            }
        }

        // Debug informatie
        error_log("Geen route gevonden voor {$method} {$uri}");
        error_log("Beschikbare routes: " . json_encode(array_keys($this->routes[$method])));

        throw new Exception("Geen route gevonden voor {$method} {$uri}");
    }

    /**
     * Krijg de huidige URI
     *
     * @return string
     */
    private function getUri()
    {
        // Haal de URI op uit de REQUEST_URI
        $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

        // Verwijder de base directory als die er is
        $scriptDir = dirname($_SERVER['SCRIPT_NAME']);

        // Als we in een subdirectory draaien, verwijder deze dan uit de URI
        if ($scriptDir !== '/' && $scriptDir !== '\\') {
            $uri = preg_replace('#^' . preg_quote($scriptDir, '#') . '#', '', $uri);
        }

        // Verwijder eventuele dubbele slashes
        $uri = preg_replace('#/+#', '/', $uri);

        // Verwijder leading en trailing slashes
        $uri = trim($uri, '/');

        // Als de URI leeg is, gebruik dan 'index'
        if (empty($uri)) {
            $uri = 'index';
        }

        // Debug informatie
        error_log("Originele REQUEST_URI: " . $_SERVER['REQUEST_URI']);
        error_log("Script directory: " . $scriptDir);
        error_log("Verwerkte URI: " . $uri);

        return $uri;
    }

    /**
     * Roep een controller-actie aan
     *
     * @param string $controller
     * @param string $action
     * @param mixed ...$params
     * @return mixed
     */
    private function callAction($controller, $action, ...$params)
    {
        $controller = new $controller();

        if (!method_exists($controller, $action)) {
            throw new Exception(
                "{$controller} heeft geen methode {$action}"
            );
        }

        return $controller->$action(...$params);
    }
}
