<?php
/**
 * View klasse voor het renderen van templates
 */
class View
{
    /**
     * Render een template met data
     *
     * @param string $template
     * @param array $data
     * @return string
     */
    public static function render($template, $data = [])
    {
        // Maak variabelen beschikbaar in de template
        extract($data);

        // Start output buffering
        ob_start();

        // Laad de template
        $templatePath = __DIR__ . "/../views/{$template}.php";

        if (!file_exists($templatePath)) {
            throw new Exception("Template '{$template}' niet gevonden.");
        }

        include $templatePath;

        // Krijg de inhoud van de buffer en sluit deze
        $content = ob_get_clean();

        return $content;
    }

    /**
     * Render een template met een layout
     *
     * @param string $template
     * @param array $data
     * @param string $layout
     * @return string
     */
    public static function renderWithLayout($template, $data = [], $layout = 'default')
    {
        // Render de template
        $content = self::render($template, $data);

        // Render de layout met de template-inhoud
        return self::render("layouts/{$layout}", array_merge($data, ['content' => $content]));
    }

    /**
     * Toon een template met data
     *
     * @param string $template
     * @param array $data
     * @return void
     */
    public static function display($template, $data = [])
    {
        echo self::render($template, $data);
    }

    /**
     * Toon een template met een layout
     *
     * @param string $template
     * @param array $data
     * @param string $layout
     * @return void
     */
    public static function displayWithLayout($template, $data = [], $layout = 'default')
    {
        echo self::renderWithLayout($template, $data, $layout);
    }

    /**
     * Escape HTML-tekens
     *
     * @param string|null $string
     * @return string
     */
    public static function escape($string)
    {
        if ($string === null) {
            return '';
        }
        return htmlspecialchars((string)$string, ENT_QUOTES, 'UTF-8');
    }
}
