<?php
/**
 * Auth klasse voor authenticatie en autorisatie
 */
class Auth
{
    /**
     * Controleer of de gebruiker is ingelogd
     *
     * @return bool
     */
    public static function check()
    {
        return isset($_SESSION['user_id']);
    }

    /**
     * Krijg de ingelogde gebruiker
     *
     * @return array|null
     */
    public static function user()
    {
        if (!self::check()) {
            return null;
        }

        $userModel = new User();
        return $userModel->find($_SESSION['user_id']);
    }

    /**
     * Krijg het ID van de ingelogde gebruiker
     *
     * @return int|null
     */
    public static function id()
    {
        return self::check() ? $_SESSION['user_id'] : null;
    }

    /**
     * Controleer of de ingelogde gebruiker een admin is
     *
     * @return bool
     */
    public static function isAdmin()
    {
        return self::check() && $_SESSION['is_admin'] == 1;
    }

    /**
     * Log een gebruiker in
     *
     * @param array $user
     * @return void
     */
    public static function login($user)
    {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['is_admin'] = (int) $user['is_admin'];
    }

    /**
     * Log een gebruiker uit
     *
     * @return void
     */
    public static function logout()
    {
        unset($_SESSION['user_id']);
        unset($_SESSION['username']);
        unset($_SESSION['is_admin']);
        session_destroy();
    }

    /**
     * Authenticeer een gebruiker
     *
     * @param string $username
     * @param string $password
     * @return bool
     */
    public static function attempt($username, $password)
    {
        $userModel = new User();
        $user = $userModel->authenticate($username, $password);

        if ($user) {
            self::login($user);
            return true;
        }

        return false;
    }

    /**
     * Controleer of de gebruiker toegang heeft tot een pagina
     *
     * @param string $permission
     * @return bool
     */
    public static function can($permission)
    {
        if (!self::check()) {
            return false;
        }

        switch ($permission) {
            case 'admin':
                return self::isAdmin();
            case 'view_declarations':
            case 'view_travel_records':
                return true;
            default:
                return false;
        }
    }

    /**
     * Redirect als de gebruiker niet is ingelogd
     *
     * @param string $redirect
     * @return void
     */
    public static function requireLogin($redirect = 'login')
    {
        if (!self::check()) {
            // Log de redirect
            error_log("Auth: Gebruiker niet ingelogd, redirect naar $redirect");

            // Voeg een leading slash toe als die er nog niet is
            if (strpos($redirect, '/') !== 0 && strpos($redirect, 'http') !== 0) {
                $redirect = '/' . $redirect;
            }

            header("Location: $redirect");
            exit();
        }
    }

    /**
     * Redirect als de gebruiker geen admin is
     *
     * @param string $redirect
     * @return void
     */
    public static function requireAdmin($redirect = 'index')
    {
        if (!self::isAdmin()) {
            // Log de redirect
            error_log("Auth: Gebruiker is geen admin, redirect naar $redirect");

            // Voeg een leading slash toe als die er nog niet is
            if (strpos($redirect, '/') !== 0 && strpos($redirect, 'http') !== 0) {
                $redirect = '/' . $redirect;
            }

            header("Location: $redirect");
            exit();
        }
    }

    /**
     * Verifieer het wachtwoord van de ingelogde gebruiker
     *
     * @param string $password
     * @return bool
     */
    public static function verifyPassword($password)
    {
        if (!self::check()) {
            return false;
        }

        $user = self::user();
        return password_verify($password, $user['password']);
    }

    /**
     * Log een bericht naar het logbestand
     *
     * @param string $message Het bericht om te loggen
     * @param string $level Het logniveau (error, warning, info, debug)
     * @return void
     */
    public static function log($message, $level = 'info')
    {
        $prefix = '';
        $userId = self::id() ? self::id() : 'guest';
        $username = self::check() ? self::user()['username'] : 'guest';

        switch (strtolower($level)) {
            case 'error':
                $prefix = "ERROR [User $userId:$username]: ";
                break;
            case 'warning':
                $prefix = "WARNING [User $userId:$username]: ";
                break;
            case 'info':
                $prefix = "INFO [User $userId:$username]: ";
                break;
            case 'debug':
                $prefix = "DEBUG [User $userId:$username]: ";
                break;
        }

        error_log($prefix . $message);
    }
}
