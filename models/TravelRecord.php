<?php
/**
 * TravelRecord model
 */
class TravelRecord extends Model
{
    protected $table = 'travel_records';

    /**
     * Voeg een nieuwe reiskostenregel toe
     *
     * @param int $userId
     * @param string $date
     * @param string $projectNumber
     * @param string $addressA
     * @param string $addressB
     * @param string|null $extraAddresses
     * @param float $singleTripKm
     * @param float $totalKm
     * @param bool $returnTrip
     * @return int
     */
    public function addTravelRecord($userId, $date, $projectNumber, $addressA, $addressB, $extraAddresses, $singleTripKm, $totalKm, $returnTrip)
    {
        // Converteer extra adressen naar JSON als het een array is
        if (is_array($extraAddresses)) {
            $extraAddresses = json_encode($extraAddresses);
        }
        return $this->create([
            'user_id' => $userId,
            'date' => $date,
            'project_number' => $projectNumber,
            'address_a' => $addressA,
            'address_b' => $addressB,
            'extra_addresses' => $extraAddresses,
            'single_trip_km' => $singleTripKm,
            'total_km' => $totalKm,
            'return_trip' => $returnTrip ? 1 : 0,
            'submitted' => 0,
            'paid' => 0,
            'rejected' => 0
        ]);
    }

    /**
     * Update een reiskostenregel
     *
     * @param int $id
     * @param string $date
     * @param string $projectNumber
     * @param string $addressA
     * @param string $addressB
     * @param string|null $extraAddresses
     * @param float $singleTripKm
     * @param float $totalKm
     * @param bool $returnTrip
     * @return bool
     */
    public function updateTravelRecord($id, $date, $projectNumber, $addressA, $addressB, $extraAddresses, $singleTripKm, $totalKm, $returnTrip)
    {
        // Converteer extra adressen naar JSON als het een array is
        if (is_array($extraAddresses)) {
            $extraAddresses = json_encode($extraAddresses);
        }

        return $this->update($id, [
            'date' => $date,
            'project_number' => $projectNumber,
            'address_a' => $addressA,
            'address_b' => $addressB,
            'extra_addresses' => $extraAddresses,
            'single_trip_km' => $singleTripKm,
            'total_km' => $totalKm,
            'return_trip' => $returnTrip ? 1 : 0,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Dien reiskostenregels in
     *
     * @param array $ids
     * @param int $userId
     * @return bool
     */
    public function submitTravelRecords($ids, $userId)
    {
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $params = array_merge($ids, [$userId]);

        $sql = "UPDATE {$this->table} SET submitted = 1, submitted_at = NOW()
                WHERE id IN ({$placeholders}) AND user_id = ? AND submitted = 0";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Markeer reiskostenregels als betaald
     *
     * @param array $ids
     * @return bool
     */
    public function markAsPaid($ids)
    {
        $placeholders = implode(',', array_fill(0, count($ids), '?'));

        $sql = "UPDATE {$this->table} SET paid = 1, updated_at = NOW()
                WHERE id IN ({$placeholders})";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute($ids);
    }

    /**
     * Markeer een reiskostenregel als afgekeurd
     *
     * @param int $id
     * @return bool
     */
    public function rejectTravelRecord($id)
    {
        return $this->update($id, [
            'submitted' => 0,
            'paid' => 0,
            'rejected' => 1,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Krijg gefilterde reiskostenregels voor een gebruiker
     *
     * @param int $userId
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @param string|null $projectNumber
     * @param bool $showSubmitted
     * @param bool $showPaid
     * @param bool $showRejected
     * @param bool $showOpen
     * @return array
     */
    public function getFilteredTravelRecords($userId, $dateFrom = null, $dateTo = null, $projectNumber = null, $showSubmitted = false, $showPaid = false, $showRejected = false, $showOpen = true)
    {
        $sql = "
            SELECT
                id, date, project_number, address_a, address_b, extra_addresses,
                single_trip_km, total_km, return_trip, submitted, paid, rejected, updated_at
            FROM {$this->table}
            WHERE user_id = :userId
        ";
        $params = ['userId' => $userId];

        // Filter op datum
        if ($dateFrom) {
            $sql .= " AND date >= :dateFrom";
            $params['dateFrom'] = $dateFrom;
        }
        if ($dateTo) {
            $sql .= " AND date <= :dateTo";
            $params['dateTo'] = $dateTo;
        }

        // Filter op projectnummer
        if ($projectNumber) {
            $sql .= " AND project_number = :projectNumber";
            $params['projectNumber'] = $projectNumber;
        }

        // Opbouw filtervoorwaarden
        $filterConditions = [];
        if ($showSubmitted) {
            $filterConditions[] = "(submitted = 1 AND paid = 0 AND rejected = 0)";
        }
        if ($showPaid) {
            $filterConditions[] = "(paid = 1)";
        }
        if ($showRejected) {
            $filterConditions[] = "(rejected = 1)";
        }
        if ($showOpen) {
            $filterConditions[] = "(submitted = 0 AND paid = 0 AND rejected = 0)";
        }

        // Als er filters zijn aangekruist, combineer met OR
        if (!empty($filterConditions)) {
            $sql .= " AND (" . implode(" OR ", $filterConditions) . ")";
        }

        $sql .= " ORDER BY date DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $records = $stmt->fetchAll();

        // Dynamisch bepalen van de status
        foreach ($records as &$record) {
            if ($record['paid']) {
                $record['dynamic_status'] = 'Uitbetaald';
                if (!empty($record['updated_at'])) {
                    $record['dynamic_status'] .= ' op ' . date('d-m-Y', strtotime($record['updated_at']));
                }
            } elseif ($record['rejected']) {
                $record['dynamic_status'] = 'Afgekeurd';
            } elseif ($record['submitted']) {
                $record['dynamic_status'] = 'Ingediend';
            } else {
                $record['dynamic_status'] = 'Niet ingediend';
            }
        }

        return $records;
    }

    /**
     * Krijg ingediende, niet-betaalde reiskostenregels voor een gebruiker
     *
     * @param int $userId
     * @return array
     */
    public function getPendingTravelRecords($userId)
    {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND submitted = 1 AND paid = 0 AND rejected = 0
                ORDER BY date DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }

    /**
     * Krijg alle niet-betaalde reiskostenregels voor een gebruiker
     *
     * @param int $userId
     * @return array
     */
    public function getPendingByUserId($userId)
    {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND paid = 0
                ORDER BY date DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }

    /**
     * Bereken het totaal aantal kilometers voor een set reiskostenregels
     *
     * @param array $records
     * @return float
     */
    public function calculateTotalKm($records)
    {
        $totalKm = 0;
        foreach ($records as $record) {
            $totalKm += $record['total_km'];
        }
        return $totalKm;
    }
}
