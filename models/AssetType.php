<?php
/**
 * AssetType model
 */
class AssetType extends Model
{
    protected $table = 'asset_types';
    
    /**
     * Haal alle asset types op
     * 
     * @return array
     */
    public function getAllTypes()
    {
        $sql = "SELECT * FROM {$this->table} ORDER BY name ASC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * Voeg een nieuw asset type toe
     * 
     * @param string $name
     * @return int
     */
    public function addType($name)
    {
        return $this->create([
            'name' => $name
        ]);
    }
    
    /**
     * Update een asset type
     * 
     * @param int $id
     * @param string $name
     * @return bool
     */
    public function updateType($id, $name)
    {
        return $this->update($id, [
            'name' => $name,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Verwijder een asset type
     * 
     * @param int $id
     * @return bool
     */
    public function deleteType($id)
    {
        return $this->delete($id);
    }
    
    /**
     * Controleer of een asset type in gebruik is
     * 
     * @param int $id
     * @return bool
     */
    public function isTypeInUse($id)
    {
        $sql = "SELECT COUNT(*) FROM assets WHERE type_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        return (int) $stmt->fetchColumn() > 0;
    }
}
