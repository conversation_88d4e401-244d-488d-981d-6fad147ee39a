<?php
/**
 * Notification model
 */
class Notification extends Model
{
    protected $table = 'notifications';

    /**
     * Maak een nieuwe notificatie
     *
     * @param int $userId De gebruiker die de actie heeft uitgevoerd
     * @param string $type Het type notificatie (asset_assigned, declaration_submitted, travel_submitted)
     * @param string $message Het bericht
     * @param array $data Extra data als JSON
     * @return int
     */
    public function create($userId, $type, $message, $data = [])
    {
        return parent::create([
            'user_id' => $userId,
            'type' => $type,
            'message' => $message,
            'data' => json_encode($data),
            'is_read' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Haal alle ongelezen notificaties op voor admins
     *
     * @return array
     */
    public function getUnreadForAdmins()
    {
        $sql = "
            SELECT n.*, u.username
            FROM {$this->table} n
            LEFT JOIN users u ON n.user_id = u.id
            WHERE n.is_read = 0
            ORDER BY n.created_at DESC
        ";
        return $this->query($sql);
    }

    /**
     * Tel het aantal ongelezen notificaties
     *
     * @return int
     */
    public function countUnread()
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE is_read = 0";
        $result = $this->query($sql);
        return $result[0]['count'] ?? 0;
    }

    /**
     * Markeer een notificatie als gelezen
     *
     * @param int $id
     * @return bool
     */
    public function markAsRead($id)
    {
        return $this->update($id, ['is_read' => 1]);
    }

    /**
     * Markeer alle notificaties als gelezen
     *
     * @return bool
     */
    public function markAllAsRead()
    {
        $sql = "UPDATE {$this->table} SET is_read = 1 WHERE is_read = 0";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute();
    }

    /**
     * Haal alle notificaties op (gelezen en ongelezen)
     *
     * @param int $limit
     * @return array
     */
    public function getAll($limit = 50)
    {
        $sql = "
            SELECT n.*, u.username
            FROM {$this->table} n
            LEFT JOIN users u ON n.user_id = u.id
            ORDER BY n.created_at DESC
            LIMIT :limit
        ";
        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Verwijder oude notificaties (ouder dan 30 dagen)
     *
     * @return bool
     */
    public function cleanupOld()
    {
        $sql = "DELETE FROM {$this->table} WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute();
    }
}
