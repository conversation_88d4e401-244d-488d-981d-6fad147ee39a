<?php
/**
 * LogModel klasse voor het beheren van logs
 */
class LogModel extends Model
{
    /**
     * Haal de logs op uit het logbestand
     * 
     * @param int $limit Aantal regels om op te halen
     * @param int $offset Offset voor paginering
     * @param string|null $search Zoekterm
     * @param string|null $level Log level filter (error, warning, info, debug)
     * @return array
     */
    public function getLogs($limit = 100, $offset = 0, $search = null, $level = null)
    {
        // Bepaal het pad naar het logbestand
        $logPath = $this->getLogPath();
        
        if (!file_exists($logPath)) {
            return [
                'logs' => [],
                'total' => 0
            ];
        }
        
        // Lees het logbestand
        $logs = [];
        $totalLines = 0;
        $matchedLines = 0;
        
        $handle = fopen($logPath, 'r');
        if ($handle) {
            // Eerst tellen we het totaal aantal regels en matches
            while (($line = fgets($handle)) !== false) {
                $totalLines++;
                
                // Filter op zoekterm en level
                if ($this->lineMatchesFilters($line, $search, $level)) {
                    $matchedLines++;
                }
            }
            
            // Reset de file pointer
            rewind($handle);
            
            // Bereken hoeveel regels we moeten overslaan
            $skipLines = max(0, $matchedLines - $limit - $offset);
            $currentLine = 0;
            $addedLines = 0;
            
            // Lees het bestand opnieuw en haal de juiste regels op
            while (($line = fgets($handle)) !== false) {
                if ($this->lineMatchesFilters($line, $search, $level)) {
                    $currentLine++;
                    
                    // Sla regels over voor paginering
                    if ($currentLine <= $skipLines) {
                        continue;
                    }
                    
                    // Voeg de regel toe aan de logs
                    $logs[] = $this->parseLine($line);
                    $addedLines++;
                    
                    // Stop als we genoeg regels hebben
                    if ($addedLines >= $limit) {
                        break;
                    }
                }
            }
            
            fclose($handle);
        }
        
        // Sorteer de logs op datum (nieuwste eerst)
        usort($logs, function($a, $b) {
            return $b['timestamp'] <=> $a['timestamp'];
        });
        
        return [
            'logs' => $logs,
            'total' => $matchedLines
        ];
    }
    
    /**
     * Controleer of een regel overeenkomt met de filters
     * 
     * @param string $line
     * @param string|null $search
     * @param string|null $level
     * @return bool
     */
    private function lineMatchesFilters($line, $search = null, $level = null)
    {
        // Filter op zoekterm
        if ($search !== null && stripos($line, $search) === false) {
            return false;
        }
        
        // Filter op level
        if ($level !== null) {
            $levelPattern = '';
            
            switch (strtolower($level)) {
                case 'error':
                    $levelPattern = '/(error|exception|fatal)/i';
                    break;
                case 'warning':
                    $levelPattern = '/(warning|warn)/i';
                    break;
                case 'info':
                    $levelPattern = '/(info|notice)/i';
                    break;
                case 'debug':
                    $levelPattern = '/(debug)/i';
                    break;
            }
            
            if ($levelPattern && !preg_match($levelPattern, $line)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Parse een logregel naar een gestructureerd formaat
     * 
     * @param string $line
     * @return array
     */
    private function parseLine($line)
    {
        $timestamp = '';
        $level = 'info';
        $message = $line;
        
        // Probeer de timestamp te extraheren
        if (preg_match('/^\[(.*?)\]/', $line, $matches)) {
            $timestamp = $matches[1];
            $message = substr($line, strlen($matches[0]));
        }
        
        // Probeer het level te bepalen
        if (preg_match('/(error|exception|fatal)/i', $line)) {
            $level = 'error';
        } elseif (preg_match('/(warning|warn)/i', $line)) {
            $level = 'warning';
        } elseif (preg_match('/(info|notice)/i', $line)) {
            $level = 'info';
        } elseif (preg_match('/(debug)/i', $line)) {
            $level = 'debug';
        }
        
        return [
            'timestamp' => $timestamp,
            'level' => $level,
            'message' => trim($message),
            'raw' => $line
        ];
    }
    
    /**
     * Krijg het pad naar het logbestand
     * 
     * @return string
     */
    private function getLogPath()
    {
        return ini_get('error_log') ?: '/var/log/apache2/error.log';
    }
    
    /**
     * Leeg het logbestand
     * 
     * @return bool
     */
    public function clearLogs()
    {
        $logPath = $this->getLogPath();
        
        if (file_exists($logPath) && is_writable($logPath)) {
            return file_put_contents($logPath, '') !== false;
        }
        
        return false;
    }
}
