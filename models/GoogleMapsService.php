<?php
/**
 * GoogleMapsService klasse voor het berekenen van afstanden tussen adressen
 */
class GoogleMapsService
{
    /**
     * De API key voor Google Maps
     *
     * @var string
     */
    private $apiKey;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->apiKey = Config::get('app.google_maps_api_key');
    }

    /**
     * Bereken de afstand tussen twee adressen
     *
     * @param string $addressA
     * @param string $addressB
     * @return float|null
     */
    public function calculateDistance($addressA, $addressB)
    {
        // Debug informatie
        error_log("Berekenen afstand tussen: '$addressA' en '$addressB'");

        // Voeg Nederland toe als het niet in het adres staat
        $addressA = $this->ensureCountry($addressA);
        $addressB = $this->ensureCountry($addressB);

        // Debug informatie
        error_log("Aangepaste adressen: '$addressA' en '$addressB'");

        $url = "https://maps.googleapis.com/maps/api/distancematrix/json?"
             . "units=metric"
             . "&region=nl"
             . "&language=nl"
             . "&origins=" . urlencode($addressA)
             . "&destinations=" . urlencode($addressB)
             . "&key=" . $this->apiKey;

        // Debug informatie
        error_log("Google Maps API URL: $url");

        $response = file_get_contents($url);
        $data = json_decode($response, true);

        // Debug informatie
        error_log("Google Maps API response: " . json_encode($data));

        if ($data['status'] === "OK"
            && isset($data['rows'][0]['elements'][0]['distance']['value'])
        ) {
            $meters = $data['rows'][0]['elements'][0]['distance']['value'];
            $kilometers = round($meters / 1000, 1);

            // Debug informatie
            error_log("Berekende afstand: $kilometers km");

            return $kilometers;
        } else {
            error_log("Fout bij het berekenen van de afstand: " . json_encode($data));
            return null;
        }
    }

    /**
     * Bereken de afstand tussen meerdere adressen
     *
     * @param array $addresses
     * @return float|null
     */
    public function calculateTotalDistance($addresses)
    {
        // Debug informatie
        error_log("Berekenen totale afstand voor adressen: " . json_encode($addresses));

        if (count($addresses) < 2) {
            error_log("Fout: Minder dan twee adressen ontvangen");
            return null;
        }

        $totalDistance = 0;

        for ($i = 0; $i < count($addresses) - 1; $i++) {
            $distance = $this->calculateDistance($addresses[$i], $addresses[$i + 1]);

            if ($distance === null) {
                return null;
            }

            $totalDistance += $distance;
        }

        // Debug informatie
        error_log("Totale afstand: $totalDistance km");

        return $totalDistance;
    }

    /**
     * Zorg ervoor dat het adres Nederland bevat
     *
     * @param string $address
     * @return string
     */
    private function ensureCountry($address)
    {
        if (strpos($address, 'Nederland') === false) {
            return $address . ', Nederland';
        }

        return $address;
    }
}
