<?php
/**
 * User model
 */
class User extends Model
{
    protected $table = 'users';

    /**
     * Authenticeer een gebruiker
     *
     * @param string $username
     * @param string $password
     * @return array|null
     */
    public function authenticate($username, $password)
    {
        $user = $this->whereOne(['username' => $username]);

        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }

        return null;
    }

    /**
     * Maak een nieuwe gebruiker
     *
     * @param string $username
     * @param string $password
     * @param bool $isAdmin
     * @return int
     */
    public function register($username, $password, $isAdmin = false)
    {
        $hashedPassword = password_hash($password, PASSWORD_BCRYPT);

        return $this->create([
            'username' => $username,
            'password' => $hashedPassword,
            'is_admin' => $isAdmin ? 1 : 0
        ]);
    }

    /**
     * Update het wachtwoord van een gebruiker
     *
     * @param int $userId
     * @param string $newPassword
     * @return bool
     */
    public function updatePassword($userId, $newPassword)
    {
        $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT);

        return $this->update($userId, [
            'password' => $hashedPassword
        ]);
    }

    /**
     * Stel het standaardadres in voor een gebruiker
     *
     * @param int $userId
     * @param string $defaultAddress
     * @return bool
     */
    public function setDefaultAddress($userId, $defaultAddress)
    {
        return $this->update($userId, [
            'default_address' => $defaultAddress
        ]);
    }

    /**
     * Update het standaard adres van een gebruiker
     *
     * @param int $userId
     * @param string $defaultAddress
     * @return bool
     */
    public function updateDefaultAddress($userId, $defaultAddress)
    {
        return $this->update($userId, [
            'default_address' => $defaultAddress
        ]);
    }

    /**
     * Krijg het standaardadres van een gebruiker
     *
     * @param int $userId
     * @return string|null
     */
    public function getDefaultAddress($userId)
    {
        $user = $this->find($userId);
        return $user ? $user['default_address'] : null;
    }

    /**
     * Controleer of een gebruiker een admin is
     *
     * @param int $userId
     * @return bool
     */
    public function isAdmin($userId)
    {
        $user = $this->find($userId);
        return $user && $user['is_admin'] == 1;
    }

    /**
     * Krijg de rol van een gebruiker
     *
     * @param int $userId
     * @return string
     */
    public function getUserRole($userId)
    {
        $user = $this->find($userId);
        return $user && $user['is_admin'] == 1 ? 'administrator' : 'gebruiker';
    }

    /**
     * Krijg alle gebruikers met het aantal ingediende, niet-betaalde kilometers
     *
     * @return array
     */
    public function getUsersWithPendingKilometers()
    {
        $sql = "
            SELECT u.id, u.username,
                   COALESCE(SUM(tr.total_km), 0) AS total_km
            FROM users u
            LEFT JOIN travel_records tr
                   ON u.id = tr.user_id
                   AND tr.submitted = 1
                   AND tr.paid = 0
                   AND tr.rejected = 0
            GROUP BY u.id, u.username
            ORDER BY total_km DESC
        ";

        return $this->query($sql);
    }

    /**
     * Krijg alle gebruikers met het aantal ingediende, niet-betaalde declaraties
     *
     * @return array
     */
    public function getUsersWithPendingDeclarations()
    {
        $sql = "
            SELECT u.id AS user_id, u.username,
                   COUNT(d.id) AS declaration_count,
                   SUM(d.totaal_bedrag) AS total_amount
            FROM users u
            LEFT JOIN declaraties d
                   ON u.id = d.user_id
                   AND d.submitted = 1
                   AND d.paid = 0
                   AND d.rejected = 0
            GROUP BY u.id, u.username
            ORDER BY declaration_count DESC
        ";

        return $this->query($sql);
    }
}
