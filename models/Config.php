<?php
/**
 * Config klasse voor het beheren van configuratiewaarden
 */
class Config
{
    private static $configs = [];

    /**
     * Laad een configuratiebestand
     * 
     * @param string $name
     * @return array
     */
    private static function load($name)
    {
        $path = __DIR__ . "/../config/{$name}.php";
        
        if (!file_exists($path)) {
            throw new Exception("Configuratiebestand '{$name}' niet gevonden.");
        }
        
        return require $path;
    }

    /**
     * Krijg een configuratiewaarde
     * 
     * @param string $key Configuratiepad in de vorm 'bestand.sleutel'
     * @param mixed $default Standaardwaarde als de sleutel niet bestaat
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        $parts = explode('.', $key);
        
        if (count($parts) < 2) {
            throw new Exception("Ongeldige configuratiesleutel: '{$key}'. Gebruik 'bestand.sleutel' formaat.");
        }
        
        $file = $parts[0];
        $configKey = $parts[1];
        
        // Laad de configuratie als deze nog niet is geladen
        if (!isset(self::$configs[$file])) {
            self::$configs[$file] = self::load($file);
        }
        
        // Haal de waarde op
        if (isset(self::$configs[$file][$configKey])) {
            return self::$configs[$file][$configKey];
        }
        
        // Retourneer de standaardwaarde als de sleutel niet bestaat
        return $default;
    }
}
