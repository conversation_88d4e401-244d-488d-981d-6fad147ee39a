<?php
/**
 * Asset model
 */
class Asset extends Model
{
    protected $table = 'assets';

    /**
     * Haal alle assets op met de naam van de huidige houder en het type
     *
     * @return array
     */
    public function getAllWithHolderName()
    {
        $sql = "
            SELECT a.*,
                   u.username AS current_holder_name,
                   t.name AS type_name
              FROM {$this->table} a
              LEFT JOIN users u ON a.current_holder = u.id
              LEFT JOIN asset_types t ON a.type_id = t.id
             ORDER BY a.id ASC
        ";
        return $this->query($sql);
    }

    /**
     * Haal een asset op met de UID
     *
     * @param string $uid
     * @return array|null
     */
    public function findByUID($uid)
    {
        $sql = "
            SELECT a.*,
                   u.username AS current_holder_name,
                   t.name AS type_name
              FROM {$this->table} a
              LEFT JOIN users u ON a.current_holder = u.id
              LEFT JOIN asset_types t ON a.type_id = t.id
             WHERE a.uid = :uid
             LIMIT 1
        ";
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['uid' => $uid]);
        return $stmt->fetch();
    }

    /**
     * Haal alle assets op die aan een gebruiker zijn toegewezen
     *
     * @param int $userId
     * @return array
     */
    public function findByUserId($userId)
    {
        return $this->where(['current_holder' => $userId]);
    }

    /**
     * Wijs een asset toe aan een gebruiker
     *
     * @param int $assetId
     * @param int $userId
     * @return bool
     */
    public function assignToUser($assetId, $userId)
    {
        $sql = "UPDATE {$this->table} SET current_holder = :user_id, holder_since = NOW(), status = 'uitgeleend' WHERE id = :asset_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            'user_id' => $userId,
            'asset_id' => $assetId
        ]);
    }

    /**
     * Ontkoppel een asset van een gebruiker
     *
     * @param int $assetId
     * @return bool
     */
    public function unassignFromUser($assetId)
    {
        $sql = "UPDATE {$this->table} SET current_holder = NULL, holder_since = NULL, status = 'beschikbaar' WHERE id = :asset_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute(['asset_id' => $assetId]);
    }

    /**
     * Voeg een nieuw asset toe
     *
     * @param array $data
     * @return int
     */
    public function addAsset($data)
    {
        return $this->create($data);
    }

    /**
     * Update een asset
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateAsset($id, $data)
    {
        return $this->update($id, $data);
    }

    /**
     * Verwijder een asset
     *
     * @param int $id
     * @return bool
     */
    public function deleteAsset($id)
    {
        return $this->delete($id);
    }

    /**
     * Controleer of een UID al bestaat
     *
     * @param string $uid
     * @param int|null $excludeId
     * @return bool
     */
    public function uidExists($uid, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE uid = :uid";
        $params = ['uid' => $uid];

        if ($excludeId !== null) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return (int) $stmt->fetchColumn() > 0;
    }
}
