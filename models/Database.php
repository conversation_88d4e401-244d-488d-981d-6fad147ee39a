<?php
/**
 * Database klasse voor het beheren van de database-verbinding
 */
class Database
{
    private static $instance = null;
    private $pdo;

    /**
     * Constructor - maakt een nieuwe PDO-verbinding
     */
    private function __construct()
    {
        $config = require __DIR__ . '/../config/database.php';
        
        $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
        
        try {
            $this->pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
        } catch (PDOException $e) {
            throw new Exception("Database verbinding mislukt: " . $e->getMessage());
        }
    }

    /**
     * Singleton pattern - krijg de database-instantie
     * 
     * @return Database
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }

    /**
     * Krijg de PDO-instantie
     * 
     * @return PDO
     */
    public function getConnection()
    {
        return $this->pdo;
    }

    /**
     * Voer een query uit en krijg alle resultaten
     * 
     * @param string $sql
     * @param array $params
     * @return array
     */
    public function query($sql, $params = [])
    {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }

    /**
     * Voer een query uit en krijg één resultaat
     * 
     * @param string $sql
     * @param array $params
     * @return array|null
     */
    public function queryOne($sql, $params = [])
    {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch();
    }

    /**
     * Voer een query uit zonder resultaten (INSERT, UPDATE, DELETE)
     * 
     * @param string $sql
     * @param array $params
     * @return bool
     */
    public function execute($sql, $params = [])
    {
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Krijg het laatst ingevoegde ID
     * 
     * @return string
     */
    public function lastInsertId()
    {
        return $this->pdo->lastInsertId();
    }
}
