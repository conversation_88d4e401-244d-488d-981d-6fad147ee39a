<?php
/**
 * Declaration model
 */
class Declaration extends Model
{
    protected $table = 'declaraties';

    /**
     * Voeg een nieuwe declaratie toe
     *
     * @param string $titel
     * @param float $totaalBedrag
     * @param float $bedragExclBtw
     * @param float $btwPercentage
     * @param string $productDienst
     * @param string $bestandPad
     * @param int $userId
     * @return int
     */
    public function addDeclaration($titel, $totaalBedrag, $bedragExclBtw, $btwPercentage, $productDienst, $bestandPad, $userId)
    {
        return $this->create([
            'titel' => $titel,
            'totaal_bedrag' => $totaalBedrag,
            'bedrag_excl_btw' => $bedragExclBtw,
            'btw_percentage' => $btwPercentage,
            'product_dienst' => $productDienst,
            'bestand_pad' => $bestandPad,
            'user_id' => $userId,
            'submitted' => 0
        ]);
    }

    /**
     * Update een declaratie
     *
     * @param int $id
     * @param string $titel
     * @param float $totaalBedrag
     * @param float $bedragExclBtw
     * @param float $btwPercentage
     * @param string $productDienst
     * @param string $bestandPad
     * @return bool
     */
    public function updateDeclaration($id, $titel, $totaalBedrag, $bedragExclBtw, $btwPercentage, $productDienst, $bestandPad)
    {
        return $this->update($id, [
            'titel' => $titel,
            'totaal_bedrag' => $totaalBedrag,
            'bedrag_excl_btw' => $bedragExclBtw,
            'btw_percentage' => $btwPercentage,
            'product_dienst' => $productDienst,
            'bestand_pad' => $bestandPad
        ]);
    }

    /**
     * Dien declaraties in
     *
     * @param array $ids
     * @param int $userId
     * @return bool
     */
    public function submitDeclarations($ids, $userId)
    {
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $params = array_merge($ids, [$userId]);

        $sql = "UPDATE {$this->table} SET submitted = 1, submitted_at = NOW()
                WHERE id IN ({$placeholders}) AND user_id = ? AND submitted = 0";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Markeer declaraties als betaald
     *
     * @param array $ids
     * @return bool
     */
    public function markAsPaid($ids)
    {
        $placeholders = implode(',', array_fill(0, count($ids), '?'));

        $sql = "UPDATE {$this->table} SET paid = 1, rejected = 0
                WHERE id IN ({$placeholders})";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute($ids);
    }

    /**
     * Markeer declaraties als afgekeurd
     *
     * @param array $ids
     * @return bool
     */
    public function markAsRejected($ids)
    {
        $placeholders = implode(',', array_fill(0, count($ids), '?'));

        $sql = "UPDATE {$this->table} SET submitted = 0, paid = 0, rejected = 1
                WHERE id IN ({$placeholders})";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute($ids);
    }

    /**
     * Krijg gefilterde declaraties voor een gebruiker
     *
     * @param int $userId
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @param bool $showSubmitted
     * @param bool $showPaid
     * @param bool $showRejected
     * @param bool $showOpen
     * @return array
     */
    public function getFilteredDeclarations($userId, $dateFrom = null, $dateTo = null, $showSubmitted = false, $showPaid = false, $showRejected = false, $showOpen = true)
    {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = :userId";
        $params = ['userId' => $userId];

        if ($dateFrom) {
            $sql .= " AND aanmaakdatum >= :dateFrom";
            $params['dateFrom'] = $dateFrom;
        }

        if ($dateTo) {
            $sql .= " AND aanmaakdatum <= :dateTo";
            $params['dateTo'] = $dateTo;
        }

        // Opbouw filtervoorwaarden
        $filterConditions = [];
        if ($showSubmitted) {
            $filterConditions[] = "(submitted = 1 AND paid = 0 AND rejected = 0)";
        }
        if ($showPaid) {
            $filterConditions[] = "(paid = 1)";
        }
        if ($showRejected) {
            $filterConditions[] = "(rejected = 1)";
        }
        if ($showOpen) {
            $filterConditions[] = "(submitted = 0 AND paid = 0 AND rejected = 0)";
        }

        // Als er filters zijn aangekruist, combineer met OR
        if (!empty($filterConditions)) {
            $sql .= " AND (" . implode(" OR ", $filterConditions) . ")";
        }

        $sql .= " ORDER BY aanmaakdatum DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }

    /**
     * Krijg ingediende, niet-betaalde declaraties voor een gebruiker
     *
     * @param int $userId
     * @return array
     */
    public function getPendingDeclarations($userId)
    {
        return $this->where([
            'user_id' => $userId,
            'submitted' => 1,
            'paid' => 0,
            'rejected' => 0
        ]);
    }

    /**
     * Krijg het totale uitbetaalde bedrag voor een gebruiker
     *
     * @param int $userId
     * @return float
     */
    public function getTotalPaidAmount($userId)
    {
        $sql = "SELECT SUM(totaal_bedrag) as total_paid_amount FROM {$this->table}
                WHERE user_id = ? AND paid = 1";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        $result = $stmt->fetch();

        return $result['total_paid_amount'] ? (float)$result['total_paid_amount'] : 0;
    }
}
