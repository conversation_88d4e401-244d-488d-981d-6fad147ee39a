<?php
/**
 * AdminDeclarationController
 */
class AdminDeclarationController extends Controller
{
    /**
     * De Declaration model
     * 
     * @var Declaration
     */
    private $declarationModel;
    
    /**
     * De User model
     * 
     * @var User
     */
    private $userModel;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->declarationModel = new Declaration();
        $this->userModel = new User();
    }
    
    /**
     * Toon een lijst van gebruikers met ingediende declaraties
     * 
     * @return string
     */
    public function index()
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();
        
        // Haal gebruikers op met ingediende, niet-betaalde declaraties
        $users = $this->userModel->getUsersWithPendingDeclarations();
        
        // Render de view
        return $this->viewWithLayout('admin/declarations/index', [
            'users' => $users,
            'message' => $this->getMessage()
        ]);
    }
    
    /**
     * Toon declaraties van een specifieke gebruiker
     * 
     * @param int $userId
     * @return string
     */
    public function show($userId)
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();
        
        // Haal de gebruiker op
        $user = $this->userModel->find($userId);
        
        if (!$user) {
            $this->setMessage("Gebruiker niet gevonden.");
            $this->redirect('admin/declarations');
        }
        
        // Haal de declaraties op voor de gebruiker
        $declarations = $this->declarationModel->getPendingDeclarations($userId);
        
        // Bereken het totaalbedrag voor de weergegeven declaraties
        $totalAmount = array_reduce($declarations, function($sum, $declaration) {
            return $sum + $declaration['totaal_bedrag'];
        }, 0);
        
        // Render de view
        return $this->viewWithLayout('admin/declarations/show', [
            'user' => $user,
            'declarations' => $declarations,
            'totalAmount' => $totalAmount,
            'message' => $this->getMessage()
        ]);
    }
    
    /**
     * Update de status van declaraties
     * 
     * @param int $userId
     * @return void
     */
    public function updateStatus($userId)
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();
        
        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            $this->redirect("admin/declarations/user/$userId");
        }
        
        // Haal de geselecteerde records en statussen op
        $recordIds = $this->post('record_ids', []);
        $statuses = $this->post('status', []);
        
        if (empty($recordIds)) {
            $this->setMessage("Geen declaraties geselecteerd.");
            $this->redirect("admin/declarations/user/$userId");
        }
        
        // Verwerk de statussen
        $paidIds = [];
        $rejectedIds = [];
        
        foreach ($recordIds as $recordId) {
            if (isset($statuses[$recordId])) {
                $status = $statuses[$recordId];
                
                if ($status === 'paid') {
                    $paidIds[] = $recordId;
                } elseif ($status === 'rejected') {
                    $rejectedIds[] = $recordId;
                }
            }
        }
        
        // Update de statussen in de database
        $success = true;
        
        if (!empty($paidIds)) {
            $success = $success && $this->declarationModel->markAsPaid($paidIds);
        }
        
        if (!empty($rejectedIds)) {
            $success = $success && $this->declarationModel->markAsRejected($rejectedIds);
        }
        
        if ($success) {
            $this->setMessage("Status van declaraties succesvol bijgewerkt.");
        } else {
            $this->setMessage("Er is een fout opgetreden bij het bijwerken van de status.");
        }
        
        $this->redirect("admin/declarations/user/$userId");
    }
}
