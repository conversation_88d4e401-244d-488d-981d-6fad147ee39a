<?php
/**
 * AuthController
 */
class AuthController extends Controller
{
    /**
     * Toon het inlogformulier
     *
     * @return string
     */
    public function showLoginForm()
    {
        // Als de gebruiker al is ingelogd, redirect naar de homepagina
        if (Auth::check()) {
            return $this->redirect('');
        }

        // Render de view
        return $this->viewWithLayout('auth/login', [
            'message' => $this->getMessage()
        ]);
    }

    /**
     * Verwerk het inlogformulier
     *
     * @return void
     */
    public function login()
    {
        // Als de gebruiker al is ingelogd, redirect naar de homepagina
        if (Auth::check()) {
            return $this->redirect('');
        }

        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            return $this->redirect('login');
        }

        // Haal de inloggegevens op
        $username = $this->post('username');
        $password = $this->post('password');

        // Controleer of de inloggegevens zijn ingevuld
        if (empty($username) || empty($password)) {
            $this->setMessage('Vul alle velden in.');
            return $this->redirect('login');
        }

        // Probeer in te loggen
        if (Auth::attempt($username, $password)) {
            Auth::log("Gebruiker succesvol ingelogd", 'info');
            return $this->redirect('/index');
        }

        // Inloggen mislukt
        Auth::log("Mislukte inlogpoging voor gebruiker: $username", 'warning');
        $this->setMessage('Ongeldige gebruikersnaam of wachtwoord.');
        return $this->redirect('login');
    }

    /**
     * Log de gebruiker uit
     *
     * @return void
     */
    public function logout()
    {
        $userId = Auth::id();
        $username = Auth::user()['username'];

        Auth::logout();

        Auth::log("Gebruiker $userId:$username uitgelogd", 'info');
        $this->setMessage('Je bent uitgelogd.');
        return $this->redirect('login');
    }
}
