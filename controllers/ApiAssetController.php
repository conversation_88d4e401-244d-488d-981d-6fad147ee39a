<?php
/**
 * ApiAssetController
 */
class ApiAssetController extends Controller
{
    /**
     * De Asset model
     * 
     * @var Asset
     */
    private $assetModel;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->assetModel = new Asset();
        
        // Controleer of de gebruiker is ingelogd
        if (!isset($_SESSION['user_id'])) {
            header('Content-Type: application/json');
            echo json_encode(['error' => 'Niet ingelogd']);
            exit;
        }
    }
    
    /**
     * Zoek een asset op UID
     * 
     * @return void
     */
    public function findByUID()
    {
        $uid = $this->get('uid');
        
        if (!$uid) {
            header('Content-Type: application/json');
            echo json_encode(['error' => 'Geen UID opgegeven']);
            exit;
        }
        
        $asset = $this->assetModel->findByUID($uid);
        
        header('Content-Type: application/json');
        echo json_encode($asset);
    }
}
