<?php
/**
 * TravelRecordController
 */
class TravelRecordController extends Controller
{
    /**
     * De TravelRecord model
     *
     * @var TravelRecord
     */
    private $travelRecordModel;

    /**
     * De Notification model
     *
     * @var Notification
     */
    private $notificationModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->travelRecordModel = new TravelRecord();
        $this->notificationModel = new Notification();
    }

    /**
     * Toon een lijst van reiskostenrecords
     *
     * @return string
     */
    public function index()
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();

        // Haal filters op uit de GET parameters
        $dateFrom = $this->get('date_from', '');
        $dateTo = $this->get('date_to', '');
        $projectNumber = $this->get('project_number', '');
        $showSubmitted = $this->get('show_submitted', false);
        $showPaid = $this->get('show_paid', false);
        $showRejected = $this->get('show_rejected', false);
        $showOpen = $this->get('show_open', true);

        // Haal reiskostenrecords op met de toegepaste filters
        $records = $this->travelRecordModel->getFilteredTravelRecords(
            Auth::id(),
            $dateFrom,
            $dateTo,
            $projectNumber,
            $showSubmitted,
            $showPaid,
            $showRejected,
            $showOpen
        );

        // Bereken het totaal aantal kilometers
        $totalKm = $this->travelRecordModel->calculateTotalKm($records);

        // Render de view
        return $this->viewWithLayout('travel_records/index', [
            'records' => $records,
            'totalKm' => $totalKm,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'projectNumber' => $projectNumber,
            'showSubmitted' => $showSubmitted,
            'showPaid' => $showPaid,
            'showRejected' => $showRejected,
            'showOpen' => $showOpen,
            'message' => $this->getMessage()
        ]);
    }

    /**
     * Toon het formulier om een nieuwe reiskostenregel toe te voegen
     *
     * @return string
     */
    public function create()
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();

        // Haal het standaardadres van de gebruiker op
        $userModel = new User();
        $defaultAddress = $userModel->getDefaultAddress(Auth::id());

        // Render de view
        return $this->viewWithLayout('travel_records/create', [
            'defaultAddress' => $defaultAddress
        ]);
    }

    /**
     * Sla een nieuwe reiskostenregel op
     *
     * @return void
     */
    public function store()
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();

        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            $this->redirect('travel_records/create');
        }

        // Haal de gegevens op uit het formulier
        $date = $this->post('date', date('Y-m-d'));
        $projectNumber = $this->post('project_number', '');
        $isCommute = $this->post('is_commute', false);
        $addressA = $this->post('address_a', '');
        $addressB = $this->post('address_b', '');
        $extraAddresses = $this->post('extra_addresses', '');
        $singleTripKm = $this->post('single_trip_km', 0);
        $totalKm = $this->post('total_km', 0);
        $returnTrip = $this->post('return_trip', false);

        // Als het woon-werkverkeer is, zet dan het projectnummer op "Woon-werkverkeer"
        if ($isCommute) {
            $projectNumber = "Woon-werkverkeer";
        }

        // Verwerk de extra adressen
        if (!empty($extraAddresses)) {
            try {
                $extraAddresses = json_decode($extraAddresses, true);
                if (!is_array($extraAddresses)) {
                    $extraAddresses = [];
                }
            } catch (Exception) {
                $extraAddresses = [];
            }
        } else {
            $extraAddresses = [];
        }

        // Gebruik de totale kilometers uit het formulier
        // De berekening is al gedaan in JavaScript

        // Voeg de reiskostenregel toe aan de database
        $recordId = $this->travelRecordModel->addTravelRecord(
            Auth::id(),
            $date,
            $projectNumber,
            $addressA,
            $addressB,
            $extraAddresses,
            $singleTripKm,
            $totalKm,
            $returnTrip
        );

        if ($recordId) {
            $this->setMessage("Reiskostenregel succesvol toegevoegd!");
            $this->redirect('travel_records');
        } else {
            $this->setMessage("Er is een fout opgetreden bij het toevoegen van de reiskostenregel.");
            $this->redirect('travel_records/create');
        }
    }

    /**
     * Toon het formulier om een reiskostenregel te bewerken
     *
     * @param int $id
     * @return string
     */
    public function edit($id)
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();

        // Haal de reiskostenregel op
        $record = $this->travelRecordModel->find($id);

        // Controleer of de reiskostenregel bestaat en van de huidige gebruiker is
        if (!$record || $record['user_id'] != Auth::id()) {
            $this->setMessage("Reiskostenregel niet gevonden of je hebt geen toegang.");
            $this->redirect('travel_records');
        }

        // Controleer of de reiskostenregel al is ingediend
        if ($record['submitted']) {
            $this->setMessage("Je kunt geen ingediende reiskostenregel bewerken.");
            $this->redirect('travel_records');
        }

        // Render de view
        return $this->viewWithLayout('travel_records/edit', [
            'record' => $record
        ]);
    }

    /**
     * Update een reiskostenregel
     *
     * @param int $id
     * @return void
     */
    public function update($id)
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();

        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            $this->redirect("travel_records/edit/$id");
        }

        // Haal de reiskostenregel op
        $record = $this->travelRecordModel->find($id);

        // Controleer of de reiskostenregel bestaat en van de huidige gebruiker is
        if (!$record || $record['user_id'] != Auth::id()) {
            $this->setMessage("Reiskostenregel niet gevonden of je hebt geen toegang.");
            $this->redirect('travel_records');
        }

        // Controleer of de reiskostenregel al is ingediend
        if ($record['submitted']) {
            $this->setMessage("Je kunt geen ingediende reiskostenregel bewerken.");
            $this->redirect('travel_records');
        }

        // Haal de gegevens op uit het formulier
        $date = $this->post('date', date('Y-m-d'));
        $projectNumber = $this->post('project_number', '');
        $isCommute = $this->post('is_commute', false);
        $addressA = $this->post('address_a', '');
        $addressB = $this->post('address_b', '');
        $extraAddresses = $this->post('extra_addresses', '');
        $singleTripKm = $this->post('single_trip_km', 0);
        $totalKm = $this->post('total_km', 0);
        $returnTrip = $this->post('return_trip', false);

        // Als het woon-werkverkeer is, zet dan het projectnummer op "Woon-werkverkeer"
        if ($isCommute) {
            $projectNumber = "Woon-werkverkeer";
        }

        // Verwerk de extra adressen
        if (!empty($extraAddresses)) {
            try {
                $extraAddresses = json_decode($extraAddresses, true);
                if (!is_array($extraAddresses)) {
                    $extraAddresses = [];
                }
            } catch (Exception) {
                $extraAddresses = [];
            }
        } else {
            $extraAddresses = [];
        }

        // Update de reiskostenregel in de database
        $result = $this->travelRecordModel->updateTravelRecord(
            $id,
            $date,
            $projectNumber,
            $addressA,
            $addressB,
            $extraAddresses,
            $singleTripKm,
            $totalKm,
            $returnTrip
        );

        if ($result) {
            $this->setMessage("Reiskostenregel succesvol bijgewerkt!");
            $this->redirect('travel_records');
        } else {
            $this->setMessage("Er is een fout opgetreden bij het bijwerken van de reiskostenregel.");
            $this->redirect("travel_records/edit/$id");
        }
    }

    /**
     * Verwijder een reiskostenregel
     *
     * @param int $id
     * @return void
     */
    public function destroy($id)
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();

        // Haal de reiskostenregel op
        $record = $this->travelRecordModel->find($id);

        // Controleer of de reiskostenregel bestaat en van de huidige gebruiker is
        if (!$record || $record['user_id'] != Auth::id()) {
            $this->setMessage("Reiskostenregel niet gevonden of je hebt geen toegang.");
            $this->redirect('travel_records');
        }

        // Controleer of de reiskostenregel al is ingediend
        if ($record['submitted']) {
            $this->setMessage("Je kunt geen ingediende reiskostenregel verwijderen.");
            $this->redirect('travel_records');
        }

        // Verwijder de reiskostenregel uit de database
        $result = $this->travelRecordModel->delete($id);

        if ($result) {
            $this->setMessage("Reiskostenregel succesvol verwijderd.");
        } else {
            $this->setMessage("Er is een fout opgetreden bij het verwijderen van de reiskostenregel.");
        }

        $this->redirect('travel_records');
    }

    /**
     * Dien reiskostenregels in
     *
     * @return void
     */
    public function submit()
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();

        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            $this->redirect('travel_records');
        }

        // Controleer of er records zijn geselecteerd
        $recordIds = $this->post('record_ids', []);

        if (empty($recordIds)) {
            $this->setMessage("Geen reiskostenregels geselecteerd om in te dienen.");
            $this->redirect('travel_records');
        }

        // Dien de reiskostenregels in
        $result = $this->travelRecordModel->submitTravelRecords($recordIds, Auth::id());

        if ($result) {
            // Maak notificatie voor admins
            $userModel = new User();
            $user = $userModel->find(Auth::id());
            $username = $user['username'] ?? 'Onbekend';

            // Bereken totaal aantal kilometers van ingediende records
            $totalKm = 0;
            foreach ($recordIds as $recordId) {
                $record = $this->travelRecordModel->find($recordId);
                if ($record) {
                    $totalKm += $record['total_km'];
                }
            }

            $this->notificationModel->createNotification(
                Auth::id(),
                'travel_submitted',
                "Reiskosten ingediend door {$username} (" . count($recordIds) . " record(s), totaal {$totalKm} km)",
                [
                    'travel_record_ids' => $recordIds,
                    'total_km' => $totalKm,
                    'count' => count($recordIds)
                ]
            );

            // Log voor audit trail
            Auth::log("Reiskosten ingediend: " . count($recordIds) . " record(s) met IDs: " . implode(', ', $recordIds), 'info');

            $this->setMessage("Reiskostenregels succesvol ingediend!");
        } else {
            // Log de fout
            Auth::log("Fout bij indienen reiskosten: IDs " . implode(', ', $recordIds), 'error');

            $this->setMessage("Er is een fout opgetreden bij het indienen van de reiskostenregels.");
        }

        $this->redirect('travel_records');
    }
}
