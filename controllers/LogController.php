<?php
/**
 * LogController
 */
class LogController extends Controller
{
    /**
     * De LogModel
     * 
     * @var LogModel
     */
    private $logModel;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->logModel = new LogModel();
    }
    
    /**
     * Toon de logviewer
     * 
     * @return string
     */
    public function index()
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();
        
        // Haal de query parameters op
        $page = max(1, (int) $this->get('page', 1));
        $limit = (int) $this->get('limit', 50);
        $search = $this->get('search', '');
        $level = $this->get('level', '');
        
        // Bereken de offset
        $offset = ($page - 1) * $limit;
        
        // Haal de logs op
        $result = $this->logModel->getLogs($limit, $offset, $search, $level);
        $logs = $result['logs'];
        $total = $result['total'];
        
        // Bereken het aantal pagina's
        $totalPages = ceil($total / $limit);
        
        // Render de view
        return $this->viewWithLayout('logs/index', [
            'logs' => $logs,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'totalPages' => $totalPages,
            'search' => $search,
            'level' => $level,
            'message' => $this->getMessage()
        ]);
    }
    
    /**
     * Leeg het logbestand
     * 
     * @return void
     */
    public function clear()
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();
        
        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            $this->redirect('logs');
        }
        
        // Leeg het logbestand
        $success = $this->logModel->clearLogs();
        
        if ($success) {
            $this->setMessage('Logbestand succesvol geleegd.');
        } else {
            $this->setMessage('Er is een fout opgetreden bij het legen van het logbestand.');
        }
        
        $this->redirect('logs');
    }
    
    /**
     * Schrijf een testbericht naar het logbestand
     * 
     * @return void
     */
    public function test()
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();
        
        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            $this->redirect('logs');
        }
        
        // Schrijf een testbericht naar het logbestand
        $level = $this->post('level', 'info');
        $message = $this->post('message', 'Dit is een testbericht');
        
        switch ($level) {
            case 'error':
                error_log("ERROR: $message");
                break;
            case 'warning':
                error_log("WARNING: $message");
                break;
            case 'info':
                error_log("INFO: $message");
                break;
            case 'debug':
                error_log("DEBUG: $message");
                break;
            default:
                error_log($message);
                break;
        }
        
        $this->setMessage('Testbericht succesvol geschreven naar het logbestand.');
        $this->redirect('logs');
    }
}
