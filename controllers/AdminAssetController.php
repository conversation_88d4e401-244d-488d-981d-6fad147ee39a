<?php
/**
 * AdminAssetController
 */
class AdminAssetController extends Controller
{
    /**
     * De Asset model
     *
     * @var Asset
     */
    private $assetModel;

    /**
     * De AssetLog model
     *
     * @var AssetLog
     */
    private $assetLogModel;

    /**
     * De AssetType model
     *
     * @var AssetType
     */
    private $assetTypeModel;

    /**
     * De User model
     *
     * @var User
     */
    private $userModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->assetModel = new Asset();
        $this->assetLogModel = new AssetLog();
        $this->assetTypeModel = new AssetType();
        $this->userModel = new User();

        // Controleer of de gebruiker is ingelogd en admin is
        if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
            $this->redirect('login');
        }
    }

    /**
     * Toon alle assets
     *
     * @return string
     */
    public function index()
    {
        $assets = $this->assetModel->getAllWithHolderName();

        return $this->viewWithLayout('admin/assets/index', [
            'assets' => $assets,
            'pageTitle' => 'Asset beheer'
        ]);
    }

    /**
     * Toon het formulier om een nieuw asset toe te voegen
     *
     * @return string
     */
    public function create()
    {
        $types = $this->assetTypeModel->getAllTypes();

        return $this->viewWithLayout('admin/assets/create', [
            'types' => $types,
            'pageTitle' => 'Asset toevoegen'
        ]);
    }

    /**
     * Sla een nieuw asset op
     *
     * @return void
     */
    public function store()
    {
        if (!$this->isPost()) {
            $this->redirect('admin/assets');
        }

        $uid = trim($this->post('uid'));
        $name = trim($this->post('name'));

        if (empty($uid) || empty($name)) {
            $this->setMessage('UID en Naam zijn verplichte velden', 'error');
            $this->redirect('admin/assets/create');
        }

        // Controleer of de UID al bestaat
        if ($this->assetModel->uidExists($uid)) {
            $this->setMessage('De UID "' . $uid . '" bestaat al. Kies een andere.', 'error');
            $this->redirect('admin/assets/create');
        }

        // Verzamel alle gegevens
        $data = [
            'uid' => $uid,
            'name' => $name,
            'brand' => $this->post('brand'),
            'model' => $this->post('model'),
            'type_id' => $this->post('type_id'),
            'serial_number' => $this->post('serial_number'),
            'purchase_date' => $this->post('purchase_date') ?: null,
            'purchase_value' => $this->post('purchase_value') ?: null,
            'quantity' => $this->post('quantity') ?: 1,
            'product_link' => $this->post('product_link'),
            'status' => $this->post('status') ?: 'beschikbaar',
            'comments' => $this->post('comments'),
            'visibility' => (string)($this->post('visibility') ?: 'public'),
            'user_group' => $this->post('user_group')
        ];

        // Voeg het asset toe
        $this->assetModel->addAsset($data);

        $this->setMessage('Asset "' . $uid . '" succesvol toegevoegd');
        $this->redirect('admin/assets');
    }

    /**
     * Toon het formulier om een asset te bewerken
     *
     * @param int $id
     * @return string
     */
    public function edit($id)
    {
        $asset = $this->assetModel->find($id);

        if (!$asset) {
            $this->setMessage('Asset niet gevonden', 'error');
            $this->redirect('admin/assets');
        }

        $types = $this->assetTypeModel->getAllTypes();

        return $this->viewWithLayout('admin/assets/edit', [
            'asset' => $asset,
            'types' => $types,
            'pageTitle' => 'Asset bewerken'
        ]);
    }

    /**
     * Update een asset
     *
     * @param int $id
     * @return void
     */
    public function update($id)
    {
        if (!$this->isPost()) {
            $this->redirect('admin/assets');
        }

        $asset = $this->assetModel->find($id);

        if (!$asset) {
            $this->setMessage('Asset niet gevonden', 'error');
            $this->redirect('admin/assets');
        }

        $uid = trim($this->post('uid'));
        $name = trim($this->post('name'));

        if (empty($uid) || empty($name)) {
            $this->setMessage('UID en Naam zijn verplichte velden', 'error');
            $this->redirect('admin/assets/edit/' . $id);
        }

        // Controleer of de UID al bestaat (behalve voor dit asset)
        if ($this->assetModel->uidExists($uid, $id)) {
            $this->setMessage('De UID "' . $uid . '" bestaat al. Kies een andere.', 'error');
            $this->redirect('admin/assets/edit/' . $id);
        }

        // Verzamel alle gegevens
        $data = [
            'uid' => $uid,
            'name' => $name,
            'brand' => $this->post('brand'),
            'model' => $this->post('model'),
            'type_id' => $this->post('type_id'),
            'serial_number' => $this->post('serial_number'),
            'purchase_date' => $this->post('purchase_date') ?: null,
            'purchase_value' => $this->post('purchase_value') ?: null,
            'quantity' => $this->post('quantity') ?: 1,
            'product_link' => $this->post('product_link'),
            'status' => $this->post('status') ?: 'beschikbaar',
            'comments' => $this->post('comments'),
            'visibility' => (string)($this->post('visibility') ?: 'public'),
            'user_group' => $this->post('user_group'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update het asset
        $this->assetModel->updateAsset($id, $data);

        $this->setMessage('Asset "' . $uid . '" succesvol bijgewerkt');
        $this->redirect('admin/assets');
    }

    /**
     * Verwijder een asset
     *
     * @param int $id
     * @return void
     */
    public function destroy($id)
    {
        $asset = $this->assetModel->find($id);

        if (!$asset) {
            $this->setMessage('Asset niet gevonden', 'error');
            $this->redirect('admin/assets');
        }

        // Verwijder het asset
        $this->assetModel->deleteAsset($id);

        $this->setMessage('Asset succesvol verwijderd');
        $this->redirect('admin/assets');
    }

    /**
     * Toon het formulier om een asset toe te wijzen aan een gebruiker
     *
     * @return string
     */
    public function manage()
    {
        $assets = $this->assetModel->getAllWithHolderName();
        $users = $this->userModel->all();

        return $this->viewWithLayout('admin/assets/manage', [
            'assets' => $assets,
            'users' => $users,
            'pageTitle' => 'Asset toewijzen'
        ]);
    }

    /**
     * Wijs een asset toe aan een gebruiker
     *
     * @return void
     */
    public function assignToUser()
    {
        if (!$this->isPost()) {
            $this->redirect('admin/assets/manage');
        }

        $assetId = $this->post('asset_id');
        $userId = $this->post('user_id');
        $comments = $this->post('comments');

        if (!$assetId || !$userId) {
            $this->setMessage('Asset en gebruiker zijn verplichte velden', 'error');
            $this->redirect('admin/assets/manage');
        }

        // Wijs het asset toe aan de gebruiker
        $this->assetModel->assignToUser($assetId, $userId);

        // Voeg een log toe
        $this->assetLogModel->addLog($assetId, $userId, 'uitgeleend', $comments);

        $this->setMessage('Asset succesvol toegewezen');
        $this->redirect('admin/assets/manage');
    }

    /**
     * Ontkoppel een asset van een gebruiker
     *
     * @return void
     */
    public function unassignFromUser()
    {
        if (!$this->isPost()) {
            $this->redirect('admin/assets/manage');
        }

        $assetId = $this->post('asset_id');

        if (!$assetId) {
            $this->setMessage('Geen asset geselecteerd', 'error');
            $this->redirect('admin/assets/manage');
        }

        // Haal de huidige log op (voor debugging doeleinden)
        // $currentLog = $this->assetLogModel->getCurrentLog($assetId);

        // Ontkoppel het asset van de gebruiker
        $this->assetModel->unassignFromUser($assetId);

        // Update de einddatum van de log
        $this->assetLogModel->updateEndDate($assetId);

        $this->setMessage('Asset succesvol ontkoppeld');
        $this->redirect('admin/assets/manage');
    }
}
