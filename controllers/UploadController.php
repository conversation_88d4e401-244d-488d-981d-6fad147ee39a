<?php
/**
 * Upload Controller
 * 
 * Handles secure file serving for uploaded files
 */
class UploadController extends Controller
{
    private $declarationModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->declarationModel = new Declaration();
    }
    
    /**
     * Serve declaration files securely
     * 
     * @param string $filename
     * @return void
     */
    public function serveDeclarationFile($filename)
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();
        
        // Sanitize filename to prevent directory traversal
        $filename = basename($filename);
        
        // Construct file path
        $uploadsDir = Config::get('app.uploads_dir') . '/declaraties';
        $filePath = $uploadsDir . '/' . $filename;
        
        // Check if file exists
        if (!file_exists($filePath)) {
            http_response_code(404);
            echo "Bestand niet gevonden.";
            return;
        }
        
        // Security check: verify the file belongs to a valid declaration
        if (!$this->isValidDeclarationFile($filename)) {
            http_response_code(403);
            echo "Toegang geweigerd.";
            return;
        }
        
        // Additional security: check user permissions
        if (!$this->hasFileAccess($filename)) {
            http_response_code(403);
            echo "Geen toegang tot dit bestand.";
            return;
        }
        
        // Get file info
        $fileInfo = pathinfo($filePath);
        $mimeType = $this->getMimeType($fileInfo['extension']);
        
        // Set appropriate headers
        header('Content-Type: ' . $mimeType);
        header('Content-Length: ' . filesize($filePath));
        header('Content-Disposition: inline; filename="' . $filename . '"');
        header('Cache-Control: private, max-age=3600');
        header('X-Content-Type-Options: nosniff');
        
        // Output file
        readfile($filePath);
        exit;
    }
    
    /**
     * Check if the file belongs to a valid declaration
     * 
     * @param string $filename
     * @return bool
     */
    private function isValidDeclarationFile($filename)
    {
        try {
            // Check if the file is referenced in any declaration
            $sql = "SELECT COUNT(*) as count FROM declaraties WHERE bestand_pad LIKE ?";
            $stmt = $this->declarationModel->getDb()->prepare($sql);
            $stmt->execute(['%' . $filename]);
            $result = $stmt->fetch();
            
            return $result['count'] > 0;
        } catch (Exception $e) {
            error_log("Error checking declaration file: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if the current user has access to the file
     * 
     * @param string $filename
     * @return bool
     */
    private function hasFileAccess($filename)
    {
        try {
            $currentUserId = Auth::id();
            $isAdmin = Auth::isAdmin();
            
            // Admins can access all files
            if ($isAdmin) {
                return true;
            }
            
            // Regular users can only access their own files
            $sql = "SELECT user_id FROM declaraties WHERE bestand_pad LIKE ? LIMIT 1";
            $stmt = $this->declarationModel->getDb()->prepare($sql);
            $stmt->execute(['%' . $filename]);
            $result = $stmt->fetch();
            
            if ($result) {
                return $result['user_id'] == $currentUserId;
            }
            
            return false;
        } catch (Exception $e) {
            error_log("Error checking file access: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get MIME type based on file extension
     * 
     * @param string $extension
     * @return string
     */
    private function getMimeType($extension)
    {
        $mimeTypes = [
            'pdf' => 'application/pdf',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp'
        ];
        
        $extension = strtolower($extension);
        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
    
    /**
     * Serve travel expense files (for future use)
     * 
     * @param string $filename
     * @return void
     */
    public function serveTravelFile($filename)
    {
        // Similar implementation for travel expense files
        // This can be implemented when travel expenses support file uploads
        
        Auth::requireLogin();
        
        $filename = basename($filename);
        $uploadsDir = Config::get('app.uploads_dir') . '/reiskosten';
        $filePath = $uploadsDir . '/' . $filename;
        
        if (!file_exists($filePath)) {
            http_response_code(404);
            echo "Bestand niet gevonden.";
            return;
        }
        
        // Add similar security checks as above
        
        $fileInfo = pathinfo($filePath);
        $mimeType = $this->getMimeType($fileInfo['extension']);
        
        header('Content-Type: ' . $mimeType);
        header('Content-Length: ' . filesize($filePath));
        header('Content-Disposition: inline; filename="' . $filename . '"');
        header('Cache-Control: private, max-age=3600');
        header('X-Content-Type-Options: nosniff');
        
        readfile($filePath);
        exit;
    }
}
?>
