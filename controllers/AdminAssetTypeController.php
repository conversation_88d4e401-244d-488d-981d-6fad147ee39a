<?php
/**
 * AdminAssetTypeController
 */
class AdminAssetTypeController extends Controller
{
    /**
     * De AssetType model
     * 
     * @var AssetType
     */
    private $assetTypeModel;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->assetTypeModel = new AssetType();
        
        // Controleer of de gebruiker is ingelogd en admin is
        if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
            $this->redirect('login');
        }
    }
    
    /**
     * Toon alle asset types
     * 
     * @return string
     */
    public function index()
    {
        $types = $this->assetTypeModel->getAllTypes();
        
        return $this->viewWithLayout('admin/asset_types/index', [
            'types' => $types,
            'pageTitle' => 'Asset types beheren'
        ]);
    }
    
    /**
     * Toon het formulier om een nieuw asset type toe te voegen
     * 
     * @return string
     */
    public function create()
    {
        return $this->viewWithLayout('admin/asset_types/create', [
            'pageTitle' => 'Asset type toevoegen'
        ]);
    }
    
    /**
     * Sla een nieuw asset type op
     * 
     * @return void
     */
    public function store()
    {
        if (!$this->isPost()) {
            $this->redirect('admin/asset_types');
        }
        
        $name = trim($this->post('name'));
        
        if (empty($name)) {
            $this->setError('Naam is een verplicht veld');
            $this->redirect('admin/asset_types/create');
        }
        
        // Voeg het asset type toe
        $this->assetTypeModel->addType($name);
        
        $this->setMessage('Asset type "' . $name . '" succesvol toegevoegd');
        $this->redirect('admin/asset_types');
    }
    
    /**
     * Toon het formulier om een asset type te bewerken
     * 
     * @param int $id
     * @return string
     */
    public function edit($id)
    {
        $type = $this->assetTypeModel->find($id);
        
        if (!$type) {
            $this->setError('Asset type niet gevonden');
            $this->redirect('admin/asset_types');
        }
        
        return $this->viewWithLayout('admin/asset_types/edit', [
            'type' => $type,
            'pageTitle' => 'Asset type bewerken'
        ]);
    }
    
    /**
     * Update een asset type
     * 
     * @param int $id
     * @return void
     */
    public function update($id)
    {
        if (!$this->isPost()) {
            $this->redirect('admin/asset_types');
        }
        
        $type = $this->assetTypeModel->find($id);
        
        if (!$type) {
            $this->setError('Asset type niet gevonden');
            $this->redirect('admin/asset_types');
        }
        
        $name = trim($this->post('name'));
        
        if (empty($name)) {
            $this->setError('Naam is een verplicht veld');
            $this->redirect('admin/asset_types/edit/' . $id);
        }
        
        // Update het asset type
        $this->assetTypeModel->updateType($id, $name);
        
        $this->setMessage('Asset type "' . $name . '" succesvol bijgewerkt');
        $this->redirect('admin/asset_types');
    }
    
    /**
     * Verwijder een asset type
     * 
     * @param int $id
     * @return void
     */
    public function destroy($id)
    {
        $type = $this->assetTypeModel->find($id);
        
        if (!$type) {
            $this->setError('Asset type niet gevonden');
            $this->redirect('admin/asset_types');
        }
        
        // Controleer of het asset type in gebruik is
        if ($this->assetTypeModel->isTypeInUse($id)) {
            $this->setError('Dit asset type is in gebruik en kan niet worden verwijderd');
            $this->redirect('admin/asset_types');
        }
        
        // Verwijder het asset type
        $this->assetTypeModel->deleteType($id);
        
        $this->setMessage('Asset type succesvol verwijderd');
        $this->redirect('admin/asset_types');
    }
}
