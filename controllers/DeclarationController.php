<?php
/**
 * DeclarationController
 */
class DeclarationController extends Controller
{
    /**
     * De Declaration model
     *
     * @var Declaration
     */
    private $declarationModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->declarationModel = new Declaration();
    }

    /**
     * Toon een lijst van declaraties
     *
     * @return string
     */
    public function index()
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();

        // Haal filters op uit de GET parameters
        $dateFrom = $this->get('date_from', '');
        $dateTo = $this->get('date_to', '');
        $showSubmitted = $this->get('show_submitted', false);
        $showPaid = $this->get('show_paid', false);
        $showRejected = $this->get('show_rejected', false);
        $showOpen = $this->get('show_open', true);

        // Haal declaraties op met de toegepaste filters
        $records = $this->declarationModel->getFilteredDeclarations(
            Auth::id(),
            $dateFrom,
            $dateTo,
            $showSubmitted,
            $showPaid,
            $showRejected,
            $showOpen
        );

        // Render de view
        return $this->viewWithLayout('declarations/index', [
            'records' => $records,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'showSubmitted' => $showSubmitted,
            'showPaid' => $showPaid,
            'showRejected' => $showRejected,
            'showOpen' => $showOpen,
            'message' => $this->getMessage()
        ]);
    }

    /**
     * Toon het formulier om een nieuwe declaratie toe te voegen
     *
     * @return string
     */
    public function create()
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();

        // Render de view
        return $this->viewWithLayout('declarations/create');
    }

    /**
     * Sla een nieuwe declaratie op
     *
     * @return void
     */
    public function store()
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();

        // Extra validatie: controleer of de gebruiker bestaat in de database
        $currentUser = Auth::user();
        if (!$currentUser) {
            // Sessie is corrupt, log uit en redirect naar login
            Auth::logout();
            $this->setMessage("Uw sessie is verlopen. Log opnieuw in.");
            $this->redirect('login');
        }

        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            $this->redirect('declarations/create');
        }

        // Haal de gegevens op uit het formulier
        $titel = $this->post('titel', '');
        $totaalBedrag = $this->post('totaal_bedrag', 0);
        $bedragExclBtw = $this->post('bedrag_excl_btw', 0);
        $btwPercentage = $this->post('btw_percentage', 21.00);
        $productDienst = $this->post('product_dienst', '');
        $bestandPad = null;

        // Validatie: bestand upload is verplicht
        if (!$this->hasFile('bon_factuur')) {
            $this->setMessage("Het uploaden van een bon/factuur is verplicht.");
            $this->redirect('declarations/create');
        }

        // Verwerk bestandsupload (verplicht)
        $uploadsDir = Config::get('app.uploads_dir') . '/declaraties';

        // Maak de uploads directory als deze nog niet bestaat
        if (!file_exists($uploadsDir)) {
            mkdir($uploadsDir, 0755, true);
        }

        // Valideer bestandstype (alleen afbeeldingen)
        $file = $this->file('bon_factuur');
        $tempName = $file['tmp_name'];
        $fileName = basename($file['name']);
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        // Toegestane afbeelding extensies
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (!in_array($fileExt, $allowedExtensions)) {
            $this->setMessage("Alleen afbeeldingen zijn toegestaan (JPG, JPEG, PNG, GIF, WEBP).");
            $this->redirect('declarations/create');
        }

        // Valideer MIME type voor extra veiligheid
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $tempName);
        finfo_close($finfo);

        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($mimeType, $allowedMimeTypes)) {
            $this->setMessage("Het bestand is geen geldige afbeelding.");
            $this->redirect('declarations/create');
        }

        // Valideer bestandsgrootte (max 5MB)
        $maxFileSize = 5 * 1024 * 1024; // 5MB
        if ($file['size'] > $maxFileSize) {
            $this->setMessage("Het bestand is te groot. Maximaal 5MB toegestaan.");
            $this->redirect('declarations/create');
        }

        // Genereer een unieke bestandsnaam
        $uniqueName = uniqid('declaratie_' . Auth::id() . '_') . '.' . $fileExt;
        $targetFilePath = $uploadsDir . '/' . $uniqueName;

        // Verplaats het bestand naar de uploads directory
        if (move_uploaded_file($tempName, $targetFilePath)) {
            $bestandPad = 'uploads/declaraties/' . $uniqueName;
        } else {
            $this->setMessage("Er is een fout opgetreden bij het uploaden van het bestand.");
            $this->redirect('declarations/create');
        }

        // Voeg de declaratie toe aan de database
        try {
            $declarationId = $this->declarationModel->addDeclaration(
                $titel,
                $totaalBedrag,
                $bedragExclBtw,
                $btwPercentage,
                $productDienst,
                $bestandPad,
                Auth::id()
            );
        } catch (PDOException $e) {
            // Log de error voor debugging
            error_log("Declaration creation failed: " . $e->getMessage());

            // Check if it's a foreign key constraint error
            if (strpos($e->getMessage(), 'foreign key constraint') !== false) {
                // Sessie probleem, log uit en redirect
                Auth::logout();
                $this->setMessage("Er is een probleem met uw account. Log opnieuw in.");
                $this->redirect('login');
            } else {
                // Andere database error
                $this->setMessage("Er is een fout opgetreden bij het opslaan van de declaratie. Probeer het opnieuw.");
                $this->redirect('declarations/create');
            }
        }

        if ($declarationId) {
            // Log de succesvolle declaratie aanmaak
            Auth::log("Declaratie succesvol aangemaakt: '$titel' (€" . number_format($totaalBedrag, 2) . ")", 'info');

            $this->setMessage("Declaratie succesvol toegevoegd!");
            $this->redirect('declarations');
        } else {
            // Log de fout
            Auth::log("Fout bij aanmaken declaratie: '$titel'", 'error');

            $this->setMessage("Er is een fout opgetreden bij het toevoegen van de declaratie.");
            $this->redirect('declarations/create');
        }
    }

    /**
     * Toon het formulier om een declaratie te bewerken
     *
     * @param int $id
     * @return string
     */
    public function edit($id)
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();

        // Haal de declaratie op
        $declaration = $this->declarationModel->find($id);

        // Controleer of de declaratie bestaat en van de huidige gebruiker is
        if (!$declaration || $declaration['user_id'] != Auth::id()) {
            $this->setMessage("Declaratie niet gevonden of je hebt geen toegang.");
            $this->redirect('declarations');
        }

        // Controleer of de declaratie al is ingediend
        if ($declaration['submitted']) {
            $this->setMessage("Je kunt geen ingediende declaratie bewerken.");
            $this->redirect('declarations');
        }

        // Render de view
        return $this->viewWithLayout('declarations/edit', [
            'declaration' => $declaration
        ]);
    }

    /**
     * Update een declaratie
     *
     * @param int $id
     * @return void
     */
    public function update($id)
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();

        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            $this->redirect("declarations/edit/$id");
        }

        // Haal de declaratie op
        $declaration = $this->declarationModel->find($id);

        // Controleer of de declaratie bestaat en van de huidige gebruiker is
        if (!$declaration || $declaration['user_id'] != Auth::id()) {
            $this->setMessage("Declaratie niet gevonden of je hebt geen toegang.");
            $this->redirect('declarations');
        }

        // Controleer of de declaratie al is ingediend
        if ($declaration['submitted']) {
            $this->setMessage("Je kunt geen ingediende declaratie bewerken.");
            $this->redirect('declarations');
        }

        // Haal de gegevens op uit het formulier
        $titel = $this->post('titel', '');
        $totaalBedrag = $this->post('totaal_bedrag', 0);
        $bedragExclBtw = $this->post('bedrag_excl_btw', 0);
        $btwPercentage = $this->post('btw_percentage', 21.00);
        $productDienst = $this->post('product_dienst', '');
        $bestandPad = $declaration['bestand_pad'];

        // Verwerk bestandsupload als er een nieuw bestand is
        if ($this->hasFile('bon_factuur')) {
            $uploadsDir = Config::get('app.uploads_dir') . '/declaraties';

            // Maak de uploads directory als deze nog niet bestaat
            if (!file_exists($uploadsDir)) {
                mkdir($uploadsDir, 0755, true);
            }

            // Valideer bestandstype (alleen afbeeldingen)
            $file = $this->file('bon_factuur');
            $tempName = $file['tmp_name'];
            $fileName = basename($file['name']);
            $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

            // Toegestane afbeelding extensies
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            if (!in_array($fileExt, $allowedExtensions)) {
                $this->setMessage("Alleen afbeeldingen zijn toegestaan (JPG, JPEG, PNG, GIF, WEBP).");
                $this->redirect("declarations/edit/$id");
            }

            // Valideer MIME type voor extra veiligheid
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $tempName);
            finfo_close($finfo);

            $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($mimeType, $allowedMimeTypes)) {
                $this->setMessage("Het bestand is geen geldige afbeelding.");
                $this->redirect("declarations/edit/$id");
            }

            // Valideer bestandsgrootte (max 5MB)
            $maxFileSize = 5 * 1024 * 1024; // 5MB
            if ($file['size'] > $maxFileSize) {
                $this->setMessage("Het bestand is te groot. Maximaal 5MB toegestaan.");
                $this->redirect("declarations/edit/$id");
            }

            // Genereer een unieke bestandsnaam
            $uniqueName = uniqid('declaratie_' . Auth::id() . '_') . '.' . $fileExt;
            $targetFilePath = $uploadsDir . '/' . $uniqueName;

            // Verplaats het bestand naar de uploads directory
            if (move_uploaded_file($tempName, $targetFilePath)) {
                // Verwijder het oude bestand als het bestaat
                if (!empty($declaration['bestand_pad']) && file_exists($declaration['bestand_pad'])) {
                    unlink($declaration['bestand_pad']);
                }

                $bestandPad = 'uploads/declaraties/' . $uniqueName;
            } else {
                $this->setMessage("Er is een fout opgetreden bij het uploaden van het bestand.");
                $this->redirect("declarations/edit/$id");
            }
        }

        // Update de declaratie in de database
        $result = $this->declarationModel->updateDeclaration(
            $id,
            $titel,
            $totaalBedrag,
            $bedragExclBtw,
            $btwPercentage,
            $productDienst,
            $bestandPad
        );

        if ($result) {
            $this->setMessage("Declaratie succesvol bijgewerkt!");
            $this->redirect('declarations');
        } else {
            $this->setMessage("Er is een fout opgetreden bij het bijwerken van de declaratie.");
            $this->redirect("declarations/edit/$id");
        }
    }

    /**
     * Verwijder een declaratie
     *
     * @param int $id
     * @return void
     */
    public function destroy($id)
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();

        // Haal de declaratie op
        $declaration = $this->declarationModel->find($id);

        // Controleer of de declaratie bestaat en van de huidige gebruiker is
        if (!$declaration || $declaration['user_id'] != Auth::id()) {
            $this->setMessage("Declaratie niet gevonden of je hebt geen toegang.");
            $this->redirect('declarations');
        }

        // Controleer of de declaratie al is ingediend
        if ($declaration['submitted']) {
            $this->setMessage("Je kunt geen ingediende declaratie verwijderen.");
            $this->redirect('declarations');
        }

        // Verwijder het bestand als het bestaat
        if (!empty($declaration['bestand_pad']) && file_exists($declaration['bestand_pad'])) {
            unlink($declaration['bestand_pad']);
        }

        // Verwijder de declaratie uit de database
        $result = $this->declarationModel->delete($id);

        if ($result) {
            $this->setMessage("Declaratie succesvol verwijderd.");
        } else {
            $this->setMessage("Er is een fout opgetreden bij het verwijderen van de declaratie.");
        }

        $this->redirect('declarations');
    }

    /**
     * Dien declaraties in
     *
     * @return void
     */
    public function submit()
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();

        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            $this->redirect('declarations');
        }

        // Controleer of er records zijn geselecteerd
        $recordIds = $this->post('record_ids', []);

        if (empty($recordIds)) {
            $this->setMessage("Geen declaraties geselecteerd om in te dienen.");
            $this->redirect('declarations');
        }

        // Dien de declaraties in
        $result = $this->declarationModel->submitDeclarations($recordIds, Auth::id());

        if ($result) {
            // Log de succesvolle indiening
            Auth::log("Declaraties ingediend: " . count($recordIds) . " declaratie(s) met IDs: " . implode(', ', $recordIds), 'info');

            $this->setMessage("Declaraties succesvol ingediend!");
        } else {
            // Log de fout
            Auth::log("Fout bij indienen declaraties: IDs " . implode(', ', $recordIds), 'error');

            $this->setMessage("Er is een fout opgetreden bij het indienen van de declaraties.");
        }

        $this->redirect('declarations');
    }
}
