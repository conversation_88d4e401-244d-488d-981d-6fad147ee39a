<?php
/**
 * UserController
 */
class UserController extends Controller
{
    /**
     * De User model
     * 
     * @var User
     */
    private $userModel;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->userModel = new User();
    }
    
    /**
     * Toon het profiel van de ingelogde gebruiker
     * 
     * @return string
     */
    public function profile()
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();
        
        // Haal de gebruiker op
        $user = Auth::user();
        
        // Render de view
        return $this->viewWithLayout('users/profile', [
            'user' => $user,
            'message' => $this->getMessage()
        ]);
    }
    
    /**
     * Update het wachtwoord van de ingelogde gebruiker
     * 
     * @return void
     */
    public function updatePassword()
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();
        
        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            return $this->redirect('/user/profile');
        }
        
        // Haal de gegevens op uit het formulier
        $currentPassword = $this->post('current_password');
        $newPassword = $this->post('new_password');
        $confirmPassword = $this->post('confirm_password');
        
        // Controleer of alle velden zijn ingevuld
        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            $this->setMessage('Vul alle wachtwoordvelden in.');
            return $this->redirect('/user/profile');
        }
        
        // Controleer of het nieuwe wachtwoord en de bevestiging overeenkomen
        if ($newPassword !== $confirmPassword) {
            $this->setMessage('Het nieuwe wachtwoord en de bevestiging komen niet overeen.');
            return $this->redirect('/user/profile');
        }
        
        // Controleer of het huidige wachtwoord correct is
        if (!Auth::verifyPassword($currentPassword)) {
            $this->setMessage('Het huidige wachtwoord is onjuist.');
            return $this->redirect('/user/profile');
        }
        
        // Update het wachtwoord
        $result = $this->userModel->updatePassword(Auth::id(), $newPassword);
        
        if ($result) {
            $this->setMessage('Wachtwoord succesvol bijgewerkt.');
        } else {
            $this->setMessage('Er is een fout opgetreden bij het bijwerken van het wachtwoord.');
        }
        
        return $this->redirect('/user/profile');
    }
    
    /**
     * Update het standaard adres van de ingelogde gebruiker
     * 
     * @return void
     */
    public function updateDefaultAddress()
    {
        // Controleer of de gebruiker is ingelogd
        Auth::requireLogin();
        
        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            return $this->redirect('/user/profile');
        }
        
        // Haal de gegevens op uit het formulier
        $defaultAddress = $this->post('default_address');
        
        // Update het standaard adres
        $result = $this->userModel->updateDefaultAddress(Auth::id(), $defaultAddress);
        
        if ($result) {
            $this->setMessage('Standaard adres succesvol bijgewerkt.');
        } else {
            $this->setMessage('Er is een fout opgetreden bij het bijwerken van het standaard adres.');
        }
        
        return $this->redirect('/user/profile');
    }
}
