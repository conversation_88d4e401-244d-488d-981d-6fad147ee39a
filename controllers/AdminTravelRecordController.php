<?php
/**
 * AdminTravelRecordController
 */
class AdminTravelRecordController extends Controller
{
    /**
     * De TravelRecord model
     * 
     * @var TravelRecord
     */
    private $travelRecordModel;
    
    /**
     * De User model
     * 
     * @var User
     */
    private $userModel;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->travelRecordModel = new TravelRecord();
        $this->userModel = new User();
    }
    
    /**
     * Toon een lijst van gebruikers met ingediende reiskostenrecords
     * 
     * @return string
     */
    public function index()
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();
        
        // Haal gebruikers op met ingediende, niet-betaalde reiskostenrecords
        $users = $this->userModel->getUsersWithPendingKilometers();
        
        // Render de view
        return $this->viewWithLayout('admin/travel_records/index', [
            'users' => $users,
            'message' => $this->getMessage()
        ]);
    }
    
    /**
     * Toon reiskostenrecords van een specifieke gebruiker
     * 
     * @param int $userId
     * @return string
     */
    public function show($userId)
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();
        
        // Haal de gebruiker op
        $user = $this->userModel->find($userId);
        
        if (!$user) {
            $this->setMessage("Gebruiker niet gevonden.");
            $this->redirect('admin/travel_records');
        }
        
        // Haal de reiskostenrecords op voor de gebruiker
        $records = $this->travelRecordModel->getPendingTravelRecords($userId);
        
        // Bereken het totaal aantal kilometers
        $totalKm = $this->travelRecordModel->calculateTotalKm($records);
        
        // Render de view
        return $this->viewWithLayout('admin/travel_records/show', [
            'user' => $user,
            'records' => $records,
            'totalKm' => $totalKm,
            'message' => $this->getMessage()
        ]);
    }
    
    /**
     * Update de status van reiskostenrecords
     * 
     * @param int $userId
     * @return void
     */
    public function updateStatus($userId)
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();
        
        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            $this->redirect("admin/travel_records/user/$userId");
        }
        
        // Haal de geselecteerde records en statussen op
        $recordIds = $this->post('record_ids', []);
        $statuses = $this->post('status', []);
        
        if (empty($recordIds)) {
            $this->setMessage("Geen reiskostenrecords geselecteerd.");
            $this->redirect("admin/travel_records/user/$userId");
        }
        
        // Verwerk de statussen
        $paidIds = [];
        $rejectedIds = [];
        
        foreach ($recordIds as $recordId) {
            if (isset($statuses[$recordId])) {
                $status = $statuses[$recordId];
                
                if ($status === 'paid') {
                    $paidIds[] = $recordId;
                } elseif ($status === 'rejected') {
                    $rejectedIds[] = $recordId;
                }
            }
        }
        
        // Update de statussen in de database
        $success = true;
        
        if (!empty($paidIds)) {
            $success = $success && $this->travelRecordModel->markAsPaid($paidIds);
        }
        
        if (!empty($rejectedIds)) {
            foreach ($rejectedIds as $recordId) {
                $success = $success && $this->travelRecordModel->rejectTravelRecord($recordId);
            }
        }
        
        if ($success) {
            $this->setMessage("Status van reiskostenrecords succesvol bijgewerkt.");
        } else {
            $this->setMessage("Er is een fout opgetreden bij het bijwerken van de status.");
        }
        
        $this->redirect("admin/travel_records/user/$userId");
    }
}
