<?php
/**
 * HomeController
 */
class HomeController extends Controller
{
    /**
     * Toon de homepagina
     *
     * @return string
     */
    public function index()
    {
        // Controleer of de gebruiker is ingelogd
        if (!Auth::check()) {
            // Log de redirect
            error_log("HomeController: Gebruiker niet ingelogd, redirect naar login");
            return $this->redirect('login');
        }

        // Log de succesvolle toegang
        error_log("HomeController: Gebruiker ingelogd, toon home/index");

        // Haal de gebruiker op
        $user = Auth::user();

        // Haal de modellen op
        $declarationModel = new Declaration();
        $travelRecordModel = new TravelRecord();

        // Haal de statistieken op
        $pendingDeclarations = $declarationModel->getPendingDeclarations(Auth::id());
        $pendingTravelRecords = $travelRecordModel->getPendingTravelRecords(Auth::id());

        $totalPendingDeclarations = count($pendingDeclarations);
        $totalPendingAmount = array_reduce($pendingDeclarations, function($sum, $declaration) {
            return $sum + $declaration['totaal_bedrag'];
        }, 0);

        $totalPendingTravelRecords = count($pendingTravelRecords);
        $totalPendingKm = array_reduce($pendingTravelRecords, function($sum, $record) {
            return $sum + $record['total_km'];
        }, 0);

        // Render de view
        return $this->viewWithLayout('home/index', [
            'user' => $user,
            'totalPendingDeclarations' => $totalPendingDeclarations,
            'totalPendingAmount' => $totalPendingAmount,
            'totalPendingTravelRecords' => $totalPendingTravelRecords,
            'totalPendingKm' => $totalPendingKm,
            'isAdmin' => Auth::isAdmin()
        ]);
    }
}
