<?php
/**
 * NotificationController
 */
class NotificationController extends Controller
{
    /**
     * De Notification model
     *
     * @var Notification
     */
    private $notificationModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->notificationModel = new Notification();

        // Controleer of de gebruiker is ingelogd en admin is
        if (!isset($_SESSION['user_id'])) {
            $this->redirect('login');
        }

        $userModel = new User();
        if (!$userModel->isAdmin($_SESSION['user_id'])) {
            $this->redirect('');
        }
    }

    /**
     * Toon alle notificaties
     *
     * @return string
     */
    public function index()
    {
        $notifications = $this->notificationModel->getAll();
        $unreadCount = $this->notificationModel->countUnread();

        return $this->viewWithLayout('notifications/index', [
            'notifications' => $notifications,
            'unreadCount' => $unreadCount,
            'pageTitle' => 'Admin Notificaties'
        ]);
    }

    /**
     * Haal ongelezen notificaties op (AJAX)
     *
     * @return void
     */
    public function getUnread()
    {
        header('Content-Type: application/json');
        
        $notifications = $this->notificationModel->getUnreadForAdmins();
        $count = $this->notificationModel->countUnread();

        echo json_encode([
            'notifications' => $notifications,
            'count' => $count
        ]);
    }

    /**
     * Markeer een notificatie als gelezen
     *
     * @return void
     */
    public function markAsRead()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            return;
        }

        $id = $_POST['id'] ?? null;
        if (!$id) {
            http_response_code(400);
            return;
        }

        $success = $this->notificationModel->markAsRead($id);
        
        header('Content-Type: application/json');
        echo json_encode(['success' => $success]);
    }

    /**
     * Markeer alle notificaties als gelezen
     *
     * @return void
     */
    public function markAllAsRead()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('notifications');
        }

        $success = $this->notificationModel->markAllAsRead();
        
        if ($success) {
            $this->setMessage("Alle notificaties gemarkeerd als gelezen.");
        } else {
            $this->setMessage("Er is een fout opgetreden.");
        }

        $this->redirect('notifications');
    }

    /**
     * Verwijder oude notificaties
     *
     * @return void
     */
    public function cleanup()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('notifications');
        }

        $success = $this->notificationModel->cleanupOld();
        
        if ($success) {
            $this->setMessage("Oude notificaties verwijderd.");
        } else {
            $this->setMessage("Er is een fout opgetreden.");
        }

        $this->redirect('notifications');
    }
}
