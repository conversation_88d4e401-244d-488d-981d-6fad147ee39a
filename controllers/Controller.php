<?php
/**
 * Controller basisklasse
 */
class Controller
{
    /**
     * Render een view
     *
     * @param string $view
     * @param array $data
     * @return string
     */
    protected function view($view, $data = [])
    {
        return View::render($view, $data);
    }

    /**
     * Render een view met een layout
     *
     * @param string $view
     * @param array $data
     * @param string $layout
     * @return string
     */
    protected function viewWithLayout($view, $data = [], $layout = 'default')
    {
        return View::renderWithLayout($view, $data, $layout);
    }

    /**
     * Redirect naar een URL
     *
     * @param string $url
     * @return void
     */
    protected function redirect($url)
    {
        // Voeg een leading slash toe als de URL niet begint met http:// of https:// of /
        if (!preg_match('#^(https?://|/)#i', $url)) {
            $url = '/' . $url;
        }

        // Debug informatie
        error_log("Redirect naar: {$url}");

        header("Location: $url");
        exit();
    }

    /**
     * Sla een bericht op in de sessie
     *
     * @param string $message
     * @param string $type
     * @return void
     */
    protected function setMessage($message, $type = 'success')
    {
        $_SESSION['message'] = $message;
        $_SESSION['message_type'] = $type;
    }

    /**
     * Krijg een bericht uit de sessie
     *
     * @return array|null
     */
    protected function getMessage()
    {
        $message = $_SESSION['message'] ?? null;
        $type = $_SESSION['message_type'] ?? 'success';

        unset($_SESSION['message']);
        unset($_SESSION['message_type']);

        if ($message === null) {
            return null;
        }

        return [
            'text' => $message,
            'type' => $type
        ];
    }

    /**
     * Controleer of er een bericht is in de sessie
     *
     * @return bool
     */
    protected function hasMessage()
    {
        return isset($_SESSION['message']);
    }

    /**
     * Krijg een waarde uit de GET-parameters
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    protected function get($key, $default = null)
    {
        return $_GET[$key] ?? $default;
    }

    /**
     * Krijg een waarde uit de POST-parameters
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    protected function post($key, $default = null)
    {
        // Controleer of het een JSON-verzoek is
        $contentType = isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : '';
        if (strpos($contentType, 'application/json') !== false) {
            $json = file_get_contents('php://input');
            $data = json_decode($json, true);
            return $data[$key] ?? $default;
        }

        return $_POST[$key] ?? $default;
    }

    /**
     * Controleer of een verzoek een POST-verzoek is
     *
     * @return bool
     */
    protected function isPost()
    {
        return $_SERVER['REQUEST_METHOD'] === 'POST';
    }

    /**
     * Controleer of een verzoek een GET-verzoek is
     *
     * @return bool
     */
    protected function isGet()
    {
        return $_SERVER['REQUEST_METHOD'] === 'GET';
    }

    /**
     * Krijg alle POST-parameters
     *
     * @return array
     */
    protected function getAllPost()
    {
        return $_POST;
    }

    /**
     * Krijg alle GET-parameters
     *
     * @return array
     */
    protected function getAllGet()
    {
        return $_GET;
    }

    /**
     * Krijg een geüpload bestand
     *
     * @param string $key
     * @return array|null
     */
    protected function file($key)
    {
        return $_FILES[$key] ?? null;
    }

    /**
     * Controleer of een bestand is geüpload
     *
     * @param string $key
     * @return bool
     */
    protected function hasFile($key)
    {
        return isset($_FILES[$key]) && $_FILES[$key]['error'] === UPLOAD_ERR_OK;
    }

    /**
     * Verplaats een geüpload bestand
     *
     * @param string $key
     * @param string $destination
     * @return bool
     */
    protected function moveUploadedFile($key, $destination)
    {
        if (!$this->hasFile($key)) {
            return false;
        }

        return move_uploaded_file($_FILES[$key]['tmp_name'], $destination);
    }

    /**
     * Log een bericht naar het logbestand
     *
     * @param string $message Het bericht om te loggen
     * @param string $level Het logniveau (error, warning, info, debug)
     * @return void
     */
    protected function log($message, $level = 'info')
    {
        $prefix = '';

        switch (strtolower($level)) {
            case 'error':
                $prefix = 'ERROR: ';
                break;
            case 'warning':
                $prefix = 'WARNING: ';
                break;
            case 'info':
                $prefix = 'INFO: ';
                break;
            case 'debug':
                $prefix = 'DEBUG: ';
                break;
        }

        error_log($prefix . $message);
    }
}
