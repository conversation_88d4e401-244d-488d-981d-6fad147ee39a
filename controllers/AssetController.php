<?php
/**
 * AssetController
 */
class AssetController extends Controller
{
    /**
     * De Asset model
     *
     * @var Asset
     */
    private $assetModel;

    /**
     * De AssetLog model
     *
     * @var AssetLog
     */
    private $assetLogModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->assetModel = new Asset();
        $this->assetLogModel = new AssetLog();

        // Controleer of de gebruiker is ingelogd
        if (!isset($_SESSION['user_id'])) {
            $this->redirect('login');
        }
    }

    /**
     * Toon alle assets
     *
     * @return string
     */
    public function index()
    {
        $assets = $this->assetModel->getAllWithHolderName();
        $userId = $_SESSION['user_id'];

        return $this->viewWithLayout('assets/index', [
            'assets' => $assets,
            'userId' => $userId,
            'pageTitle' => 'Asset overzicht'
        ]);
    }

    /**
     * Toon de details van een asset
     *
     * @param int $id
     * @return string
     */
    public function show($id)
    {
        $asset = $this->assetModel->find($id);

        if (!$asset) {
            $this->setMessage('Asset niet gevonden', 'error');
            $this->redirect('assets');
        }

        $logs = $this->assetLogModel->getLogsByAssetId($id);

        return $this->viewWithLayout('assets/show', [
            'asset' => $asset,
            'logs' => $logs,
            'pageTitle' => 'Asset details'
        ]);
    }

    /**
     * Toon de geschiedenis van een asset
     *
     * @param int $id
     * @return string
     */
    public function history($id)
    {
        $asset = $this->assetModel->find($id);

        if (!$asset) {
            $this->setMessage('Asset niet gevonden', 'error');
            $this->redirect('assets');
        }

        $logs = $this->assetLogModel->getLogsByAssetId($id);

        return $this->viewWithLayout('assets/history', [
            'asset' => $asset,
            'logs' => $logs,
            'pageTitle' => 'Asset geschiedenis'
        ]);
    }

    /**
     * Wijs een asset toe aan de ingelogde gebruiker
     *
     * @return void
     */
    public function assign()
    {
        if (!$this->isPost()) {
            $this->redirect('assets');
        }

        $assetId = $this->post('asset_id');
        $userId = $_SESSION['user_id'];

        if (!$assetId) {
            $this->setMessage('Geen asset geselecteerd', 'error');
            $this->redirect('assets');
        }

        $asset = $this->assetModel->find($assetId);

        if (!$asset) {
            $this->setMessage('Asset niet gevonden', 'error');
            $this->redirect('assets');
        }

        if ($asset['current_holder']) {
            $this->setMessage('Asset is al toegewezen aan een gebruiker', 'error');
            $this->redirect('assets');
        }

        // Wijs het asset toe aan de gebruiker
        $this->assetModel->assignToUser($assetId, $userId);

        // Voeg een log toe
        $this->assetLogModel->addLog($assetId, $userId, 'uitgeleend');

        $this->setMessage('Asset succesvol toegewezen');
        $this->redirect('assets');
    }
}
