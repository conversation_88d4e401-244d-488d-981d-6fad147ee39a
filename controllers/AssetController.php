<?php
/**
 * AssetController
 */
class AssetController extends Controller
{
    /**
     * De Asset model
     *
     * @var Asset
     */
    private $assetModel;

    /**
     * De AssetLog model
     *
     * @var AssetLog
     */
    private $assetLogModel;

    /**
     * De Notification model
     *
     * @var Notification
     */
    private $notificationModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->assetModel = new Asset();
        $this->assetLogModel = new AssetLog();
        $this->notificationModel = new Notification();

        // Controleer of de gebruiker is ingelogd
        if (!isset($_SESSION['user_id'])) {
            $this->redirect('login');
        }
    }

    /**
     * Toon alle assets
     *
     * @return string
     */
    public function index()
    {
        $userId = $_SESSION['user_id'];
        $userModel = new User();
        $userRole = $userModel->getUserRole($userId);

        // Haal assets op gebaseerd op gebruikersrol
        $assets = $this->assetModel->getAssetsForRole($userRole);

        return $this->viewWithLayout('assets/index', [
            'assets' => $assets,
            'userId' => $userId,
            'userRole' => $userRole,
            'pageTitle' => 'Asset overzicht'
        ]);
    }

    /**
     * Wijs een asset toe aan de huidige gebruiker
     *
     * @return void
     */
    public function assignToMe()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('assets');
        }

        $assetId = $_POST['asset_id'] ?? null;
        $userId = $_SESSION['user_id'];

        if (!$assetId) {
            $this->setMessage("Geen asset ID opgegeven.");
            $this->redirect('assets');
        }

        // Controleer of de asset bestaat en beschikbaar is
        $asset = $this->assetModel->find($assetId);
        if (!$asset) {
            $this->setMessage("Asset niet gevonden.");
            $this->redirect('assets');
        }

        if ($asset['current_holder']) {
            $this->setMessage("Deze asset is al toegewezen aan iemand anders.");
            $this->redirect('assets');
        }

        // Wijs de asset toe
        $success = $this->assetModel->assignToUser($assetId, $userId);

        if ($success) {
            // Log de toewijzing
            $this->assetLogModel->create([
                'asset_id' => $assetId,
                'user_id' => $userId,
                'action' => 'uitgegeven',
                'comments' => 'Zelf toegewezen door gebruiker',
                'action_date' => date('Y-m-d H:i:s')
            ]);

            // Maak een notificatie voor admins
            $userModel = new User();
            $user = $userModel->find($userId);
            $username = $user['username'] ?? 'Onbekend';

            $this->notificationModel->createNotification(
                $userId,
                'asset_assigned',
                "Asset '{$asset['uid']}' toegewezen aan gebruiker '{$username}'",
                [
                    'asset_id' => $assetId,
                    'asset_uid' => $asset['uid'],
                    'assigned_to' => $username
                ]
            );

            // Log voor audit trail
            Auth::log("Asset '{$asset['uid']}' zelf toegewezen", 'info');

            $this->setMessage("Asset succesvol aan jezelf toegewezen!");
        } else {
            $this->setMessage("Er is een fout opgetreden bij het toewijzen van de asset.");
        }

        $this->redirect('assets');
    }

    /**
     * Toon de details van een asset
     *
     * @param int $id
     * @return string
     */
    public function show($id)
    {
        $asset = $this->assetModel->find($id);

        if (!$asset) {
            $this->setMessage('Asset niet gevonden', 'error');
            $this->redirect('assets');
        }

        $logs = $this->assetLogModel->getLogsByAssetId($id);

        return $this->viewWithLayout('assets/show', [
            'asset' => $asset,
            'logs' => $logs,
            'pageTitle' => 'Asset details'
        ]);
    }

    /**
     * Toon de geschiedenis van een asset
     *
     * @param int $id
     * @return string
     */
    public function history($id)
    {
        $asset = $this->assetModel->find($id);

        if (!$asset) {
            $this->setMessage('Asset niet gevonden', 'error');
            $this->redirect('assets');
        }

        $logs = $this->assetLogModel->getLogsByAssetId($id);

        return $this->viewWithLayout('assets/history', [
            'asset' => $asset,
            'logs' => $logs,
            'pageTitle' => 'Asset geschiedenis'
        ]);
    }

    /**
     * Wijs een asset toe aan de ingelogde gebruiker
     *
     * @return void
     */
    public function assign()
    {
        if (!$this->isPost()) {
            $this->redirect('assets');
        }

        $assetId = $this->post('asset_id');
        $userId = $_SESSION['user_id'];

        if (!$assetId) {
            $this->setMessage('Geen asset geselecteerd', 'error');
            $this->redirect('assets');
        }

        $asset = $this->assetModel->find($assetId);

        if (!$asset) {
            $this->setMessage('Asset niet gevonden', 'error');
            $this->redirect('assets');
        }

        if ($asset['current_holder']) {
            $this->setMessage('Asset is al toegewezen aan een gebruiker', 'error');
            $this->redirect('assets');
        }

        // Wijs het asset toe aan de gebruiker
        $this->assetModel->assignToUser($assetId, $userId);

        // Voeg een log toe
        $this->assetLogModel->addLog($assetId, $userId, 'uitgeleend');

        $this->setMessage('Asset succesvol toegewezen');
        $this->redirect('assets');
    }
}
