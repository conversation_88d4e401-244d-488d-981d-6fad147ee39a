<?php
/**
 * AdminController
 */
class AdminController extends Controller
{
    /**
     * Toon het admin dashboard
     *
     * @return string
     */
    public function index()
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();

        // Haal de modellen op
        $userModel = new User();
        $declarationModel = new Declaration();
        $travelRecordModel = new TravelRecord();
        $assetModel = new Asset();

        // Haal de statistieken op
        $users = $userModel->all();
        $usersWithPendingDeclarations = $userModel->getUsersWithPendingDeclarations();
        $usersWithPendingKilometers = $userModel->getUsersWithPendingKilometers();
        $assets = $assetModel->all();

        // Bereken totalen
        $totalUsers = count($users);
        $totalPendingDeclarations = array_reduce($usersWithPendingDeclarations, function($sum, $user) {
            return $sum + $user['declaration_count'];
        }, 0);
        $totalPendingAmount = array_reduce($usersWithPendingDeclarations, function($sum, $user) {
            return $sum + $user['total_amount'];
        }, 0);

        // Bereken totaal aantal kilometers
        $totalKilometers = 0;
        foreach ($usersWithPendingKilometers as $user) {
            $userTravelRecords = $travelRecordModel->getPendingByUserId($user['id']);
            foreach ($userTravelRecords as $record) {
                $totalKilometers += isset($record['distance']) ? $record['distance'] : $record['total_km'];
                // Als het een retourrit is, verdubbel de afstand
                if (isset($record['return_trip']) && $record['return_trip']) {
                    $totalKilometers += isset($record['distance']) ? $record['distance'] : $record['total_km'];
                }
            }
        }

        // Bereken totaal aantal assets
        $totalAssets = count($assets);

        // Render de view
        return $this->viewWithLayout('admin/index', [
            'totalUsers' => $totalUsers,
            'totalPendingDeclarations' => $totalPendingDeclarations,
            'totalPendingAmount' => $totalPendingAmount,
            'totalKilometers' => $totalKilometers,
            'totalAssets' => $totalAssets,
            'usersWithPendingDeclarations' => $usersWithPendingDeclarations,
            'usersWithPendingKilometers' => $usersWithPendingKilometers,
            'message' => $this->getMessage()
        ]);
    }

    /**
     * Toon een lijst van gebruikers
     *
     * @return string
     */
    public function users()
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();

        // Haal de gebruikers op
        $userModel = new User();
        $users = $userModel->all();

        // Render de view
        return $this->viewWithLayout('admin/users', [
            'users' => $users,
            'message' => $this->getMessage()
        ]);
    }

    /**
     * Toon het formulier om een nieuwe gebruiker toe te voegen
     *
     * @return string
     */
    public function createUser()
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();

        // Render de view
        return $this->viewWithLayout('admin/create_user');
    }

    /**
     * Sla een nieuwe gebruiker op
     *
     * @return void
     */
    public function storeUser()
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();

        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            $this->redirect('admin/users/create');
        }

        // Haal de gegevens op uit het formulier
        $username = $this->post('username', '');
        $password = $this->post('password', '');
        $isAdmin = $this->post('is_admin', false);
        $defaultAddress = $this->post('default_address', '');

        // Controleer of de gebruikersnaam en het wachtwoord zijn ingevuld
        if (empty($username) || empty($password)) {
            $this->setMessage('Vul alle verplichte velden in.');
            $this->redirect('admin/users/create');
        }

        // Controleer of de gebruikersnaam al bestaat
        $userModel = new User();
        $existingUser = $userModel->whereOne(['username' => $username]);

        if ($existingUser) {
            $this->setMessage('Deze gebruikersnaam is al in gebruik.');
            $this->redirect('admin/users/create');
        }

        // Maak de nieuwe gebruiker aan
        $userId = $userModel->register($username, $password, $isAdmin);

        if ($userId) {
            // Stel het standaardadres in als het is opgegeven
            if (!empty($defaultAddress)) {
                $userModel->setDefaultAddress($userId, $defaultAddress);
            }

            $this->setMessage('Gebruiker succesvol aangemaakt.');
            $this->redirect('admin/users');
        } else {
            $this->setMessage('Er is een fout opgetreden bij het aanmaken van de gebruiker.');
            $this->redirect('admin/users/create');
        }
    }

    /**
     * Toon het formulier om een gebruiker te bewerken
     *
     * @param int $id
     * @return string
     */
    public function editUser($id)
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();

        // Haal de gebruiker op
        $userModel = new User();
        $user = $userModel->find($id);

        if (!$user) {
            $this->setMessage('Gebruiker niet gevonden.');
            $this->redirect('admin/users');
        }

        // Render de view
        return $this->viewWithLayout('admin/edit_user', [
            'user' => $user
        ]);
    }

    /**
     * Update een gebruiker
     *
     * @param int $id
     * @return void
     */
    public function updateUser($id)
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();

        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            $this->redirect("admin/users/edit/$id");
        }

        // Haal de gebruiker op
        $userModel = new User();
        $user = $userModel->find($id);

        if (!$user) {
            $this->setMessage('Gebruiker niet gevonden.');
            $this->redirect('admin/users');
        }

        // Haal de gegevens op uit het formulier
        $username = $this->post('username', '');
        $password = $this->post('password', '');
        $isAdmin = $this->post('is_admin', false);
        $defaultAddress = $this->post('default_address', '');

        // Controleer of de gebruikersnaam is ingevuld
        if (empty($username)) {
            $this->setMessage('Vul alle verplichte velden in.');
            $this->redirect("admin/users/edit/$id");
        }

        // Controleer of de gebruikersnaam al bestaat (behalve voor de huidige gebruiker)
        $existingUser = $userModel->whereOne(['username' => $username]);

        if ($existingUser && $existingUser['id'] != $id) {
            $this->setMessage('Deze gebruikersnaam is al in gebruik.');
            $this->redirect("admin/users/edit/$id");
        }

        // Update de gebruiker
        $success = $userModel->update($id, [
            'username' => $username,
            'is_admin' => $isAdmin ? 1 : 0,
            'default_address' => $defaultAddress
        ]);

        // Update het wachtwoord als het is opgegeven
        if (!empty($password)) {
            $success = $success && $userModel->updatePassword($id, $password);
        }

        if ($success) {
            $this->setMessage('Gebruiker succesvol bijgewerkt.');
            $this->redirect('admin/users');
        } else {
            $this->setMessage('Er is een fout opgetreden bij het bijwerken van de gebruiker.');
            $this->redirect("admin/users/edit/$id");
        }
    }

    /**
     * Verwijder een gebruiker
     *
     * @param int $id
     * @return void
     */
    public function destroyUser($id)
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();

        // Controleer of de gebruiker niet zichzelf probeert te verwijderen
        if ($id == Auth::id()) {
            $this->setMessage('Je kunt jezelf niet verwijderen.');
            $this->redirect('admin/users');
        }

        // Haal de gebruiker op
        $userModel = new User();
        $user = $userModel->find($id);

        if (!$user) {
            $this->setMessage('Gebruiker niet gevonden.');
            $this->redirect('admin/users');
        }

        // Verwijder de gebruiker
        $success = $userModel->delete($id);

        if ($success) {
            $this->setMessage('Gebruiker succesvol verwijderd.');
        } else {
            $this->setMessage('Er is een fout opgetreden bij het verwijderen van de gebruiker.');
        }

        $this->redirect('admin/users');
    }

    /**
     * Toon admin assets overzicht
     *
     * @return string
     */
    public function assets()
    {
        Auth::requireLogin();
        Auth::requireAdmin();

        $assetModel = new Asset();
        $assets = $assetModel->getAllWithHolderName();

        return $this->viewWithLayout('admin/assets/index', [
            'assets' => $assets
        ]);
    }

    /**
     * Toon formulier voor nieuw asset
     *
     * @return string
     */
    public function createAsset()
    {
        Auth::requireLogin();
        Auth::requireAdmin();

        $assetTypeModel = new AssetType();
        $assetTypes = $assetTypeModel->all();

        return $this->viewWithLayout('admin/assets/create', [
            'assetTypes' => $assetTypes
        ]);
    }

    /**
     * Sla nieuw asset op
     *
     * @return void
     */
    public function storeAsset()
    {
        Auth::requireLogin();
        Auth::requireAdmin();

        $assetModel = new Asset();

        $data = [
            'uid' => $_POST['uid'],
            'name' => $_POST['name'],
            'brand' => $_POST['brand'] ?? null,
            'model' => $_POST['model'] ?? null,
            'type' => $_POST['type'] ?? null,
            'purchase_date' => $_POST['purchase_date'] ?? null,
            'serial_number' => $_POST['serial_number'] ?? null,
            'status' => $_POST['status'] ?? 'beschikbaar',
            'purchase_value' => $_POST['purchase_value'] ?? null,
            'quantity' => $_POST['quantity'] ?? 1,
            'product_link' => $_POST['product_link'] ?? null,
            'comments' => $_POST['comments'] ?? null,
            'visibility' => isset($_POST['visibility']) ? 1 : 0,
            'user_group' => $_POST['user_group'] ?? null
        ];

        $assetModel->create($data);

        $this->redirect('admin/assets');
    }

    /**
     * Toon formulier voor asset bewerken
     *
     * @param int $id
     * @return string
     */
    public function editAsset($id)
    {
        Auth::requireLogin();
        Auth::requireAdmin();

        $assetModel = new Asset();
        $assetTypeModel = new AssetType();

        $asset = $assetModel->find($id);
        $assetTypes = $assetTypeModel->all();

        if (!$asset) {
            $this->redirect('admin/assets');
            return;
        }

        return $this->viewWithLayout('admin/assets/edit', [
            'asset' => $asset,
            'assetTypes' => $assetTypes
        ]);
    }

    /**
     * Update asset
     *
     * @param int $id
     * @return void
     */
    public function updateAsset($id)
    {
        Auth::requireLogin();
        Auth::requireAdmin();

        $assetModel = new Asset();

        $data = [
            'uid' => $_POST['uid'],
            'name' => $_POST['name'],
            'brand' => $_POST['brand'] ?? null,
            'model' => $_POST['model'] ?? null,
            'type' => $_POST['type'] ?? null,
            'purchase_date' => $_POST['purchase_date'] ?? null,
            'serial_number' => $_POST['serial_number'] ?? null,
            'status' => $_POST['status'] ?? 'beschikbaar',
            'purchase_value' => $_POST['purchase_value'] ?? null,
            'quantity' => $_POST['quantity'] ?? 1,
            'product_link' => $_POST['product_link'] ?? null,
            'comments' => $_POST['comments'] ?? null,
            'visibility' => isset($_POST['visibility']) ? 1 : 0,
            'user_group' => $_POST['user_group'] ?? null
        ];

        $assetModel->update($id, $data);

        $this->redirect('admin/assets');
    }

    /**
     * Verwijder asset
     *
     * @param int $id
     * @return void
     */
    public function deleteAsset($id)
    {
        Auth::requireLogin();
        Auth::requireAdmin();

        $assetModel = new Asset();
        $assetModel->delete($id);

        $this->redirect('admin/assets');
    }

    /**
     * Toon asset management pagina (in- en uitgifte)
     *
     * @return string
     */
    public function manageAssets()
    {
        Auth::requireLogin();
        Auth::requireAdmin();

        $userModel = new User();
        $users = $userModel->all();

        return $this->viewWithLayout('admin/assets/manage', [
            'users' => $users
        ]);
    }

    /**
     * Wijs asset toe aan gebruiker
     *
     * @return void
     */
    public function assignAsset()
    {
        Auth::requireLogin();
        Auth::requireAdmin();

        $assetModel = new Asset();
        $assetLogModel = new AssetLog();

        $assetId = $_POST['asset_id'];
        $userId = $_POST['user_id'];
        $comments = $_POST['comments'] ?? '';

        // Update asset met nieuwe houder
        $assetModel->update($assetId, [
            'current_holder' => $userId,
            'holder_since' => date('Y-m-d H:i:s')
        ]);

        // Log de uitgifte
        $assetLogModel->create([
            'asset_id' => $assetId,
            'user_id' => $userId,
            'action' => 'uitgegeven',
            'comments' => $comments,
            'action_date' => date('Y-m-d H:i:s')
        ]);

        $this->redirect('admin/assets/manage');
    }

    /**
     * Ontkoppel asset van gebruiker
     *
     * @return void
     */
    public function unassignAsset()
    {
        Auth::requireLogin();
        Auth::requireAdmin();

        $assetModel = new Asset();
        $assetLogModel = new AssetLog();

        $assetId = $_POST['asset_id'];

        // Update asset - verwijder houder
        $assetModel->update($assetId, [
            'current_holder' => null,
            'holder_since' => null
        ]);

        // Log de inname
        $assetLogModel->create([
            'asset_id' => $assetId,
            'user_id' => null,
            'action' => 'ingenomen',
            'comments' => 'Asset ingenomen door admin',
            'action_date' => date('Y-m-d H:i:s')
        ]);

        $this->redirect('admin/assets/manage');
    }
}
