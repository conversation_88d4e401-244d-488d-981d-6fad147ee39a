<?php
/**
 * AdminController
 */
class AdminController extends Controller
{
    /**
     * Toon het admin dashboard
     *
     * @return string
     */
    public function index()
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();

        // Haal de modellen op
        $userModel = new User();
        $declarationModel = new Declaration();
        $travelRecordModel = new TravelRecord();
        $assetModel = new Asset();

        // Haal de statistieken op
        $users = $userModel->all();
        $usersWithPendingDeclarations = $userModel->getUsersWithPendingDeclarations();
        $usersWithPendingKilometers = $userModel->getUsersWithPendingKilometers();
        $assets = $assetModel->all();

        // Bereken totalen
        $totalUsers = count($users);
        $totalPendingDeclarations = array_reduce($usersWithPendingDeclarations, function($sum, $user) {
            return $sum + $user['declaration_count'];
        }, 0);
        $totalPendingAmount = array_reduce($usersWithPendingDeclarations, function($sum, $user) {
            return $sum + $user['total_amount'];
        }, 0);

        // Bereken totaal aantal kilometers
        $totalKilometers = 0;
        foreach ($usersWithPendingKilometers as $user) {
            $userTravelRecords = $travelRecordModel->getPendingByUserId($user['id']);
            foreach ($userTravelRecords as $record) {
                $totalKilometers += isset($record['distance']) ? $record['distance'] : $record['total_km'];
                // Als het een retourrit is, verdubbel de afstand
                if (isset($record['return_trip']) && $record['return_trip']) {
                    $totalKilometers += isset($record['distance']) ? $record['distance'] : $record['total_km'];
                }
            }
        }

        // Bereken totaal aantal assets
        $totalAssets = count($assets);

        // Render de view
        return $this->viewWithLayout('admin/index', [
            'totalUsers' => $totalUsers,
            'totalPendingDeclarations' => $totalPendingDeclarations,
            'totalPendingAmount' => $totalPendingAmount,
            'totalKilometers' => $totalKilometers,
            'totalAssets' => $totalAssets,
            'usersWithPendingDeclarations' => $usersWithPendingDeclarations,
            'usersWithPendingKilometers' => $usersWithPendingKilometers,
            'message' => $this->getMessage()
        ]);
    }

    /**
     * Toon een lijst van gebruikers
     *
     * @return string
     */
    public function users()
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();

        // Haal de gebruikers op
        $userModel = new User();
        $users = $userModel->all();

        // Render de view
        return $this->viewWithLayout('admin/users', [
            'users' => $users,
            'message' => $this->getMessage()
        ]);
    }

    /**
     * Toon het formulier om een nieuwe gebruiker toe te voegen
     *
     * @return string
     */
    public function createUser()
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();

        // Render de view
        return $this->viewWithLayout('admin/create_user');
    }

    /**
     * Sla een nieuwe gebruiker op
     *
     * @return void
     */
    public function storeUser()
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();

        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            $this->redirect('admin/users/create');
        }

        // Haal de gegevens op uit het formulier
        $username = $this->post('username', '');
        $password = $this->post('password', '');
        $isAdmin = $this->post('is_admin', false);
        $defaultAddress = $this->post('default_address', '');

        // Controleer of de gebruikersnaam en het wachtwoord zijn ingevuld
        if (empty($username) || empty($password)) {
            $this->setMessage('Vul alle verplichte velden in.');
            $this->redirect('admin/users/create');
        }

        // Controleer of de gebruikersnaam al bestaat
        $userModel = new User();
        $existingUser = $userModel->whereOne(['username' => $username]);

        if ($existingUser) {
            $this->setMessage('Deze gebruikersnaam is al in gebruik.');
            $this->redirect('admin/users/create');
        }

        // Maak de nieuwe gebruiker aan
        $userId = $userModel->register($username, $password, $isAdmin);

        if ($userId) {
            // Stel het standaardadres in als het is opgegeven
            if (!empty($defaultAddress)) {
                $userModel->setDefaultAddress($userId, $defaultAddress);
            }

            $this->setMessage('Gebruiker succesvol aangemaakt.');
            $this->redirect('admin/users');
        } else {
            $this->setMessage('Er is een fout opgetreden bij het aanmaken van de gebruiker.');
            $this->redirect('admin/users/create');
        }
    }

    /**
     * Toon het formulier om een gebruiker te bewerken
     *
     * @param int $id
     * @return string
     */
    public function editUser($id)
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();

        // Haal de gebruiker op
        $userModel = new User();
        $user = $userModel->find($id);

        if (!$user) {
            $this->setMessage('Gebruiker niet gevonden.');
            $this->redirect('admin/users');
        }

        // Render de view
        return $this->viewWithLayout('admin/edit_user', [
            'user' => $user
        ]);
    }

    /**
     * Update een gebruiker
     *
     * @param int $id
     * @return void
     */
    public function updateUser($id)
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();

        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            $this->redirect("admin/users/edit/$id");
        }

        // Haal de gebruiker op
        $userModel = new User();
        $user = $userModel->find($id);

        if (!$user) {
            $this->setMessage('Gebruiker niet gevonden.');
            $this->redirect('admin/users');
        }

        // Haal de gegevens op uit het formulier
        $username = $this->post('username', '');
        $password = $this->post('password', '');
        $isAdmin = $this->post('is_admin', false);
        $defaultAddress = $this->post('default_address', '');

        // Controleer of de gebruikersnaam is ingevuld
        if (empty($username)) {
            $this->setMessage('Vul alle verplichte velden in.');
            $this->redirect("admin/users/edit/$id");
        }

        // Controleer of de gebruikersnaam al bestaat (behalve voor de huidige gebruiker)
        $existingUser = $userModel->whereOne(['username' => $username]);

        if ($existingUser && $existingUser['id'] != $id) {
            $this->setMessage('Deze gebruikersnaam is al in gebruik.');
            $this->redirect("admin/users/edit/$id");
        }

        // Update de gebruiker
        $success = $userModel->update($id, [
            'username' => $username,
            'is_admin' => $isAdmin ? 1 : 0,
            'default_address' => $defaultAddress
        ]);

        // Update het wachtwoord als het is opgegeven
        if (!empty($password)) {
            $success = $success && $userModel->updatePassword($id, $password);
        }

        if ($success) {
            $this->setMessage('Gebruiker succesvol bijgewerkt.');
            $this->redirect('admin/users');
        } else {
            $this->setMessage('Er is een fout opgetreden bij het bijwerken van de gebruiker.');
            $this->redirect("admin/users/edit/$id");
        }
    }

    /**
     * Verwijder een gebruiker
     *
     * @param int $id
     * @return void
     */
    public function destroyUser($id)
    {
        // Controleer of de gebruiker is ingelogd en een admin is
        Auth::requireLogin();
        Auth::requireAdmin();

        // Controleer of de gebruiker niet zichzelf probeert te verwijderen
        if ($id == Auth::id()) {
            $this->setMessage('Je kunt jezelf niet verwijderen.');
            $this->redirect('admin/users');
        }

        // Haal de gebruiker op
        $userModel = new User();
        $user = $userModel->find($id);

        if (!$user) {
            $this->setMessage('Gebruiker niet gevonden.');
            $this->redirect('admin/users');
        }

        // Verwijder de gebruiker
        $success = $userModel->delete($id);

        if ($success) {
            $this->setMessage('Gebruiker succesvol verwijderd.');
        } else {
            $this->setMessage('Er is een fout opgetreden bij het verwijderen van de gebruiker.');
        }

        $this->redirect('admin/users');
    }
}
