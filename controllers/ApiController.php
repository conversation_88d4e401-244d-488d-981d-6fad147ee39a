<?php
/**
 * ApiController
 */
class ApiController extends Controller
{
    /**
     * Bereken de afstand tussen twee adressen
     *
     * @return string
     */
    public function calculateDistance()
    {
        // Controleer of de gebruiker is ingelogd
        if (!Auth::check()) {
            http_response_code(401);
            Auth::log('Unauthorized API access attempt: calculateDistance', 'warning');
            return json_encode(['error' => 'Unauthorized']);
        }

        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            http_response_code(405);
            return json_encode(['error' => 'Method Not Allowed']);
        }

        // Haal de adressen op
        $addressA = $this->post('addressA', '');
        $addressB = $this->post('addressB', '');

        // Controleer of de adressen zijn ingevuld
        if (empty($addressA) || empty($addressB)) {
            http_response_code(400);
            return json_encode(['error' => 'Bad Request']);
        }

        // Bereken de afstand
        $googleMapsService = new GoogleMapsService();
        $distance = $googleMapsService->calculateDistance($addressA, $addressB);

        // Controleer of de afstand kon worden berekend
        if ($distance === null) {
            http_response_code(404);
            return json_encode(['error' => 'Not Found']);
        }

        // Retourneer de afstand
        return json_encode(['distance' => $distance]);
    }

    /**
     * Bereken de afstand tussen meerdere adressen
     *
     * @return string
     */
    public function calculateTotalDistance()
    {
        // Controleer of de gebruiker is ingelogd
        if (!Auth::check()) {
            http_response_code(401);
            Auth::log('Unauthorized API access attempt: calculateTotalDistance', 'warning');
            return json_encode(['error' => 'Unauthorized']);
        }

        // Controleer of het een POST-verzoek is
        if (!$this->isPost()) {
            http_response_code(405);
            return json_encode(['error' => 'Method Not Allowed']);
        }

        // Haal de adressen op
        $addresses = $this->post('addresses', []);

        // Debug informatie
        error_log("Ontvangen adressen: " . json_encode($addresses));

        // Controleer of er minstens twee adressen zijn
        if (count($addresses) < 2) {
            http_response_code(400);
            error_log("Fout: Minder dan twee adressen ontvangen");
            return json_encode(['error' => 'Bad Request - Minstens twee adressen vereist']);
        }

        // Bereken de afstand
        $googleMapsService = new GoogleMapsService();
        $distance = $googleMapsService->calculateTotalDistance($addresses);

        // Controleer of de afstand kon worden berekend
        if ($distance === null) {
            http_response_code(404);
            Auth::log("Afstand kon niet worden berekend voor adressen: " . json_encode($addresses), 'error');
            return json_encode(['error' => 'Not Found - Afstand kon niet worden berekend']);
        }

        // Log de berekende afstand
        Auth::log("Afstand berekend: " . $distance . " km voor adressen: " . json_encode($addresses), 'info');

        // Retourneer de afstand
        return json_encode(['distance' => $distance]);
    }
}
