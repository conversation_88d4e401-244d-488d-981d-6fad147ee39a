<?php
/**
 * Basis test suite voor de applicatie
 *
 * Deze tests controleren de belangrijkste functionaliteiten van de applicatie
 */

require_once __DIR__ . '/../bootstrap.php';

class BasicTests
{
    private $errors = [];
    private $passed = 0;
    private $failed = 0;

    public function runAllTests()
    {
        echo "=== Ecosprong App Test Suite ===\n\n";

        $this->testDatabaseConnection();
        $this->testDatabaseTables();
        $this->testModels();
        $this->testControllers();
        $this->testRoutes();
        $this->testAuth();
        $this->testNewFeatures();

        $this->printResults();
    }

    private function testDatabaseConnection()
    {
        echo "Testing Database Connection...\n";

        try {
            $db = Database::getInstance();
            $this->assert(true, "Database connection successful");
        } catch (Exception $e) {
            $this->assert(false, "Database connection failed: " . $e->getMessage());
        }
    }

    private function testDatabaseTables()
    {
        echo "\nTesting Database Tables...\n";

        try {
            $db = Database::getInstance()->getConnection();

            // Test required tables exist
            $tables = ['users', 'declaraties', 'travel_records', 'assets', 'asset_logs', 'asset_types'];

            foreach ($tables as $table) {
                $stmt = $db->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                $exists = $stmt->fetch() !== false;
                $this->assert($exists, "Table '$table' exists");
            }

        } catch (Exception $e) {
            $this->assert(false, "Database table testing failed: " . $e->getMessage());
        }
    }

    private function testModels()
    {
        echo "\nTesting Models...\n";

        // Test User model
        try {
            $userModel = new User();
            $this->assert(true, "User model instantiated");
        } catch (Exception $e) {
            $this->assert(false, "User model failed: " . $e->getMessage());
        }

        // Test Declaration model
        try {
            $declarationModel = new Declaration();
            $this->assert(true, "Declaration model instantiated");
        } catch (Exception $e) {
            $this->assert(false, "Declaration model failed: " . $e->getMessage());
        }

        // Test TravelRecord model
        try {
            $travelRecordModel = new TravelRecord();
            $this->assert(true, "TravelRecord model instantiated");
        } catch (Exception $e) {
            $this->assert(false, "TravelRecord model failed: " . $e->getMessage());
        }

        // Test Asset model
        try {
            $assetModel = new Asset();
            $this->assert(true, "Asset model instantiated");
        } catch (Exception $e) {
            $this->assert(false, "Asset model failed: " . $e->getMessage());
        }
    }

    private function testControllers()
    {
        echo "\nTesting Controllers...\n";

        // Test HomeController
        try {
            $homeController = new HomeController();
            $this->assert(true, "HomeController instantiated");
        } catch (Exception $e) {
            $this->assert(false, "HomeController failed: " . $e->getMessage());
        }

        // Test AuthController
        try {
            $authController = new AuthController();
            $this->assert(true, "AuthController instantiated");
        } catch (Exception $e) {
            $this->assert(false, "AuthController failed: " . $e->getMessage());
        }

        // Test DeclarationController
        try {
            $declarationController = new DeclarationController();
            $this->assert(true, "DeclarationController instantiated");
        } catch (Exception $e) {
            $this->assert(false, "DeclarationController failed: " . $e->getMessage());
        }

        // Test TravelRecordController
        try {
            $travelRecordController = new TravelRecordController();
            $this->assert(true, "TravelRecordController instantiated");
        } catch (Exception $e) {
            $this->assert(false, "TravelRecordController failed: " . $e->getMessage());
        }

        // Test AssetController
        try {
            $assetController = new AssetController();
            $this->assert(true, "AssetController instantiated");
        } catch (Exception $e) {
            $this->assert(false, "AssetController failed: " . $e->getMessage());
        }
    }

    private function testRoutes()
    {
        echo "\nTesting Router...\n";

        try {
            $router = new Router();
            $this->assert(true, "Router instantiated");
        } catch (Exception $e) {
            $this->assert(false, "Router failed: " . $e->getMessage());
        }
    }

    private function testAuth()
    {
        echo "\nTesting Authentication...\n";

        try {
            // Test Auth class methods exist
            $this->assert(method_exists('Auth', 'check'), "Auth::check method exists");
            $this->assert(method_exists('Auth', 'login'), "Auth::login method exists");
            $this->assert(method_exists('Auth', 'logout'), "Auth::logout method exists");
            $this->assert(method_exists('Auth', 'user'), "Auth::user method exists");
            $this->assert(method_exists('Auth', 'id'), "Auth::id method exists");
            $this->assert(method_exists('Auth', 'isAdmin'), "Auth::isAdmin method exists");
        } catch (Exception $e) {
            $this->assert(false, "Auth testing failed: " . $e->getMessage());
        }
    }

    private function testNewFeatures()
    {
        echo "\nTesting New Features...\n";

        // Test TravelRecord new methods
        try {
            $travelRecordModel = new TravelRecord();
            $this->assert(method_exists($travelRecordModel, 'getTotalPaidKm'), "TravelRecord::getTotalPaidKm method exists");
            $this->assert(method_exists($travelRecordModel, 'getFilteredTravelRecords'), "TravelRecord::getFilteredTravelRecords method exists");
        } catch (Exception $e) {
            $this->assert(false, "TravelRecord new features failed: " . $e->getMessage());
        }

        // Test Declaration new methods
        try {
            $declarationModel = new Declaration();
            $this->assert(method_exists($declarationModel, 'getTotalPaidAmount'), "Declaration::getTotalPaidAmount method exists");
            $this->assert(method_exists($declarationModel, 'getFilteredDeclarations'), "Declaration::getFilteredDeclarations method exists");
        } catch (Exception $e) {
            $this->assert(false, "Declaration new features failed: " . $e->getMessage());
        }

        // Test Config class
        try {
            $appName = Config::get('app.name');
            $this->assert(!empty($appName), "Config::get works for app.name");
        } catch (Exception $e) {
            $this->assert(false, "Config testing failed: " . $e->getMessage());
        }

        // Test Asset functionality
        try {
            $assetModel = new Asset();
            $this->assert(method_exists($assetModel, 'getAllWithHolderName'), "Asset::getAllWithHolderName method exists");

            $assetLogModel = new AssetLog();
            $this->assert(method_exists($assetLogModel, 'getLogsByAssetId'), "AssetLog::getLogsByAssetId method exists");
        } catch (Exception $e) {
            $this->assert(false, "Asset functionality testing failed: " . $e->getMessage());
        }

        // Test Google Maps Service
        try {
            $googleMapsService = new GoogleMapsService();
            $this->assert(method_exists($googleMapsService, 'calculateDistance'), "GoogleMapsService::calculateDistance method exists");
            $this->assert(method_exists($googleMapsService, 'calculateTotalDistance'), "GoogleMapsService::calculateTotalDistance method exists");
        } catch (Exception $e) {
            $this->assert(false, "Google Maps Service testing failed: " . $e->getMessage());
        }
    }

    private function assert($condition, $message)
    {
        if ($condition) {
            echo "  ✓ $message\n";
            $this->passed++;
        } else {
            echo "  ✗ $message\n";
            $this->failed++;
            $this->errors[] = $message;
        }
    }

    private function printResults()
    {
        echo "\n=== Test Results ===\n";
        echo "Passed: {$this->passed}\n";
        echo "Failed: {$this->failed}\n";
        echo "Total: " . ($this->passed + $this->failed) . "\n";

        if (!empty($this->errors)) {
            echo "\nErrors:\n";
            foreach ($this->errors as $error) {
                echo "  - $error\n";
            }
        }

        if ($this->failed === 0) {
            echo "\n🎉 All tests passed!\n";
        } else {
            echo "\n❌ Some tests failed. Please review the errors above.\n";
        }
    }
}

// Run tests if this file is executed directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $tests = new BasicTests();
    $tests->runAllTests();
}
