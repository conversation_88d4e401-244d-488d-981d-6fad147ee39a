# 🔄 Database Migratie Instructies - Online Omgeving

## 📋 Overzicht

Dit document beschrijft hoe je de online database kunt migreren zodat deze compatibel is met de nieuwe code structuur.

## ⚠️ BELANGRIJK - MAAK EERST EEN BACKUP!

```bash
# Maak een backup van de huidige database
mysqldump -u username -p jvanderpluijm_reiskosten_X1 > backup_before_migration_$(date +%Y%m%d_%H%M%S).sql
```

## 🚀 Migratie Uitvoeren

### Optie 1: Via Web Interface (Aanbevolen)

1. **Log in als admin** op de website
2. **Ga naar**: `https://jouwdomain.com/migrate-online-database.php`
3. **Klik op "Check Database Status"** om te zien wat er aangepast moet worden
4. **Klik op "Voer Migratie Uit"** om de migratie te starten
5. **Controleer de resultaten** en log output

### Optie 2: Via Command Line

```bash
# Upload het migratie script naar de server
scp sql/migrate_online_database.sql user@server:/path/to/project/sql/

# Voer de migratie uit
mysql -u username -p jvanderpluijm_reiskosten_X1 < sql/migrate_online_database.sql
```

## 📊 Wat wordt er aangepast?

### 🗃️ Assets Tabel
- ✅ `type_id` kolom toegevoegd (vervangt `type`)
- ✅ `description` kolom toegevoegd
- ✅ `updated_at` kolom toegevoegd
- ✅ `visibility` gewijzigd van tinyint naar varchar
- ✅ Foreign keys toegevoegd

### 🗃️ Declaraties Tabel
- ✅ `project_number` kolom toegevoegd
- ✅ `btw_percentage` kolom toegevoegd
- ✅ `bestand_pad` kolom toegevoegd
- ✅ `updated_at` kolom toegevoegd
- ✅ `submitted_at` kolom toegevoegd

### 🗃️ Asset_Types Tabel
- ✅ Nieuwe tabel aangemaakt
- ✅ Standaard types toegevoegd

### 🗃️ Users Tabel
- ✅ `default_address` kolom toegevoegd

### 🗃️ Travel_Records Tabel
- ✅ `updated_at` kolom toegevoegd (indien nodig)
- ✅ `submitted_at` kolom toegevoegd (indien nodig)

### 🗃️ Asset_Logs Tabel
- ✅ `notes` kolom toegevoegd
- ✅ `action` enum geoptimaliseerd

## 🔍 Verificatie na Migratie

### 1. Check Database Structuur
```sql
-- Controleer of alle nieuwe kolommen bestaan
DESCRIBE assets;
DESCRIBE declaraties;
DESCRIBE asset_types;
```

### 2. Test de Applicatie
- ✅ Log in op de website
- ✅ Test declaraties aanmaken met projectnummer
- ✅ Test asset management
- ✅ Controleer of alle pagina's laden zonder errors

### 3. Check Logs
- ✅ Controleer server error logs
- ✅ Controleer database logs
- ✅ Test alle CRUD operaties

## 🔄 Rollback Procedure

Als er problemen zijn na de migratie:

### Via Web Interface
```bash
# Upload rollback script
scp sql/rollback_migration.sql user@server:/path/to/project/sql/

# Voer rollback uit
mysql -u username -p jvanderpluijm_reiskosten_X1 < sql/rollback_migration.sql
```

### Herstel van Backup
```bash
# Herstel volledige database van backup
mysql -u username -p jvanderpluijm_reiskosten_X1 < backup_before_migration_YYYYMMDD_HHMMSS.sql
```

## 📝 Checklist voor Deployment

### Voor Migratie
- [ ] ✅ Backup van database gemaakt
- [ ] ✅ Nieuwe code geüpload naar server
- [ ] ✅ Migratie scripts geüpload
- [ ] ✅ Admin toegang gecontroleerd

### Tijdens Migratie
- [ ] ✅ Database status gecontroleerd
- [ ] ✅ Migratie uitgevoerd
- [ ] ✅ Resultaten gecontroleerd
- [ ] ✅ Geen kritieke errors

### Na Migratie
- [ ] ✅ Website functionaliteit getest
- [ ] ✅ Alle pagina's laden correct
- [ ] ✅ CRUD operaties werken
- [ ] ✅ Geen PHP errors in logs
- [ ] ✅ Database integriteit gecontroleerd

## 🆘 Troubleshooting

### Veel voorkomende problemen:

#### 1. "Column already exists" errors
```
Dit is normaal - de migratie script gebruikt IF NOT EXISTS
```

#### 2. Foreign key constraint errors
```sql
-- Controleer of referentie tabellen bestaan
SHOW TABLES;
```

#### 3. Permission errors
```sql
-- Controleer database permissions
SHOW GRANTS FOR CURRENT_USER();
```

#### 4. PHP errors na migratie
```bash
# Controleer PHP error logs
tail -f /var/log/php_errors.log
```

## 📞 Support

Als er problemen zijn tijdens de migratie:

1. **Stop de migratie** onmiddellijk
2. **Maak screenshots** van error messages
3. **Check de database logs**
4. **Overweeg rollback** als kritieke functionaliteit niet werkt
5. **Documenteer alle stappen** voor debugging

## ✅ Success Criteria

De migratie is succesvol als:

- ✅ Alle nieuwe kolommen bestaan
- ✅ Website laadt zonder errors
- ✅ Declaraties kunnen worden aangemaakt met projectnummer
- ✅ Asset management werkt correct
- ✅ Geen data verlies
- ✅ Alle bestaande functionaliteit werkt nog

---

**Datum**: 7 juli 2025  
**Versie**: 1.0  
**Auteur**: Database Migratie Script
