const { defineConfig } = require('cypress')

module.exports = defineConfig({
  e2e: {
    baseUrl: 'https://ecosprongapp.local',
    viewportWidth: 1280,
    viewportHeight: 800,
    defaultCommandTimeout: 10000, // Verhoogd van 5000 naar 10000
    pageLoadTimeout: 30000, // Verhoogd van standaard 60000 naar 30000
    retries: {
      runMode: 2, // Retry tests in run mode
      openMode: 1 // Retry tests in open mode
    },
    setupNodeEvents(on, config) {
      // implement node event listeners here
    },
  },
})
