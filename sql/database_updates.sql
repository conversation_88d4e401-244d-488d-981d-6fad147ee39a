-- Database updates voor de MVC-structuur

-- Voeg een kolom toe voor het pad naar het geüploade bestand
ALTER TABLE declaraties ADD COLUMN IF NOT EXISTS bestand_pad VARCHAR(255) DEFAULT NULL AFTER bon_factuur_gemaild;

-- Voeg een updated_at kolom toe aan de declaraties tabel
ALTER TABLE declaraties ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP NULL DEFAULT NULL AFTER aanmaakdatum;

-- Voeg een updated_at kolom toe aan de travel_records tabel
ALTER TABLE travel_records ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP NULL DEFAULT NULL AFTER date;

-- Voeg een submitted_at kolom toe aan de declaraties tabel
ALTER TABLE declaraties ADD COLUMN IF NOT EXISTS submitted_at TIMESTAMP NULL DEFAULT NULL AFTER submitted;

-- Voeg een submitted_at kolom toe aan de travel_records tabel
ALTER TABLE travel_records ADD COLUMN IF NOT EXISTS submitted_at TIMESTAMP NULL DEFAULT NULL AFTER submitted;

-- Voeg een default_address kolom toe aan de users tabel
ALTER TABLE users ADD COLUMN IF NOT EXISTS default_address VARCHAR(255) DEFAULT NULL AFTER is_admin;

-- Zorg ervoor dat alle declaraties standaard niet ingediend zijn
UPDATE declaraties SET submitted = 0 WHERE submitted IS NULL;

-- Zorg ervoor dat alle reiskosten standaard niet ingediend zijn
UPDATE travel_records SET submitted = 0 WHERE submitted IS NULL;

-- Zorg ervoor dat alle declaraties standaard niet betaald zijn
UPDATE declaraties SET paid = 0 WHERE paid IS NULL;

-- Zorg ervoor dat alle reiskosten standaard niet betaald zijn
UPDATE travel_records SET paid = 0 WHERE paid IS NULL;

-- Zorg ervoor dat alle declaraties standaard niet afgekeurd zijn
UPDATE declaraties SET rejected = 0 WHERE rejected IS NULL;

-- Zorg ervoor dat alle reiskosten standaard niet afgekeurd zijn
UPDATE travel_records SET rejected = 0 WHERE rejected IS NULL;
