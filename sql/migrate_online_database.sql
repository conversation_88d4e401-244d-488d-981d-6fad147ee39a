-- =====================================================
-- MIGRATIE SCRIPT VOOR ONLINE DATABASE
-- Van online structuur naar nieuwe code compatibiliteit
-- =====================================================

-- Start transactie voor veiligheid
START TRANSACTION;

-- =====================================================
-- 1. ASSETS TABEL UPDATES
-- =====================================================

-- Voeg ontbrekende kolommen toe aan assets tabel
ALTER TABLE assets 
ADD COLUMN IF NOT EXISTS type_id INT DEFAULT NULL AFTER model,
ADD COLUMN IF NOT EXISTS description TEXT DEFAULT NULL AFTER type_id,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP NULL DEFAULT NULL AFTER holder_since;

-- Wijzig type kolom naar type_id (als type nog bestaat)
-- Eerst controleren of type kolom bestaat
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'assets'
    AND COLUMN_NAME = 'type'
);

-- Als type kolom bestaat, migreer data naar type_id
SET @migrate_type = IF(@column_exists > 0,
    'UPDATE assets SET type_id = (SELECT id FROM asset_types WHERE name = assets.type LIMIT 1) WHERE type IS NOT NULL',
    'SELECT "type column does not exist" as message');

PREPARE stmt FROM @migrate_type;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Verwijder oude type kolom als deze bestaat
SET @drop_type = IF(@column_exists > 0,
    'ALTER TABLE assets DROP COLUMN type',
    'SELECT "type column already removed" as message');

PREPARE stmt FROM @drop_type;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Wijzig visibility van tinyint naar varchar
ALTER TABLE assets MODIFY COLUMN visibility VARCHAR(50) DEFAULT 'public';

-- Update visibility waarden
UPDATE assets SET visibility = 'public' WHERE visibility = 1 OR visibility IS NULL;
UPDATE assets SET visibility = 'private' WHERE visibility = 0;

-- =====================================================
-- 2. ASSET_TYPES TABEL AANMAKEN
-- =====================================================

-- Maak asset_types tabel als deze niet bestaat
CREATE TABLE IF NOT EXISTS asset_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Voeg standaard asset types toe
INSERT IGNORE INTO asset_types (name, description) VALUES
('Laptop', 'Laptops en notebooks'),
('Monitor', 'Computer monitoren'),
('Telefoon', 'Mobiele telefoons'),
('Tablet', 'Tablets en iPads'),
('Camera', 'Camera\'s en fotoapparatuur'),
('Audio', 'Audio apparatuur'),
('Meetapparatuur', 'Meetinstrumenten'),
('Overig', 'Overige apparatuur');

-- =====================================================
-- 3. DECLARATIES TABEL UPDATES
-- =====================================================

-- Voeg ontbrekende kolommen toe aan declaraties tabel
ALTER TABLE declaraties 
ADD COLUMN IF NOT EXISTS project_number VARCHAR(50) NOT NULL DEFAULT 'Algemeen' AFTER user_id,
ADD COLUMN IF NOT EXISTS btw_percentage DECIMAL(5,2) NOT NULL DEFAULT 21.00 AFTER bedrag_excl_btw,
ADD COLUMN IF NOT EXISTS bestand_pad VARCHAR(255) DEFAULT NULL AFTER product_dienst,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP NULL DEFAULT NULL AFTER aanmaakdatum,
ADD COLUMN IF NOT EXISTS submitted_at TIMESTAMP NULL DEFAULT NULL AFTER submitted;

-- Update bestaande records met standaard waarden
UPDATE declaraties SET project_number = 'Algemeen' WHERE project_number = '' OR project_number IS NULL;
UPDATE declaraties SET btw_percentage = 21.00 WHERE btw_percentage IS NULL;

-- =====================================================
-- 4. TRAVEL_RECORDS TABEL UPDATES
-- =====================================================

-- Controleer of travel_records al de juiste structuur heeft
-- (Deze tabel lijkt al correct te zijn in de online database)

-- Voeg eventueel ontbrekende kolommen toe
ALTER TABLE travel_records 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at,
ADD COLUMN IF NOT EXISTS submitted_at TIMESTAMP NULL DEFAULT NULL AFTER submitted;

-- =====================================================
-- 5. USERS TABEL UPDATES
-- =====================================================

-- Voeg default_address kolom toe als deze niet bestaat
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS default_address VARCHAR(255) DEFAULT NULL AFTER is_admin;

-- =====================================================
-- 6. ASSET_LOGS TABEL UPDATES
-- =====================================================

-- Controleer of asset_logs tabel de juiste structuur heeft
-- Voeg notes kolom toe als deze niet bestaat
ALTER TABLE asset_logs 
ADD COLUMN IF NOT EXISTS notes TEXT DEFAULT NULL AFTER comments;

-- Zorg ervoor dat action kolom bestaat met juiste enum waarden
ALTER TABLE asset_logs 
MODIFY COLUMN action ENUM('uitgeleend', 'ingeleverd', 'onderhoud', 'afgeschreven') NOT NULL DEFAULT 'uitgeleend';

-- =====================================================
-- 7. FOREIGN KEY CONSTRAINTS TOEVOEGEN
-- =====================================================

-- Voeg foreign key toe voor assets.type_id
ALTER TABLE assets 
ADD CONSTRAINT fk_assets_type_id 
FOREIGN KEY (type_id) REFERENCES asset_types(id) ON DELETE SET NULL;

-- Voeg foreign key toe voor assets.current_holder
ALTER TABLE assets 
ADD CONSTRAINT fk_assets_current_holder 
FOREIGN KEY (current_holder) REFERENCES users(id) ON DELETE SET NULL;

-- =====================================================
-- 8. INDEXES TOEVOEGEN VOOR PERFORMANCE
-- =====================================================

-- Voeg indexes toe voor betere performance
CREATE INDEX IF NOT EXISTS idx_assets_current_holder ON assets(current_holder);
CREATE INDEX IF NOT EXISTS idx_assets_type_id ON assets(type_id);
CREATE INDEX IF NOT EXISTS idx_declaraties_project_number ON declaraties(project_number);
CREATE INDEX IF NOT EXISTS idx_travel_records_project_number ON travel_records(project_number);
CREATE INDEX IF NOT EXISTS idx_asset_logs_asset_id ON asset_logs(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_logs_user_id ON asset_logs(user_id);

-- =====================================================
-- 9. DATA CLEANUP (OPTIONEEL)
-- =====================================================

-- Verwijder bon_factuur_gemaild kolom als deze niet meer gebruikt wordt
-- (Uitgecommentarieerd voor veiligheid - kan later handmatig uitgevoerd worden)
-- ALTER TABLE declaraties DROP COLUMN IF EXISTS bon_factuur_gemaild;

-- =====================================================
-- 10. VERIFICATIE QUERIES
-- =====================================================

-- Toon de nieuwe structuren ter verificatie
SELECT 'ASSETS TABLE STRUCTURE:' as info;
DESCRIBE assets;

SELECT 'DECLARATIES TABLE STRUCTURE:' as info;
DESCRIBE declaraties;

SELECT 'TRAVEL_RECORDS TABLE STRUCTURE:' as info;
DESCRIBE travel_records;

SELECT 'ASSET_TYPES TABLE STRUCTURE:' as info;
DESCRIBE asset_types;

-- Toon aantal records per tabel
SELECT 'RECORD COUNTS:' as info;
SELECT 'assets' as table_name, COUNT(*) as record_count FROM assets
UNION ALL
SELECT 'declaraties' as table_name, COUNT(*) as record_count FROM declaraties
UNION ALL
SELECT 'travel_records' as table_name, COUNT(*) as record_count FROM travel_records
UNION ALL
SELECT 'users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'asset_types' as table_name, COUNT(*) as record_count FROM asset_types;

-- Commit de transactie als alles goed is gegaan
COMMIT;

SELECT 'MIGRATIE VOLTOOID!' as status, NOW() as completed_at;
