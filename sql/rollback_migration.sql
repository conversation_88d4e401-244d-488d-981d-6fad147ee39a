-- =====================================================
-- ROLLBACK SCRIPT VOOR DATABASE MIGRATIE
-- Gebruik dit alleen als de migratie problemen veroorzaakt
-- =====================================================

-- WAARSCHUWING: Dit script verwijdert kolommen en data!
-- Maak eerst een backup van de database!

START TRANSACTION;

-- =====================================================
-- 1. VERWIJDER NIEUWE KOLOMMEN UIT ASSETS
-- =====================================================

-- Verwijder foreign key constraints eerst
ALTER TABLE assets DROP FOREIGN KEY IF EXISTS fk_assets_type_id;
ALTER TABLE assets DROP FOREIGN KEY IF EXISTS fk_assets_current_holder;

-- Verwijder nieuwe kolommen
ALTER TABLE assets DROP COLUMN IF EXISTS type_id;
ALTER TABLE assets DROP COLUMN IF EXISTS description;
ALTER TABLE assets DROP COLUMN IF EXISTS updated_at;

-- Herstel visibility naar tinyint
ALTER TABLE assets MODIFY COLUMN visibility TINYINT(1) DEFAULT NULL;
UPDATE assets SET visibility = 1 WHERE visibility = 'public';
UPDATE assets SET visibility = 0 WHERE visibility = 'private';

-- Voeg type kolom terug toe (als backup data beschikbaar is)
ALTER TABLE assets ADD COLUMN type VARCHAR(255) DEFAULT NULL AFTER model;

-- =====================================================
-- 2. VERWIJDER ASSET_TYPES TABEL
-- =====================================================

DROP TABLE IF EXISTS asset_types;

-- =====================================================
-- 3. VERWIJDER NIEUWE KOLOMMEN UIT DECLARATIES
-- =====================================================

ALTER TABLE declaraties DROP COLUMN IF EXISTS project_number;
ALTER TABLE declaraties DROP COLUMN IF EXISTS btw_percentage;
ALTER TABLE declaraties DROP COLUMN IF EXISTS bestand_pad;
ALTER TABLE declaraties DROP COLUMN IF EXISTS updated_at;
ALTER TABLE declaraties DROP COLUMN IF EXISTS submitted_at;

-- =====================================================
-- 4. VERWIJDER NIEUWE KOLOMMEN UIT TRAVEL_RECORDS
-- =====================================================

-- Alleen als deze kolommen nieuw toegevoegd waren
-- ALTER TABLE travel_records DROP COLUMN IF EXISTS updated_at;
-- ALTER TABLE travel_records DROP COLUMN IF EXISTS submitted_at;

-- =====================================================
-- 5. VERWIJDER NIEUWE KOLOMMEN UIT USERS
-- =====================================================

ALTER TABLE users DROP COLUMN IF EXISTS default_address;

-- =====================================================
-- 6. VERWIJDER NIEUWE KOLOMMEN UIT ASSET_LOGS
-- =====================================================

ALTER TABLE asset_logs DROP COLUMN IF EXISTS notes;

-- Herstel action kolom naar originele staat (als nodig)
-- ALTER TABLE asset_logs MODIFY COLUMN action VARCHAR(50) DEFAULT NULL;

-- =====================================================
-- 7. VERWIJDER INDEXES
-- =====================================================

DROP INDEX IF EXISTS idx_assets_current_holder ON assets;
DROP INDEX IF EXISTS idx_assets_type_id ON assets;
DROP INDEX IF EXISTS idx_declaraties_project_number ON declaraties;
DROP INDEX IF EXISTS idx_travel_records_project_number ON travel_records;
DROP INDEX IF EXISTS idx_asset_logs_asset_id ON asset_logs;
DROP INDEX IF EXISTS idx_asset_logs_user_id ON asset_logs;

-- =====================================================
-- VERIFICATIE
-- =====================================================

SELECT 'ROLLBACK VOLTOOID!' as status, NOW() as completed_at;

-- Toon de teruggedraaide structuren
SELECT 'ASSETS TABLE STRUCTURE AFTER ROLLBACK:' as info;
DESCRIBE assets;

SELECT 'DECLARATIES TABLE STRUCTURE AFTER ROLLBACK:' as info;
DESCRIBE declaraties;

COMMIT;

SELECT 'LET OP: Controleer of alle data correct is teruggezet!' as warning;
