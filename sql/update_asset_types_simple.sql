-- Maak de asset_types tabel
CREATE TABLE IF NOT EXISTS asset_types (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Voeg standaard asset types toe
INSERT INTO asset_types (name) VALUES ('Laptop');
INSERT INTO asset_types (name) VALUES ('Monitor');
INSERT INTO asset_types (name) VALUES ('Telefoon');
INSERT INTO asset_types (name) VALUES ('Tablet');
INSERT INTO asset_types (name) VALUES ('Printer');
INSERT INTO asset_types (name) VALUES ('Muis');
INSERT INTO asset_types (name) VALUES ('Toetsenbord');
INSERT INTO asset_types (name) VALUES ('Headset');
INSERT INTO asset_types (name) VALUES ('Docking station');
INSERT INTO asset_types (name) VALUES ('Overig');

-- Controleer of de type_id kolom al bestaat
SELECT COUNT(*) INTO @column_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'assets' 
AND COLUMN_NAME = 'type_id';

-- Voeg de type_id kolom toe als deze nog niet bestaat
SET @add_column = CONCAT('ALTER TABLE assets ADD COLUMN ', IF(@column_exists = 0, 'type_id INT DEFAULT NULL', 'dummy INT'));
PREPARE stmt FROM @add_column;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Probeer de foreign key constraint toe te voegen
-- Dit kan een fout geven als de constraint al bestaat, maar dat is OK
ALTER TABLE assets ADD CONSTRAINT fk_asset_type FOREIGN KEY (type_id) REFERENCES asset_types(id) ON DELETE SET NULL;
