-- Voeg de ontbrekende kolommen toe aan de declaraties tabel
ALTER TABLE declaraties
ADD COLUMN IF NOT EXISTS submitted_at TIMESTAMP NULL DEFAULT NULL;

-- Voeg de ontbrekende kolommen toe aan de travel_records tabel
ALTER TABLE travel_records
ADD COLUMN IF NOT EXISTS submitted_at TIMESTAMP NULL DEFAULT NULL;

-- Controleer of de kolommen zijn toegevoegd
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'declaraties' AND COLUMN_NAME = 'submitted_at';

SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'travel_records' AND COLUMN_NAME = 'submitted_at';
