-- Database structuur voor de MVC-structuur

-- <PERSON><PERSON><PERSON><PERSON><PERSON> bestaande tabellen
DROP TABLE IF EXISTS declaraties;
DROP TABLE IF EXISTS travel_records;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS assets;
DROP TABLE IF EXISTS asset_logs;

-- Maak de users tabel
CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  is_admin TINYINT(1) NOT NULL DEFAULT 0,
  default_address VARCHAR(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Maak de travel_records tabel
CREATE TABLE travel_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  date DATE NOT NULL,
  updated_at TIMESTAMP NULL DEFAULT NULL,
  project_number VARCHAR(50) NOT NULL,
  address_a VARCHAR(255) NOT NULL,
  address_b VARCHAR(255) NOT NULL,
  extra_addresses TEXT DEFAULT NULL,
  single_trip_km DECIMAL(10,1) NOT NULL,
  total_km DECIMAL(10,1) NOT NULL,
  return_trip TINYINT(1) NOT NULL DEFAULT 0,
  submitted TINYINT(1) NOT NULL DEFAULT 0,
  submitted_at TIMESTAMP NULL DEFAULT NULL,
  paid TINYINT(1) NOT NULL DEFAULT 0,
  rejected TINYINT(1) NOT NULL DEFAULT 0,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Maak de declaraties tabel
CREATE TABLE declaraties (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  titel VARCHAR(255) NOT NULL,
  totaal_bedrag DECIMAL(10,2) NOT NULL,
  bedrag_excl_btw DECIMAL(10,2) NOT NULL,
  product_dienst TEXT NOT NULL,
  bon_factuur_gemaild TINYINT(1) NOT NULL DEFAULT 0,
  bestand_pad VARCHAR(255) DEFAULT NULL,
  aanmaakdatum TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT NULL,
  submitted TINYINT(1) NOT NULL DEFAULT 0,
  submitted_at TIMESTAMP NULL DEFAULT NULL,
  paid TINYINT(1) NOT NULL DEFAULT 0,
  rejected TINYINT(1) NOT NULL DEFAULT 0,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Maak de asset_types tabel
CREATE TABLE asset_types (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Voeg standaard asset types toe
INSERT INTO asset_types (name) VALUES
('Laptop'),
('Monitor'),
('Telefoon'),
('Tablet'),
('Printer'),
('Muis'),
('Toetsenbord'),
('Headset'),
('Docking station'),
('Overig');

-- Maak de assets tabel
CREATE TABLE assets (
  id INT AUTO_INCREMENT PRIMARY KEY,
  uid VARCHAR(50) NOT NULL UNIQUE,
  name VARCHAR(255) NOT NULL,
  brand VARCHAR(255) DEFAULT NULL,
  model VARCHAR(255) DEFAULT NULL,
  type_id INT DEFAULT NULL,
  description TEXT DEFAULT NULL,
  serial_number VARCHAR(255) DEFAULT NULL,
  purchase_date DATE DEFAULT NULL,
  purchase_value DECIMAL(10,2) DEFAULT NULL,
  quantity INT DEFAULT 1,
  product_link VARCHAR(255) DEFAULT NULL,
  status ENUM('beschikbaar', 'uitgeleend', 'onderhoud', 'afgeschreven') NOT NULL DEFAULT 'beschikbaar',
  current_holder INT DEFAULT NULL,
  holder_since DATETIME DEFAULT NULL,
  comments TEXT DEFAULT NULL,
  visibility VARCHAR(50) DEFAULT 'public',
  user_group VARCHAR(50) DEFAULT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT NULL,
  FOREIGN KEY (current_holder) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (type_id) REFERENCES asset_types(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Maak de asset_logs tabel
CREATE TABLE asset_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  asset_id INT NOT NULL,
  user_id INT DEFAULT NULL,
  action ENUM('uitgeleend', 'ingeleverd', 'onderhoud', 'afgeschreven') NOT NULL,
  notes TEXT DEFAULT NULL,
  start_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  end_date DATETIME DEFAULT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Voeg een standaard admin gebruiker toe
INSERT INTO users (username, password, is_admin) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1); -- wachtwoord: password
