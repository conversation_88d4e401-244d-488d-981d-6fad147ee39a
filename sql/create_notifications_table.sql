-- Maak notifications tabel voor admin alerts
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    data JSON,
    is_read B<PERSON><PERSON><PERSON>N DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at),
    INDEX idx_type (type)
);

-- Voeg enkele test notificaties toe (optioneel)
-- INSERT INTO notifications (user_id, type, message, data) VALUES 
-- (1, 'asset_assigned', 'Asset HDH16234 toegewezen aan gebruiker pelle', '{"asset_id": 1, "asset_uid": "HDH16234", "assigned_to": "pelle"}'),
-- (2, 'declaration_submitted', 'Declaratie ingediend door esmee (€125.50)', '{"declaration_id": 5, "amount": 125.50}');
