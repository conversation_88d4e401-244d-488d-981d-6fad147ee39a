-- Controleer of de updated_at kolom bestaat
SELECT COUNT(*) INTO @column_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'assets' 
AND COLUMN_NAME = 'updated_at';

-- Voeg de updated_at kolom toe als deze nog niet bestaat
SET @add_column = CONCAT('ALTER TABLE assets ADD COLUMN ', 
                         IF(@column_exists = 0, 
                            'updated_at TIMESTAMP NULL DEFAULT NULL', 
                            'dummy INT'));
PREPARE stmt FROM @add_column;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
