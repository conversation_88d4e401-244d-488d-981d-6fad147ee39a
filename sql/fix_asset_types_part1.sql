-- Stap 1: Maak de asset_types tabel
CREATE TABLE IF NOT EXISTS asset_types (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Stap 2: Voeg standaard asset types toe
INSERT INTO asset_types (name) VALUES ('Laptop');
INSERT INTO asset_types (name) VALUES ('Monitor');
INSERT INTO asset_types (name) VALUES ('Telefoon');
INSERT INTO asset_types (name) VALUES ('Tablet');
INSERT INTO asset_types (name) VALUES ('Printer');
INSERT INTO asset_types (name) VALUES ('Muis');
INSERT INTO asset_types (name) VALUES ('Toetsenbord');
INSERT INTO asset_types (name) VALUES ('Headset');
INSERT INTO asset_types (name) VALUES ('Docking station');
INSERT INTO asset_types (name) VALUES ('Overig');

-- Stap 3: Voeg de type_id kolom toe aan de assets tabel
ALTER TABLE assets ADD COLUMN type_id INT DEFAULT NULL;
