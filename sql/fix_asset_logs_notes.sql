-- Fix asset_logs table to ensure correct structure
-- This script safely adds missing columns without causing duplicate column errors

-- Controleer of de notes kolom bestaat
SET @notes_exists = (
    SELECT COUNT(*)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'asset_logs'
    AND COLUMN_NAME = 'notes'
);

-- Voeg de notes kolom toe als deze nog niet bestaat
SET @add_notes = IF(@notes_exists = 0,
                   'ALTER TABLE asset_logs ADD COLUMN notes TEXT DEFAULT NULL',
                   'SELECT "notes column already exists" as message');

PREPARE stmt FROM @add_notes;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Controleer of de action kolom bestaat
SET @action_exists = (
    SELECT COUNT(*)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'asset_logs'
    AND COLUMN_NAME = 'action'
);

-- Voeg de action kolom toe als deze nog niet bestaat
SET @add_action = IF(@action_exists = 0,
                    'ALTER TABLE asset_logs ADD COLUMN action ENUM(\'uitgeleend\', \'ingeleverd\', \'onderhoud\', \'afgeschreven\') NOT NULL DEFAULT \'uitgeleend\'',
                    'SELECT "action column already exists" as message');

PREPARE stmt FROM @add_action;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Toon de resultaten
SELECT
    CASE
        WHEN @notes_exists = 0 THEN 'notes column was added'
        ELSE 'notes column already existed'
    END as notes_status,
    CASE
        WHEN @action_exists = 0 THEN 'action column was added'
        ELSE 'action column already existed'
    END as action_status;

-- Toon de huidige structuur
DESCRIBE asset_logs;
