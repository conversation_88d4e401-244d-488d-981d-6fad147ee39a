-- Fix asset_logs table to ensure notes column exists

-- Controleer of de notes kolom bestaat
SELECT COUNT(*) INTO @notes_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'asset_logs' 
AND COLUMN_NAME = 'notes';

-- Voeg de notes kolom toe als deze nog niet bestaat
SET @add_notes = IF(@notes_exists = 0, 
                   'ALTER TABLE asset_logs ADD COLUMN notes TEXT DEFAULT NULL', 
                   'SELECT "notes column already exists" as message');

PREPARE stmt FROM @add_notes;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Controleer of de action kolom bestaat
SELECT COUNT(*) INTO @action_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'asset_logs' 
AND COLUMN_NAME = 'action';

-- Voeg de action kolom toe als deze nog niet bestaat
SET @add_action = IF(@action_exists = 0, 
                    'ALTER TABLE asset_logs ADD COLUMN action ENUM(\'uitgeleend\', \'ingeleverd\', \'onderhoud\', \'afgeschreven\') NOT NULL DEFAULT \'uitgeleend\'', 
                    'SELECT "action column already exists" as message');

PREPARE stmt FROM @add_action;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Toon de huidige structuur
DESCRIBE asset_logs;
