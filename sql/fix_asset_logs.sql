-- Controleer of de asset_logs tabel bestaat
CREATE TABLE IF NOT EXISTS asset_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  asset_id INT NOT NULL,
  user_id INT DEFAULT NULL,
  notes TEXT DEFAULT NULL,
  start_date D<PERSON>ETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  end_date DATETIME DEFAULT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Controleer of de action kolom bestaat
SELECT COUNT(*) INTO @column_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'asset_logs' 
AND COLUMN_NAME = 'action';

-- Voeg de action kolom toe als deze nog niet bestaat
SET @add_column = CONCAT('ALTER TABLE asset_logs ADD COLUMN ', 
                         IF(@column_exists = 0, 
                            "action ENUM('uitgeleend', 'ingeleverd', 'onderhoud', 'afgeschreven') NOT NULL DEFAULT 'uitgeleend'", 
                            'dummy INT'));
PREPARE stmt FROM @add_column;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
