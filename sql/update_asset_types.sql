-- Maak de asset_types tabel
CREATE TABLE IF NOT EXISTS asset_types (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Voeg standaard asset types toe
INSERT INTO asset_types (name) VALUES
('Laptop'),
('Monitor'),
('Telefoon'),
('Tablet'),
('Printer'),
('Muis'),
('Toetsenbord'),
('Headset'),
('Docking station'),
('Overig');

-- Update de assets tabel om type_id te ondersteunen
ALTER TABLE assets ADD COLUMN IF NOT EXISTS type_id INT DEFAULT NULL;

-- Controleer of de constraint al bestaat voordat we deze toevoegen
SET @constraint_exists = (
    SELECT COUNT(*)
    FROM information_schema.TABLE_CONSTRAINTS
    WHERE CONSTRAINT_SCHEMA = DATABASE()
    AND TABLE_NAME = 'assets'
    AND CONSTRAINT_NAME = 'fk_asset_type'
);

-- Voeg de constraint alleen toe als deze nog niet bestaat
SET @sql = IF(@constraint_exists = 0,
    'ALTER TABLE assets ADD CONSTRAINT fk_asset_type FOREIGN KEY (type_id) REFERENCES asset_types(id) ON DELETE SET NULL',
    'SELECT "Constraint already exists"'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
