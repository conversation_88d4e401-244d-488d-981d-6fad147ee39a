<?php
/**
 * Applicatie configuratie
 */
// Bepaal de base_url op basis van de huidige request
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
$base_url = $protocol . '://' . $host;

return [
    'name' => 'Reiskosten & Declaraties',
    'url' => 'http://localhost',
    'base_url' => $base_url, // Dynamisch bepaalde base URL met protocol, domein en poortnummer
    'debug' => true,
    'timezone' => 'Europe/Amsterdam',
    'locale' => 'nl',
    'uploads_dir' => __DIR__ . '/../public/uploads',
    'google_maps_api_key' => 'AIzaSyB4b2J4ydc8GduIJI9h7R_jD84r4f3nq28',
];
