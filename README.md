# Reiskosten & Declaraties Applicatie

Een applicatie voor het beheren van reiskosten en declaraties.

## Installatie

1. Clone de repository:
   ```
   git clone <repository-url>
   ```

2. Maak een database aan:
   ```sql
   CREATE DATABASE jvanderpluijm_reiskosten_X1;
   ```

3. Importeer de database-structuur:
   ```
   mysql -u root -p jvanderpluijm_reiskosten_X1 < sql/database_structure.sql
   ```

4. Pas de database-configuratie aan in `config/database.php`:
   ```php
   return [
       'host' => '127.0.0.1',
       'dbname' => 'jvanderpluijm_reiskosten_X1',
       'username' => 'root',
       'password' => 'password',
       'charset' => 'utf8',
       'options' => [
           PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
           PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
           PDO::ATTR_EMULATE_PREPARES => false,
       ],
   ];
   ```

5. Maak de uploads directory aan:
   ```
   mkdir -p public/uploads/declaraties
   chmod 755 public/uploads/declaraties
   ```

6. Configureer je webserver om de `public` map als document root te gebruiken.

## Gebruik

1. Open de applicatie in je browser.
2. Log in met de standaard admin-gebruiker:
   - Gebruikersnaam: `admin`
   - Wachtwoord: `password`
3. Ga naar het admin-paneel om gebruikers aan te maken.
4. Gebruikers kunnen inloggen en reiskosten en declaraties toevoegen.

## Functionaliteiten

### Reiskosten
- Toevoegen van reiskosten
- Bewerken van reiskosten
- Verwijderen van reiskosten
- Indienen van reiskosten
- Goedkeuren of afkeuren van reiskosten (admin)
- Markeren als betaald (admin)

### Declaraties
- Toevoegen van declaraties
- Bewerken van declaraties
- Verwijderen van declaraties
- Indienen van declaraties
- Goedkeuren of afkeuren van declaraties (admin)
- Markeren als betaald (admin)
- Uploaden van bonnetjes/facturen

### Gebruikersbeheer (admin)
- Toevoegen van gebruikers
- Bewerken van gebruikers
- Verwijderen van gebruikers
- Instellen van standaardadres voor reiskosten

## Structuur

De applicatie is gebouwd volgens het MVC (Model-View-Controller) patroon:

- **Models**: Klassen die de database-interactie en business-logica bevatten
- **Views**: Templates voor de gebruikersinterface
- **Controllers**: Klassen die de verzoeken afhandelen
- **Config**: Configuratiebestanden voor de applicatie
- **Public**: Publiek toegankelijke bestanden (CSS, JavaScript, uploads)

## Technologieën

- PHP 7.4+
- MySQL 5.7+
- HTML5
- CSS3 (Tailwind CSS)
- JavaScript

## Licentie

Proprietary - Alle rechten voorbehouden.
