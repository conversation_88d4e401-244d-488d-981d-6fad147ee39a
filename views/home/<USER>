<div class="max-w-7xl mx-auto px-4 py-6">
    <div class="bg-white shadow rounded-lg p-6">
        <h1 class="text-2xl font-semibold mb-6">Dashboard</h1>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <!-- Reiskosten kaart -->
            <div class="bg-blue-50 rounded-lg p-6 shadow-sm">
                <h2 class="text-xl font-semibold text-blue-800 mb-4">Reiskosten</h2>

                <div class="mb-4">
                    <p class="text-gray-700">
                        <span class="font-medium">Ingediende, niet uitbetaalde reiskosten:</span>
                        <?php echo $totalPendingTravelRecords; ?> records
                    </p>
                    <p class="text-gray-700">
                        <span class="font-medium">Totaal aantal kilometers (openstaand):</span>
                        <?php echo number_format($totalPendingKm, 1, ',', '.'); ?> km
                    </p>
                    <p class="text-gray-700 border-t pt-2 mt-2">
                        <span class="font-medium text-green-700">Totaal uitbetaalde kilometers:</span>
                        <?php echo number_format($totalPaidKm, 1, ',', '.'); ?> km
                    </p>
                </div>

                <a
                    href="/travel_records"
                    class="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
                >
                    Bekijk reiskosten
                </a>
            </div>

            <!-- Declaraties kaart -->
            <div class="bg-green-50 rounded-lg p-6 shadow-sm">
                <h2 class="text-xl font-semibold text-green-800 mb-4">Declaraties</h2>

                <div class="mb-4">
                    <p class="text-gray-700">
                        <span class="font-medium">Ingediende, niet uitbetaalde declaraties:</span>
                        <?php echo $totalPendingDeclarations; ?> declaraties
                    </p>
                    <p class="text-gray-700">
                        <span class="font-medium">Totaalbedrag (openstaand):</span>
                        € <?php echo number_format($totalPendingAmount, 2, ',', '.'); ?>
                    </p>
                    <p class="text-gray-700 border-t pt-2 mt-2">
                        <span class="font-medium text-green-700">Totaal uitbetaalde bedrag:</span>
                        € <?php echo number_format($totalPaidAmount, 2, ',', '.'); ?>
                    </p>
                </div>

                <a
                    href="/declarations"
                    class="inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
                >
                    Bekijk declaraties
                </a>
            </div>
        </div>

        <?php if ($isAdmin): ?>
        <div class="mt-8">
            <h2 class="text-xl font-semibold mb-4">Admin functies</h2>

            <div class="flex flex-wrap gap-4">
                <a
                    href="/admin"
                    class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors"
                >
                    Admin Dashboard
                </a>

                <a
                    href="/admin/users"
                    class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
                >
                    Gebruikers beheren
                </a>

                <a
                    href="/admin/travel_records"
                    class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 transition-colors"
                >
                    Reiskosten beheren
                </a>

                <a
                    href="/admin/declarations"
                    class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
                >
                    Declaraties beheren
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
