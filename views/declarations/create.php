<div class="max-w-3xl mx-auto p-4 mt-6">
  <div class="bg-white shadow rounded p-6">
    <h1 class="text-2xl font-semibold mb-6">Nieuwe Declaratie Toevoegen</h1>

    <form method="POST" action="/declarations/store" class="space-y-4" enctype="multipart/form-data">
      <!-- Titel -->
      <div>
        <label for="titel" class="block mb-1 text-sm font-medium text-gray-700">Titel</label>
        <input
          type="text"
          name="titel"
          id="titel"
          required
          class="block w-full border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring focus:ring-blue-200"
        >
      </div>

      <!-- Totaal Bedrag -->
      <div>
        <label for="totaal_bedrag" class="block mb-1 text-sm font-medium text-gray-700">Totaal Bedrag (€)</label>
        <input
          type="number"
          name="totaal_bedrag"
          id="totaal_bedrag"
          step="0.01"
          required
          class="block w-full border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring focus:ring-blue-200"
        >
      </div>

      <!-- Bedrag excl. BTW -->
      <div>
        <label for="bedrag_excl_btw" class="block mb-1 text-sm font-medium text-gray-700">Bedrag excl. BTW (€)</label>
        <input
          type="number"
          name="bedrag_excl_btw"
          id="bedrag_excl_btw"
          step="0.01"
          required
          class="block w-full border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring focus:ring-blue-200"
        >
      </div>

      <!-- Product/Dienst -->
      <div>
        <label for="product_dienst" class="block mb-1 text-sm font-medium text-gray-700">Product/Dienst</label>
        <textarea
          name="product_dienst"
          id="product_dienst"
          rows="4"
          required
          class="block w-full border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring focus:ring-blue-200"
        ></textarea>
      </div>

      <!-- Bestand uploaden -->
      <div>
        <label for="bon_factuur" class="block mb-1 text-sm font-medium text-gray-700">Bon/Factuur uploaden</label>
        <input
          type="file"
          name="bon_factuur"
          id="bon_factuur"
          accept=".pdf,.jpg,.jpeg,.png"
          class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        >
        <p class="mt-1 text-xs text-gray-500">Toegestane bestandsformaten: PDF, JPG, JPEG, PNG</p>
      </div>

      <!-- Bon/Factuur gemaild -->
      <div class="flex items-center space-x-2">
        <input
          type="checkbox"
          name="bon_factuur_gemaild"
          id="bon_factuur_gemaild"
          value="1"
          class="h-4 w-4 border-gray-300 rounded focus:ring focus:ring-blue-500"
        >
        <label for="bon_factuur_gemaild" class="text-sm text-gray-700">Bon/factuur gemaild naar Jordi</label>
      </div>

      <!-- Knoppen -->
      <div class="flex space-x-2">
        <button
          type="submit"
          class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 focus:outline-none focus:ring focus:ring-blue-200"
        >
          Toevoegen
        </button>
        <a
          href="/declarations"
          class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 focus:outline-none focus:ring focus:ring-gray-200"
        >
          Annuleren
        </a>
      </div>
    </form>
  </div>
</div>
