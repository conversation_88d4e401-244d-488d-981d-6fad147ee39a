<div class="max-w-7xl mx-auto px-4 py-6">
    <h1 class="text-2xl font-semibold mb-6"><PERSON><PERSON>ati<PERSON></h1>

    <!-- Filterformulier -->
    <form method="GET" action="/declarations" id="filter-form" class="bg-white shadow-sm rounded-md p-4 mb-6">
        <!-- <PERSON><PERSON> van 4 kolommen op md+ -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
            <!-- Datum vanaf -->
            <div class="flex flex-col">
                <label for="date_from" class="mb-1 text-sm font-medium text-gray-700">
                    Datum vanaf:
                </label>
                <input
                    type="date"
                    id="date_from"
                    name="date_from"
                    value="<?php echo View::escape($dateFrom); ?>"
                    class="border border-gray-300 rounded py-1 px-2 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
            </div>

            <!-- Datum tot -->
            <div class="flex flex-col">
                <label for="date_to" class="mb-1 text-sm font-medium text-gray-700">
                    Datum tot:
                </label>
                <input
                    type="date"
                    id="date_to"
                    name="date_to"
                    value="<?php echo View::escape($dateTo); ?>"
                    class="border border-gray-300 rounded py-1 px-2 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
            </div>

            <!-- Checkboxes Status -->
            <div class="flex flex-col">
                <label class="mb-1 text-sm font-medium text-gray-700">Status:</label>

                <label class="flex items-center space-x-2 text-sm text-gray-700 mb-1">
                    <input
                        type="checkbox"
                        name="show_open"
                        <?php if ($showOpen) echo 'checked'; ?>
                        class="rounded focus:ring-blue-500"
                    >
                    <span>Openstaand</span>
                </label>

                <label class="flex items-center space-x-2 text-sm text-gray-700 mb-1">
                    <input
                        type="checkbox"
                        name="show_submitted"
                        <?php if ($showSubmitted) echo 'checked'; ?>
                        class="rounded focus:ring-blue-500"
                    >
                    <span>Ingediend</span>
                </label>

                <label class="flex items-center space-x-2 text-sm text-gray-700 mb-1">
                    <input
                        type="checkbox"
                        name="show_paid"
                        <?php if ($showPaid) echo 'checked'; ?>
                        class="rounded focus:ring-blue-500"
                    >
                    <span>Uitbetaald</span>
                </label>

                <label class="flex items-center space-x-2 text-sm text-gray-700 mb-1">
                    <input
                        type="checkbox"
                        name="show_rejected"
                        <?php if ($showRejected) echo 'checked'; ?>
                        class="rounded focus:ring-blue-500"
                    >
                    <span>Afgekeurd</span>
                </label>
            </div>
        </div>

        <!-- Filter buttons -->
        <div class="flex space-x-2">
            <button
                type="submit"
                class="bg-blue-600 text-white rounded-md px-4 py-2 hover:bg-blue-700 transition-colors"
            >
                Filter toepassen
            </button>
            <a
                href="/declarations"
                class="bg-gray-200 text-gray-700 rounded-md px-4 py-2 hover:bg-gray-300 transition-colors"
            >
                Filter resetten
            </a>
        </div>
    </form>

    <!-- Tabel met declaraties -->
    <form method="POST" action="/declarations/submit" class="bg-white shadow-sm rounded-md p-4">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 text-sm text-left">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="px-3 py-2">
                            <input
                                type="checkbox"
                                onclick="toggleAll(this)"
                                class="rounded focus:ring-blue-500"
                            >
                        </th>
                        <th class="px-3 py-2 font-medium text-gray-700">Titel</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Totaal Bedrag (€)</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Bedrag excl. BTW (€)</th>
                        <th class="px-3 py-2 font-medium text-gray-700">BTW %</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Product/Dienst</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Bon/Factuur</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Status</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Acties</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                <?php if (count($records) > 0): ?>
                    <?php foreach ($records as $record): ?>
                        <tr>
                            <td class="px-3 py-2">
                                <input
                                    type="checkbox"
                                    name="record_ids[]"
                                    value="<?php echo $record['id']; ?>"
                                    <?php if ($record['submitted']) echo 'disabled'; ?>
                                    class="rounded focus:ring-blue-500"
                                >
                            </td>
                            <td class="px-3 py-2"><?php echo View::escape($record['titel']); ?></td>
                            <td class="px-3 py-2"><?php echo number_format((float)$record['totaal_bedrag'], 2, ',', '.'); ?></td>
                            <td class="px-3 py-2"><?php echo number_format((float)$record['bedrag_excl_btw'], 2, ',', '.'); ?></td>
                            <td class="px-3 py-2"><?php echo number_format((float)($record['btw_percentage'] ?? 21), 0); ?>%</td>
                            <td class="px-3 py-2"><?php echo View::escape($record['product_dienst']); ?></td>
                            <td class="px-3 py-2">
                                <?php if (!empty($record['bestand_pad'])): ?>
                                    <?php
                                        // Extract filename from path
                                        $filename = basename($record['bestand_pad']);
                                    ?>
                                    <a
                                        href="/uploads/declaraties/<?php echo View::escape($filename); ?>"
                                        target="_blank"
                                        class="text-blue-600 hover:text-blue-800 transition-colors"
                                    >
                                        Bekijk bon/factuur
                                    </a>
                                <?php else: ?>
                                    <span class="text-red-600">✗</span> Geen bestand
                                <?php endif; ?>
                            </td>
                            <td class="px-3 py-2">
                                <?php
                                if ($record['paid']) {
                                    echo '<span class="text-green-600">Uitbetaald</span>';
                                } elseif ($record['rejected']) {
                                    echo '<span class="text-red-600 font-medium">Afgekeurd</span>';
                                } elseif ($record['submitted']) {
                                    echo '<span class="text-blue-600">Ingediend</span>';
                                } else {
                                    echo '<span class="text-gray-500">Niet ingediend</span>';
                                }
                                ?>
                            </td>
                            <td class="px-3 py-2 space-x-2">
                                <?php if (!$record['submitted']): ?>
                                    <a
                                        href="/declarations/edit/<?php echo $record['id']; ?>"
                                        class="text-blue-600 hover:text-blue-800 transition-colors"
                                    >
                                        Bewerken
                                    </a>
                                    <a
                                        href="/declarations/delete/<?php echo $record['id']; ?>"
                                        class="text-red-600 hover:text-red-800 transition-colors"
                                    >
                                        Verwijderen
                                    </a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="8" class="px-3 py-2 text-center text-gray-500">
                            Geen declaraties gevonden.
                        </td>
                    </tr>
                <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Knoppen onder de tabel -->
        <div class="flex items-center mt-4">
            <!-- Linkerknop: Declaraties indienen -->
            <button
                type="submit"
                class="bg-green-600 text-white rounded-md px-4 py-2 hover:bg-green-700 transition-colors"
            >
                Indienen
            </button>

            <!-- Rechterknop: 'ml-auto' duwt deze helemaal naar rechts -->
            <button
                type="button"
                class="bg-green-600 text-white rounded-md px-4 py-2 hover:bg-green-700 transition-colors ml-auto"
                onclick="window.location.href='/declarations/create';"
            >
                Nieuwe declaratie toevoegen
            </button>
        </div>
    </form>
</div>

<script>
    function toggleAll(source) {
        const checkboxes = document.getElementsByName('record_ids[]');
        for(let i = 0; i < checkboxes.length; i++) {
            if (!checkboxes[i].disabled) {
                checkboxes[i].checked = source.checked;
            }
        }
    }
</script>
