<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? Config::get('app.name'); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
    function navigateTo(path) {
        // Gebruik gewone browser navigatie - de reverse proxy handelt de port mapping af
        window.location.href = path;
    }
    </script>
</head>
<body class="bg-gray-100 min-h-screen">
    <header class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 py-1 sm:px-6 lg:px-8 flex justify-between items-center">
            <h1 class="text-xl font-semibold text-gray-900 flex items-center">
                <a href="/" class="flex items-center">
                    <img src="/Logo%20Green%20&%20Black.svg" alt="<?php echo Config::get('app.name'); ?>" class="h-16 mr-3">
                </a>
            </h1>

            <?php if (Auth::check()): ?>
                <?php $currentUser = Auth::user(); ?>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-600">
                        Welkom, <?php echo View::escape($currentUser ? $currentUser['username'] : 'Gebruiker'); ?>
                    </span>
                    <a href="/declarations" class="text-blue-600 hover:text-blue-800">
                        Declaraties
                    </a>
                    <a href="/travel_records" class="text-blue-600 hover:text-blue-800">
                        Reiskosten
                    </a>
                    <a href="/assets" class="text-blue-600 hover:text-blue-800">
                        Assets
                    </a>
                    <?php if ($currentUser && $currentUser['is_admin']): ?>
                        <a href="/admin" class="text-purple-600 hover:text-purple-800">
                            Admin
                        </a>
                    <?php endif; ?>
                    <a href="/user/profile" class="text-blue-600 hover:text-blue-800">
                        Mijn Profiel
                    </a>
                    <a href="/logout" class="text-red-600 hover:text-red-800">
                        Uitloggen
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </header>

    <main>
        <?php echo $content; ?>
    </main>

    <footer class="bg-white shadow-inner mt-8 py-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <p class="text-center text-gray-500 text-sm">
                &copy; <?php echo date('Y'); ?> <?php echo Config::get('app.name'); ?>
            </p>
        </div>
    </footer>

    <?php if (isset($message) && $message): ?>
        <script>
            Swal.fire({
                title: 'Melding',
                text: '<?php echo addslashes($message); ?>',
                icon: 'info',
                confirmButtonText: 'OK'
            });
        </script>
    <?php endif; ?>
</body>
</html>
