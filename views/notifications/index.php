<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Admin Notificaties</h1>
        <div class="flex space-x-2">
            <?php if ($unreadCount > 0): ?>
                <form method="POST" action="/notifications/markAllAsRead" class="inline">
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                        Alle markeren als gelezen
                    </button>
                </form>
            <?php endif; ?>
            <form method="POST" action="/notifications/cleanup" class="inline">
                <button type="submit" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors" onclick="return confirm('Weet je zeker dat je oude notificaties wilt verwijderen?')">
                    Oude notificaties opruimen
                </button>
            </form>
        </div>
    </div>

    <?php if ($unreadCount > 0): ?>
        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">
                        Je hebt <?php echo $unreadCount; ?> ongelezen notificatie<?php echo $unreadCount !== 1 ? 's' : ''; ?>
                    </h3>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <?php if (empty($notifications)): ?>
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM9 7H4l5-5v5zm6 10V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2h6a2 2 0 002-2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Geen notificaties</h3>
                <p class="mt-1 text-sm text-gray-500">Er zijn nog geen notificaties ontvangen.</p>
            </div>
        <?php else: ?>
            <ul class="divide-y divide-gray-200">
                <?php foreach ($notifications as $notification): ?>
                    <li class="<?php echo $notification['is_read'] ? 'bg-white' : 'bg-blue-50'; ?>">
                        <div class="px-4 py-4 sm:px-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <?php
                                        $iconClass = 'h-8 w-8 text-gray-400';
                                        $iconPath = '';
                                        
                                        switch ($notification['type']) {
                                            case 'asset_assigned':
                                                $iconClass = 'h-8 w-8 text-green-500';
                                                $iconPath = 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z';
                                                break;
                                            case 'declaration_submitted':
                                                $iconClass = 'h-8 w-8 text-blue-500';
                                                $iconPath = 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z';
                                                break;
                                            case 'travel_submitted':
                                                $iconClass = 'h-8 w-8 text-purple-500';
                                                $iconPath = 'M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z M15 11a3 3 0 11-6 0 3 3 0 016 0z';
                                                break;
                                            default:
                                                $iconPath = 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
                                        }
                                        ?>
                                        <svg class="<?php echo $iconClass; ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $iconPath; ?>" />
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <div class="flex items-center">
                                            <p class="text-sm font-medium text-gray-900">
                                                <?php echo View::escape($notification['message']); ?>
                                            </p>
                                            <?php if (!$notification['is_read']): ?>
                                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    Nieuw
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="mt-1 flex items-center text-sm text-gray-500">
                                            <span>Door: <?php echo View::escape($notification['username'] ?? 'Onbekend'); ?></span>
                                            <span class="mx-2">•</span>
                                            <span><?php echo date('d-m-Y H:i', strtotime($notification['created_at'])); ?></span>
                                            <span class="mx-2">•</span>
                                            <span class="capitalize"><?php echo View::escape(str_replace('_', ' ', $notification['type'])); ?></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <?php if (!$notification['is_read']): ?>
                                        <form method="POST" action="/notifications/markAsRead" class="inline">
                                            <input type="hidden" name="id" value="<?php echo $notification['id']; ?>">
                                            <button type="submit" class="text-blue-600 hover:text-blue-800 text-sm">
                                                Markeer als gelezen
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>
    </div>
</div>
