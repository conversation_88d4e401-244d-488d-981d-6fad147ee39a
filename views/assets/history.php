<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Asset geschiedenis</h1>
        <div>
            <a href="/assets/show/<?php echo $asset['id']; ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2">Details</a>
            <button onclick="window.location.href = window.location.origin + '/assets'" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">Terug naar overzicht</button>
        </div>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                <?php echo htmlspecialchars($asset['name'] ?? ''); ?> (<?php echo htmlspecialchars($asset['uid'] ?? ''); ?>)
            </h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Geschiedenis van dit asset
            </p>
        </div>

        <?php if (empty($logs)): ?>
            <div class="px-4 py-5 sm:px-6 text-center text-gray-500">
                Geen geschiedenis gevonden voor dit asset.
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Datum</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actie</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gebruiker</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Einddatum</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Opmerkingen</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($logs as $log): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo date('d-m-Y H:i', strtotime($log['start_date'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php
                                    $actionClass = '';
                                    $action = $log['action'] ?? '';
                                    switch ($action) {
                                        case 'uitgeleend':
                                            $actionClass = 'bg-blue-100 text-blue-800';
                                            break;
                                        case 'ingeleverd':
                                            $actionClass = 'bg-green-100 text-green-800';
                                            break;
                                        case 'onderhoud':
                                            $actionClass = 'bg-yellow-100 text-yellow-800';
                                            break;
                                        case 'afgeschreven':
                                            $actionClass = 'bg-red-100 text-red-800';
                                            break;
                                    }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $actionClass; ?>">
                                        <?php echo htmlspecialchars($log['action'] ?? ''); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo htmlspecialchars($log['username'] ?? '-'); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $log['end_date'] ? date('d-m-Y H:i', strtotime($log['end_date'])) : '-'; ?>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">
                                    <?php echo nl2br(htmlspecialchars($log['notes'] ?? '')); ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>
