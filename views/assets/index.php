<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">Asset overzicht</h1>

    <?php if (isset($_SESSION['message'])): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            <?php echo $_SESSION['message']; ?>
        </div>
        <?php unset($_SESSION['message']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <?php echo $_SESSION['error']; ?>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <!-- Assets Data -->
    <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-4">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Assets</h2>
        <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 text-sm text-left">
            <thead class="bg-gray-100">
                <tr>
                    <?php if ($userRole === 'administrator'): ?>
                        <th class="px-3 py-2 font-medium text-gray-700">UID</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Naam</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Merk</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Model</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Type</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Status</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Huidige houder</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Acties</th>
                    <?php else: ?>
                        <th class="px-3 py-2 font-medium text-gray-700">Naam</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Merk</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Model</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Type</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Huidige/Laatste houder</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Acties</th>
                    <?php endif; ?>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                <?php if (empty($assets)): ?>
                    <tr>
                        <td colspan="<?php echo $userRole === 'administrator' ? '8' : '6'; ?>" class="px-3 py-2 text-center text-sm text-gray-500">Geen assets gevonden</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($assets as $asset): ?>
                        <tr>
                            <?php if ($userRole === 'administrator'): ?>
                                <!-- Admin view: alle kolommen -->
                                <td class="px-3 py-2 text-sm font-medium text-gray-900"><?php echo htmlspecialchars($asset['uid'] ?? ''); ?></td>
                                <td class="px-3 py-2 text-sm text-gray-500"><?php echo htmlspecialchars($asset['name'] ?? ''); ?></td>
                                <td class="px-3 py-2 text-sm text-gray-500"><?php echo htmlspecialchars($asset['brand'] ?? '-'); ?></td>
                                <td class="px-3 py-2 text-sm text-gray-500"><?php echo htmlspecialchars($asset['model'] ?? '-'); ?></td>
                                <td class="px-3 py-2 text-sm text-gray-500"><?php echo htmlspecialchars($asset['type_name'] ?? '-'); ?></td>
                                <td class="px-3 py-2 text-sm text-gray-500">
                                    <?php
                                    $statusClass = '';
                                    switch ($asset['status']) {
                                        case 'beschikbaar':
                                            $statusClass = 'bg-green-100 text-green-800';
                                            break;
                                        case 'uitgeleend':
                                            $statusClass = 'bg-blue-100 text-blue-800';
                                            break;
                                        case 'onderhoud':
                                            $statusClass = 'bg-yellow-100 text-yellow-800';
                                            break;
                                        case 'afgeschreven':
                                            $statusClass = 'bg-red-100 text-red-800';
                                            break;
                                    }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $statusClass; ?>">
                                        <?php echo htmlspecialchars($asset['status'] ?? ''); ?>
                                    </span>
                                </td>
                                <td class="px-3 py-2 text-sm text-gray-500">
                                    <?php echo isset($asset['current_holder_name']) && $asset['current_holder_name'] ? htmlspecialchars($asset['current_holder_name']) : '-'; ?>
                                </td>
                                <td class="px-3 py-2 text-sm font-medium">
                                    <a href="/assets/show/<?php echo $asset['id']; ?>" class="text-indigo-600 hover:text-indigo-900 mr-2">Details</a>
                                    <a href="/assets/history/<?php echo $asset['id']; ?>" class="text-blue-600 hover:text-blue-900 mr-2">Geschiedenis</a>
                                </td>
                            <?php else: ?>
                                <!-- User view: alleen essentiële kolommen -->
                                <td class="px-3 py-2 text-sm text-gray-500"><?php echo htmlspecialchars($asset['name'] ?? ''); ?></td>
                                <td class="px-3 py-2 text-sm text-gray-500"><?php echo htmlspecialchars($asset['brand'] ?? '-'); ?></td>
                                <td class="px-3 py-2 text-sm text-gray-500"><?php echo htmlspecialchars($asset['model'] ?? '-'); ?></td>
                                <td class="px-3 py-2 text-sm text-gray-500"><?php echo htmlspecialchars($asset['type_name'] ?? '-'); ?></td>
                                <td class="px-3 py-2 text-sm text-gray-500">
                                    <?php echo isset($asset['current_holder_name']) && $asset['current_holder_name'] ? htmlspecialchars($asset['current_holder_name']) : 'Beschikbaar'; ?>
                                </td>
                                <td class="px-3 py-2 text-sm font-medium">
                                    <?php if (!$asset['current_holder']): ?>
                                        <form method="POST" action="/assets/assignToMe" class="inline">
                                            <input type="hidden" name="asset_id" value="<?php echo $asset['id']; ?>">
                                            <button type="submit" class="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700 transition-colors">
                                                Toewijzen aan mij
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <span class="text-gray-400 text-xs">Niet beschikbaar</span>
                                    <?php endif; ?>
                                </td>
                            <?php endif; ?>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
        </div>
    </div>
</div>
