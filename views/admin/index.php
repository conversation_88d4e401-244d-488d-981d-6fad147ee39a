<div class="max-w-7xl mx-auto px-4 py-6">
    <div class="bg-white shadow rounded-lg p-6">
        <h1 class="text-2xl font-semibold mb-6">Admin Dashboard</h1>

        <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <!-- Gebruikers kaart -->
            <div class="bg-blue-50 rounded-lg p-6 shadow-sm">
                <h2 class="text-xl font-semibold text-blue-800 mb-4">Gebruikers</h2>

                <div class="mb-4">
                    <p class="text-gray-700">
                        <span class="font-medium">Totaal aantal gebruikers:</span>
                        <?php echo $totalUsers; ?>
                    </p>
                </div>

                <a
                    href="/admin/users"
                    class="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
                >
                    Beheer gebruikers
                </a>
            </div>

            <!-- <PERSON><PERSON><PERSON><PERSON> kaart -->
            <div class="bg-indigo-50 rounded-lg p-6 shadow-sm">
                <h2 class="text-xl font-semibold text-indigo-800 mb-4">Reiskosten</h2>

                <div class="mb-4">
                    <p class="text-gray-700">
                        <span class="font-medium">Gebruikers met ingediende reiskosten:</span>
                        <?php echo count($usersWithPendingKilometers); ?>
                    </p>
                    <p class="text-gray-700">
                        <span class="font-medium">Totaal aantal kilometers:</span>
                        <?php echo number_format($totalKilometers, 1, ',', '.'); ?> km
                    </p>
                </div>

                <a
                    href="/admin/travel_records"
                    class="inline-block bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 transition-colors"
                >
                    Beheer reiskosten
                </a>
            </div>

            <!-- Declaraties kaart -->
            <div class="bg-green-50 rounded-lg p-6 shadow-sm">
                <h2 class="text-xl font-semibold text-green-800 mb-4">Declaraties</h2>

                <div class="mb-4">
                    <p class="text-gray-700">
                        <span class="font-medium">Ingediende declaraties:</span>
                        <?php echo $totalPendingDeclarations; ?>
                    </p>
                    <p class="text-gray-700">
                        <span class="font-medium">Totaalbedrag:</span>
                        € <?php echo number_format($totalPendingAmount, 2, ',', '.'); ?>
                    </p>
                </div>

                <a
                    href="/admin/declarations"
                    class="inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
                >
                    Beheer declaraties
                </a>
            </div>

            <!-- Assets kaart -->
            <div class="bg-yellow-50 rounded-lg p-6 shadow-sm">
                <h2 class="text-xl font-semibold text-yellow-800 mb-4">Assets</h2>

                <div class="mb-4 space-y-2">
                    <p class="text-gray-700">
                        <span class="font-medium">Totaal aantal assets:</span>
                        <?php echo $totalAssets; ?>
                    </p>
                    <p class="text-gray-700">
                        <span class="font-medium">Beschikbaar:</span>
                        <span class="text-green-600 font-semibold"><?php echo $availableAssets; ?></span>
                    </p>
                    <p class="text-gray-700">
                        <span class="font-medium">Uitgegeven:</span>
                        <span class="text-blue-600 font-semibold"><?php echo $assignedAssets; ?></span>
                    </p>
                    <?php if (!empty($recentlyAssignedAssets)): ?>
                        <p class="text-gray-700">
                            <span class="font-medium">Recent uitgegeven (30 dagen):</span>
                            <span class="text-orange-600 font-semibold"><?php echo count($recentlyAssignedAssets); ?></span>
                        </p>
                    <?php endif; ?>
                </div>

                <?php if (!empty($recentlyAssignedAssets)): ?>
                    <div class="mb-4">
                        <h3 class="text-sm font-medium text-yellow-800 mb-2">Recent uitgegeven assets:</h3>
                        <div class="space-y-1">
                            <?php foreach (array_slice($recentlyAssignedAssets, 0, 3) as $asset): ?>
                                <div class="text-xs text-gray-600 bg-yellow-100 rounded px-2 py-1">
                                    <span class="font-medium"><?php echo htmlspecialchars($asset['uid']); ?></span>
                                    → <?php echo htmlspecialchars($asset['current_holder_name'] ?? 'Onbekend'); ?>
                                    <span class="text-gray-500">
                                        (<?php echo date('d-m', strtotime($asset['holder_since'])); ?>)
                                    </span>
                                </div>
                            <?php endforeach; ?>
                            <?php if (count($recentlyAssignedAssets) > 3): ?>
                                <div class="text-xs text-gray-500 italic">
                                    ... en <?php echo count($recentlyAssignedAssets) - 3; ?> meer
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <a
                    href="/admin/assets"
                    class="inline-block bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 transition-colors"
                >
                    Beheer assets
                </a>
            </div>

            <!-- Logs kaart -->
            <div class="bg-purple-50 rounded-lg p-6 shadow-sm">
                <h2 class="text-xl font-semibold text-purple-800 mb-4">Logs</h2>

                <div class="mb-4">
                    <p class="text-gray-700">
                        <span class="font-medium">Bekijk systeemlogboeken</span>
                    </p>
                </div>

                <a
                    href="/logs"
                    class="inline-block bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors"
                >
                    Bekijk logs
                </a>
            </div>
        </div>

        <div class="mt-8">
            <h2 class="text-xl font-semibold mb-4">Gebruikers met ingediende declaraties</h2>

            <div class="bg-white shadow overflow-hidden rounded-md">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Gebruiker
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Aantal declaraties
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Totaalbedrag
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Acties
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($usersWithPendingDeclarations as $user): ?>
                            <?php if ($user['declaration_count'] > 0): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php echo View::escape($user['username']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php echo $user['declaration_count']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        € <?php echo number_format($user['total_amount'], 2, ',', '.'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <a href="/admin/declarations/user/<?php echo $user['user_id']; ?>" class="text-blue-600 hover:text-blue-900">
                                            Bekijk details
                                        </a>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="mt-8">
            <h2 class="text-xl font-semibold mb-4">Gebruikers met ingediende reiskosten</h2>

            <div class="bg-white shadow overflow-hidden rounded-md">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Gebruiker
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Totaal kilometers
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Acties
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($usersWithPendingKilometers as $user): ?>
                            <?php if ($user['total_km'] > 0): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php echo View::escape($user['username']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php echo number_format($user['total_km'], 1, ',', '.'); ?> km
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <a href="/admin/travel_records/user/<?php echo $user['id']; ?>" class="text-blue-600 hover:text-blue-900">
                                            Bekijk details
                                        </a>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
