<div class="max-w-7xl mx-auto px-4 py-6">
    <div class="bg-white shadow rounded p-6">
        <h1 class="text-2xl font-semibold mb-2"><PERSON><PERSON><PERSON><PERSON> van Gebruiker: <?php echo View::escape($user['username']); ?></h1>
        <h2 class="text-lg text-gray-600 mb-6">To<PERSON>al ingediende, niet uitbetaalde kilometers: <strong><?php echo number_format($totalKm, 1, ',', '.'); ?> km</strong></h2>

        <form method="POST" action="/admin/travel_records/update-status/<?php echo $user['id']; ?>">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 text-sm text-left">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="px-3 py-2">
                                <input
                                    type="checkbox"
                                    onclick="toggleAll(this)"
                                    class="rounded focus:ring-blue-500"
                                >
                            </th>
                            <th class="px-3 py-2 font-medium text-gray-700">Datum</th>
                            <th class="px-3 py-2 font-medium text-gray-700">Project</th>
                            <th class="px-3 py-2 font-medium text-gray-700">Van</th>
                            <th class="px-3 py-2 font-medium text-gray-700">Naar</th>
                            <th class="px-3 py-2 font-medium text-gray-700">Enkele reis (km)</th>
                            <th class="px-3 py-2 font-medium text-gray-700">Retour</th>
                            <th class="px-3 py-2 font-medium text-gray-700">Totaal (km)</th>
                            <th class="px-3 py-2 font-medium text-gray-700">Status</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <?php if (count($records) > 0): ?>
                            <?php foreach ($records as $record): ?>
                                <tr>
                                    <td class="px-3 py-2">
                                        <input
                                            type="checkbox"
                                            name="record_ids[]"
                                            value="<?php echo $record['id']; ?>"
                                            class="rounded focus:ring-blue-500"
                                        >
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php echo date('d-m-Y', strtotime($record['date'])); ?>
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php echo View::escape($record['project_number']); ?>
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php echo View::escape($record['address_a']); ?>
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php echo View::escape($record['address_b']); ?>
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php echo number_format($record['single_trip_km'], 1, ',', '.'); ?>
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php echo isset($record['return_trip']) && $record['return_trip'] ? 'Ja' : 'Nee'; ?>
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php echo number_format($record['total_km'], 1, ',', '.'); ?>
                                    </td>
                                    <td class="px-3 py-2">
                                        <select
                                            name="status[<?php echo $record['id']; ?>]"
                                            class="border border-gray-300 rounded py-1 px-2 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                        >
                                            <option value="paid">Betalen</option>
                                            <option value="rejected">Afkeuren</option>
                                        </select>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="9" class="px-3 py-2 text-center text-gray-500">
                                    Geen reiskosten gevonden.
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot class="bg-gray-50">
                        <tr>
                            <td colspan="7" class="px-3 py-2 text-right font-medium">Totaal kilometers:</td>
                            <td class="px-3 py-2 font-medium"><?php echo number_format($totalKm, 1, ',', '.'); ?></td>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class="flex items-center mt-4">
                <button
                    type="submit"
                    class="bg-blue-600 text-white rounded-md px-4 py-2 hover:bg-blue-700 transition-colors"
                    <?php if (count($records) === 0) echo 'disabled'; ?>
                >
                    Update Status
                </button>

                <a
                    href="/admin/travel_records"
                    class="bg-gray-200 text-gray-700 rounded-md px-4 py-2 hover:bg-gray-300 transition-colors ml-2"
                >
                    Terug naar overzicht
                </a>
            </div>
        </form>
    </div>
</div>

<script>
    function toggleAll(source) {
        const checkboxes = document.getElementsByName('record_ids[]');
        for(let i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = source.checked;
        }
    }
</script>
