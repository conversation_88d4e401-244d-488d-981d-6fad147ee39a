<div class="max-w-7xl mx-auto px-4 py-6">
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-semibold">G<PERSON><PERSON>ike<PERSON> beheren</h1>

            <a
                href="users/create"
                class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            >
                Nieuwe gebruiker
            </a>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            ID
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Gebruikersnaam
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Standaardadres
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Admin
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Acties
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($users as $user): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php echo $user['id']; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php echo View::escape($user['username']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php echo View::escape($user['default_address'] ?? '-'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php echo $user['is_admin'] ? 'Ja' : 'Nee'; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <div class="flex space-x-2">
                                    <a href="users/edit/<?php echo $user['id']; ?>" class="text-blue-600 hover:text-blue-900">
                                        Bewerken
                                    </a>

                                    <?php if ($user['id'] != Auth::id()): ?>
                                        <a href="users/delete/<?php echo $user['id']; ?>" class="text-red-600 hover:text-red-900" onclick="return confirm('Weet je zeker dat je deze gebruiker wilt verwijderen?');">
                                            Verwijderen
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="mt-6">
            <a
                href="/admin"
                class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition-colors"
            >
                Terug naar Admin Dashboard
            </a>
        </div>
    </div>
</div>
