<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Asset bewerken</h1>
        <a href="/admin/assets" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">Terug naar overzicht</a>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <?php echo $_SESSION['error']; ?>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
        <form method="POST" action="/admin/assets/update/<?php echo $asset['id']; ?>">
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="uid">
                    UID <span class="text-red-500">*</span>
                </label>
                <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="uid" name="uid" type="text" value="<?php echo htmlspecialchars($asset['uid']); ?>" required>
                <p class="text-gray-600 text-xs italic mt-1">Bijvoorbeeld: LAPTOP-001, MONITOR-002, etc.</p>
            </div>

            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="name">
                    Naam <span class="text-red-500">*</span>
                </label>
                <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="name" name="name" type="text" value="<?php echo htmlspecialchars($asset['name']); ?>" required>
            </div>

            <div class="flex flex-wrap -mx-3 mb-4">
                <div class="w-full md:w-1/3 px-3 mb-6 md:mb-0">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="brand">
                        Merk
                    </label>
                    <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="brand" name="brand" type="text" value="<?php echo htmlspecialchars($asset['brand'] ?? ''); ?>">
                </div>
                <div class="w-full md:w-1/3 px-3 mb-6 md:mb-0">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="model">
                        Model
                    </label>
                    <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="model" name="model" type="text" value="<?php echo htmlspecialchars($asset['model'] ?? ''); ?>">
                </div>
                <div class="w-full md:w-1/3 px-3 mb-6 md:mb-0">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="type_id">
                        Type
                    </label>
                    <select class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="type_id" name="type_id">
                        <option value="">Selecteer een type</option>
                        <?php foreach ($types as $type): ?>
                            <option value="<?php echo $type['id']; ?>" <?php echo ($asset['type_id'] == $type['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($type['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="text-gray-600 text-xs italic mt-1">
                        <a href="/admin/asset_types" class="text-blue-500 hover:underline">Beheer asset types</a>
                    </p>
                </div>
            </div>

            <div class="flex flex-wrap -mx-3 mb-4">
                <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="serial_number">
                        Serienummer
                    </label>
                    <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="serial_number" name="serial_number" type="text" value="<?php echo htmlspecialchars($asset['serial_number'] ?? ''); ?>">
                </div>
                <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="purchase_date">
                        Aankoopdatum
                    </label>
                    <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="purchase_date" name="purchase_date" type="date" value="<?php echo $asset['purchase_date'] ?? ''; ?>">
                </div>
            </div>

            <div class="flex flex-wrap -mx-3 mb-4">
                <div class="w-full md:w-1/3 px-3 mb-6 md:mb-0">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="purchase_value">
                        Aankoopwaarde (€)
                    </label>
                    <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="purchase_value" name="purchase_value" type="number" step="0.01" min="0" value="<?php echo $asset['purchase_value'] ?? ''; ?>">
                </div>
                <div class="w-full md:w-1/3 px-3 mb-6 md:mb-0">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="quantity">
                        Aantal
                    </label>
                    <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="quantity" name="quantity" type="number" min="1" value="<?php echo $asset['quantity'] ?? 1; ?>">
                </div>
                <div class="w-full md:w-1/3 px-3 mb-6 md:mb-0">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="status">
                        Status
                    </label>
                    <select class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="status" name="status">
                        <option value="beschikbaar" <?php echo ($asset['status'] === 'beschikbaar') ? 'selected' : ''; ?>>Beschikbaar</option>
                        <option value="uitgeleend" <?php echo ($asset['status'] === 'uitgeleend') ? 'selected' : ''; ?>>Uitgeleend</option>
                        <option value="onderhoud" <?php echo ($asset['status'] === 'onderhoud') ? 'selected' : ''; ?>>Onderhoud</option>
                        <option value="afgeschreven" <?php echo ($asset['status'] === 'afgeschreven') ? 'selected' : ''; ?>>Afgeschreven</option>
                    </select>
                </div>
            </div>

            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="product_link">
                    Product link
                </label>
                <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="product_link" name="product_link" type="url" value="<?php echo htmlspecialchars($asset['product_link'] ?? ''); ?>">
            </div>

            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="comments">
                    Opmerkingen
                </label>
                <textarea class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="comments" name="comments" rows="3"><?php echo htmlspecialchars($asset['comments'] ?? ''); ?></textarea>
            </div>

            <div class="flex flex-wrap -mx-3 mb-4">
                <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="visibility">
                        Zichtbaarheid
                    </label>
                    <select class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="visibility" name="visibility">
                        <option value="public" <?php echo ($asset['visibility'] === 'public') ? 'selected' : ''; ?>>Publiek</option>
                        <option value="private" <?php echo ($asset['visibility'] === 'private') ? 'selected' : ''; ?>>Privé</option>
                    </select>
                </div>
                <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="user_group">
                        Gebruikersgroep
                    </label>
                    <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="user_group" name="user_group" type="text" value="<?php echo htmlspecialchars($asset['user_group'] ?? ''); ?>">
                </div>
            </div>

            <div class="flex items-center justify-between">
                <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline" type="submit">
                    Asset bijwerken
                </button>
            </div>
        </form>
    </div>
</div>
