<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Asset toewijzen/innemen</h1>
        <div>
            <a href="/admin/asset_types" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded mr-2">Asset types</a>
            <a href="/admin/assets" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">Terug naar overzicht</a>
        </div>
    </div>

    <?php if (isset($_SESSION['message'])): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            <?php echo $_SESSION['message']; ?>
        </div>
        <?php unset($_SESSION['message']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <?php echo $_SESSION['error']; ?>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-8">
        <h2 class="text-xl font-bold mb-4">Asset zoeken op UID</h2>

        <div class="flex items-center mb-4">
            <input id="uid" type="text" placeholder="Voer UID in" class="shadow appearance-none border rounded py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mr-2">
            <button id="searchAsset" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Zoeken</button>
        </div>

        <div id="assetDetails" class="hidden">
            <div id="assetInfo" class="mb-4 p-4 border rounded"></div>

            <div id="assetActions" class="mb-4">
                <div id="assignForm" class="hidden">
                    <h3 class="text-lg font-bold mb-2">Asset toewijzen</h3>
                    <form method="POST" action="/admin/assets/assign-to-user">
                        <input type="hidden" id="assign_asset_id" name="asset_id">

                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="user_id">
                                Gebruiker
                            </label>
                            <select class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="user_id" name="user_id" required>
                                <option value="">Selecteer een gebruiker</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>"><?php echo htmlspecialchars($user['username']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="comments">
                                Opmerkingen
                            </label>
                            <textarea class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="comments" name="comments" rows="2"></textarea>
                        </div>

                        <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">Toewijzen</button>
                    </form>
                </div>

                <div id="unassignForm" class="hidden">
                    <h3 class="text-lg font-bold mb-2">Asset innemen</h3>
                    <form method="POST" action="/admin/assets/unassign-from-user">
                        <input type="hidden" id="unassign_asset_id" name="asset_id">

                        <div class="mb-4">
                            <p class="text-gray-700">
                                Dit asset is momenteel toegewezen aan <span id="current_holder_name" class="font-bold"></span>.
                                Wil je het innemen?
                            </p>
                        </div>

                        <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Innemen</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
        <h2 class="text-xl font-bold mb-4">Alle assets</h2>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Naam</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Huidige houder</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acties</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (empty($assets)): ?>
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">Geen assets gevonden</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($assets as $asset): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?php echo htmlspecialchars($asset['uid']); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo htmlspecialchars($asset['name']); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php
                                    $statusClass = '';
                                    switch ($asset['status']) {
                                        case 'beschikbaar':
                                            $statusClass = 'bg-green-100 text-green-800';
                                            break;
                                        case 'uitgeleend':
                                            $statusClass = 'bg-blue-100 text-blue-800';
                                            break;
                                        case 'onderhoud':
                                            $statusClass = 'bg-yellow-100 text-yellow-800';
                                            break;
                                        case 'afgeschreven':
                                            $statusClass = 'bg-red-100 text-red-800';
                                            break;
                                    }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $statusClass; ?>">
                                        <?php echo htmlspecialchars($asset['status']); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $asset['current_holder_name'] ? htmlspecialchars($asset['current_holder_name']) : '-'; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <?php if ($asset['status'] === 'beschikbaar'): ?>
                                        <button
                                            onclick="showAssignForm('<?php echo $asset['id']; ?>', '<?php echo htmlspecialchars($asset['uid']); ?>', '<?php echo htmlspecialchars($asset['name']); ?>')"
                                            class="text-green-600 hover:text-green-900"
                                        >
                                            Toewijzen
                                        </button>
                                    <?php elseif ($asset['status'] === 'uitgeleend'): ?>
                                        <button
                                            onclick="showUnassignForm('<?php echo $asset['id']; ?>', '<?php echo htmlspecialchars($asset['uid']); ?>', '<?php echo htmlspecialchars($asset['name']); ?>', '<?php echo htmlspecialchars($asset['current_holder_name']); ?>')"
                                            class="text-red-600 hover:text-red-900"
                                        >
                                            Innemen
                                        </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    function showAssignForm(assetId, uid, name) {
        document.getElementById('assetDetails').classList.remove('hidden');
        document.getElementById('assetInfo').innerHTML = `<p><strong>UID:</strong> ${uid}</p><p><strong>Naam:</strong> ${name}</p><p><strong>Status:</strong> <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">beschikbaar</span></p>`;
        document.getElementById('assignForm').classList.remove('hidden');
        document.getElementById('unassignForm').classList.add('hidden');
        document.getElementById('assign_asset_id').value = assetId;

        // Scroll naar het formulier
        document.getElementById('assetDetails').scrollIntoView({ behavior: 'smooth' });
    }

    function showUnassignForm(assetId, uid, name, holderName) {
        document.getElementById('assetDetails').classList.remove('hidden');
        document.getElementById('assetInfo').innerHTML = `<p><strong>UID:</strong> ${uid}</p><p><strong>Naam:</strong> ${name}</p><p><strong>Status:</strong> <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">uitgeleend</span></p>`;
        document.getElementById('assignForm').classList.add('hidden');
        document.getElementById('unassignForm').classList.remove('hidden');
        document.getElementById('unassign_asset_id').value = assetId;
        document.getElementById('current_holder_name').textContent = holderName;

        // Scroll naar het formulier
        document.getElementById('assetDetails').scrollIntoView({ behavior: 'smooth' });
    }

    document.getElementById('searchAsset').addEventListener('click', function() {
        const uid = document.getElementById('uid').value;
        if (!uid) {
            alert('Voer een UID in');
            return;
        }

        fetch(`/api/assets/find-by-uid?uid=${encodeURIComponent(uid)}`)
            .then(response => response.json())
            .then(asset => {
                if (!asset) {
                    alert('Geen asset gevonden met deze UID');
                    return;
                }

                document.getElementById('assetDetails').classList.remove('hidden');

                let statusClass = '';
                switch (asset.status) {
                    case 'beschikbaar':
                        statusClass = 'bg-green-100 text-green-800';
                        break;
                    case 'uitgeleend':
                        statusClass = 'bg-blue-100 text-blue-800';
                        break;
                    case 'onderhoud':
                        statusClass = 'bg-yellow-100 text-yellow-800';
                        break;
                    case 'afgeschreven':
                        statusClass = 'bg-red-100 text-red-800';
                        break;
                }

                document.getElementById('assetInfo').innerHTML = `
                    <p><strong>UID:</strong> ${asset.uid}</p>
                    <p><strong>Naam:</strong> ${asset.name}</p>
                    <p><strong>Status:</strong> <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">${asset.status}</span></p>
                    <p><strong>Huidige houder:</strong> ${asset.current_holder_name || '-'}</p>
                `;

                if (asset.status === 'beschikbaar') {
                    document.getElementById('assignForm').classList.remove('hidden');
                    document.getElementById('unassignForm').classList.add('hidden');
                    document.getElementById('assign_asset_id').value = asset.id;
                } else if (asset.status === 'uitgeleend') {
                    document.getElementById('assignForm').classList.add('hidden');
                    document.getElementById('unassignForm').classList.remove('hidden');
                    document.getElementById('unassign_asset_id').value = asset.id;
                    document.getElementById('current_holder_name').textContent = asset.current_holder_name;
                } else {
                    document.getElementById('assignForm').classList.add('hidden');
                    document.getElementById('unassignForm').classList.add('hidden');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Er is een fout opgetreden bij het zoeken naar het asset');
            });
    });
</script>
