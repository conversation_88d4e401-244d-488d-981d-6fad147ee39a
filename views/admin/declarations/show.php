<div class="max-w-7xl mx-auto px-4 py-6">
    <div class="bg-white shadow rounded p-6">
        <h1 class="text-2xl font-semibold mb-2">Declarati<PERSON> <PERSON>: <?php echo View::escape($user['username']); ?></h1>
        <h2 class="text-lg text-gray-600 mb-6">Totaal ingediende, niet uitbetaalde declaraties: <strong>€ <?php echo number_format($totalAmount, 2, ',', '.'); ?></strong></h2>

        <form method="POST" action="/admin/declarations/update-status/<?php echo $user['id']; ?>">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 text-sm text-left">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="px-3 py-2">
                                <input
                                    type="checkbox"
                                    onclick="toggleAll(this)"
                                    class="rounded focus:ring-blue-500"
                                >
                            </th>
                            <th class="px-3 py-2 font-medium text-gray-700">Datum</th>
                            <th class="px-3 py-2 font-medium text-gray-700">Titel</th>
                            <th class="px-3 py-2 font-medium text-gray-700">Projectnummer</th>
                            <th class="px-3 py-2 font-medium text-gray-700">Totaal Bedrag (€)</th>
                            <th class="px-3 py-2 font-medium text-gray-700">Bedrag excl. BTW (€)</th>
                            <th class="px-3 py-2 font-medium text-gray-700">BTW %</th>
                            <th class="px-3 py-2 font-medium text-gray-700">Product/Dienst</th>
                            <th class="px-3 py-2 font-medium text-gray-700">Bon/Factuur</th>
                            <th class="px-3 py-2 font-medium text-gray-700">Status</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <?php if (count($declarations) > 0): ?>
                            <?php foreach ($declarations as $declaration): ?>
                                <tr>
                                    <td class="px-3 py-2">
                                        <input
                                            type="checkbox"
                                            name="record_ids[]"
                                            value="<?php echo $declaration['id']; ?>"
                                            class="rounded focus:ring-blue-500"
                                        >
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php echo date('d-m-Y', strtotime($declaration['aanmaakdatum'])); ?>
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php echo View::escape($declaration['titel']); ?>
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php echo View::escape($declaration['project_number'] ?? 'Algemeen'); ?>
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php echo number_format((float)$declaration['totaal_bedrag'], 2, ',', '.'); ?>
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php echo number_format((float)$declaration['bedrag_excl_btw'], 2, ',', '.'); ?>
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php echo number_format((float)($declaration['btw_percentage'] ?? 21), 0); ?>%
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php echo View::escape($declaration['product_dienst']); ?>
                                    </td>
                                    <td class="px-3 py-2">
                                        <?php if (!empty($declaration['bestand_pad'])): ?>
                                            <?php
                                                // Extract filename from path
                                                $filename = basename($declaration['bestand_pad']);
                                            ?>
                                            <a
                                                href="/uploads/declaraties/<?php echo View::escape($filename); ?>"
                                                target="_blank"
                                                class="text-blue-600 hover:text-blue-800 transition-colors"
                                            >
                                                Bekijk bon/factuur
                                            </a>
                                        <?php else: ?>
                                            <span class="text-red-600">✗</span> Geen bestand
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-3 py-2">
                                        <select
                                            name="status[<?php echo $declaration['id']; ?>]"
                                            class="border border-gray-300 rounded py-1 px-2 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                        >
                                            <option value="paid">Betalen</option>
                                            <option value="rejected">Afkeuren</option>
                                        </select>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="9" class="px-3 py-2 text-center text-gray-500">
                                    Geen declaraties gevonden.
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <div class="flex items-center mt-4">
                <button
                    type="submit"
                    class="bg-green-600 text-white rounded-md px-4 py-2 hover:bg-green-700 transition-colors"
                    <?php if (count($declarations) === 0) echo 'disabled'; ?>
                >
                    Update Status
                </button>

                <a
                    href="/admin/declarations"
                    class="bg-gray-200 text-gray-700 rounded-md px-4 py-2 hover:bg-gray-300 transition-colors ml-2"
                >
                    Terug naar overzicht
                </a>
            </div>
        </form>
    </div>
</div>

<script>
    function toggleAll(source) {
        const checkboxes = document.getElementsByName('record_ids[]');
        for(let i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = source.checked;
        }
    }
</script>
