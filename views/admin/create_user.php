<div class="max-w-3xl mx-auto p-4 mt-6">
  <div class="bg-white shadow rounded p-6">
    <h1 class="text-2xl font-semibold mb-6">Nieuwe Gebruiker Toevoegen</h1>

    <form method="POST" action="store" class="space-y-4">
      <!-- Gebruikersnaam -->
      <div>
        <label for="username" class="block mb-1 text-sm font-medium text-gray-700">Gebruikersnaam</label>
        <input
          type="text"
          name="username"
          id="username"
          required
          class="block w-full border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring focus:ring-blue-200"
        >
      </div>

      <!-- Wachtwoord -->
      <div>
        <label for="password" class="block mb-1 text-sm font-medium text-gray-700">Wachtwoord</label>
        <input
          type="password"
          name="password"
          id="password"
          required
          class="block w-full border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring focus:ring-blue-200"
        >
      </div>

      <!-- Standaardadres -->
      <div>
        <label for="default_address" class="block mb-1 text-sm font-medium text-gray-700">Standaardadres</label>
        <input
          type="text"
          name="default_address"
          id="default_address"
          class="block w-full border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring focus:ring-blue-200"
        >
        <p class="mt-1 text-xs text-gray-500">Dit adres wordt automatisch ingevuld bij het toevoegen van reiskosten.</p>
      </div>

      <!-- Admin -->
      <div class="flex items-center space-x-2">
        <input
          type="checkbox"
          name="is_admin"
          id="is_admin"
          value="1"
          class="h-4 w-4 border-gray-300 rounded focus:ring focus:ring-blue-500"
        >
        <label for="is_admin" class="text-sm text-gray-700">Administrator</label>
      </div>

      <!-- Knoppen -->
      <div class="flex space-x-2">
        <button
          type="submit"
          class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 focus:outline-none focus:ring focus:ring-blue-200"
        >
          Toevoegen
        </button>
        <a
          href="/admin/users"
          class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 focus:outline-none focus:ring focus:ring-gray-200"
        >
          Annuleren
        </a>
      </div>
    </form>
  </div>
</div>
