<div class="max-w-3xl mx-auto p-4 mt-6">
  <div class="bg-white shadow rounded p-6">
    <h1 class="text-2xl font-semibold mb-6">Nieuwe Reiskostenregel Toevoegen</h1>

    <form method="POST" action="/travel_records/store" class="space-y-4">
      <!-- Datum -->
      <div>
        <label for="date" class="block mb-1 text-sm font-medium text-gray-700">Datum</label>
        <input
          type="date"
          name="date"
          id="date"
          required
          value="<?php echo date('Y-m-d'); ?>"
          class="block w-full border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring focus:ring-blue-200"
        >
      </div>

      <!-- Woon-werkverkeer checkbox -->
      <div class="flex items-center space-x-2 mb-4">
        <input
          type="checkbox"
          name="is_commute"
          id="is_commute"
          value="1"
          class="h-4 w-4 border-gray-300 rounded focus:ring focus:ring-blue-500"
          onchange="toggleCommuteMode()"
        >
        <label for="is_commute" class="text-sm text-gray-700">Woon-werkverkeer</label>
      </div>

      <!-- Projectnummer -->
      <div>
        <label for="project_number" class="block mb-1 text-sm font-medium text-gray-700">Projectnummer</label>
        <input
          type="text"
          name="project_number"
          id="project_number"
          required
          class="block w-full border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring focus:ring-blue-200"
        >
      </div>

      <!-- Adres A -->
      <div>
        <label for="address_a" class="block mb-1 text-sm font-medium text-gray-700">Van (adres)</label>
        <input
          type="text"
          name="address_a"
          id="address_a"
          required
          value="<?php echo View::escape($defaultAddress ?? ''); ?>"
          class="address-input block w-full border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring focus:ring-blue-200"
        >
      </div>

      <!-- Adres B -->
      <div>
        <label for="address_b" class="block mb-1 text-sm font-medium text-gray-700">Naar (adres)</label>
        <input
          type="text"
          name="address_b"
          id="address_b"
          required
          class="address-input block w-full border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring focus:ring-blue-200"
        >
      </div>

      <!-- Extra adressen -->
      <div>
        <label for="extra_addresses" class="block mb-1 text-sm font-medium text-gray-700">Extra adressen (optioneel)</label>
        <div id="extra-addresses-container">
          <!-- Extra adresvelden worden hier dynamisch toegevoegd -->
        </div>
        <button
          type="button"
          id="add-address-btn"
          class="mt-2 px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 focus:outline-none focus:ring focus:ring-gray-200"
        >
          Adres toevoegen
        </button>
        <input type="hidden" name="extra_addresses" id="extra_addresses">
      </div>

      <!-- Enkele reis (km) -->
      <div>
        <label for="single_trip_km" class="block mb-1 text-sm font-medium text-gray-700">Enkele reis (km)</label>
        <div class="flex items-center space-x-2">
          <input
            type="number"
            name="single_trip_km"
            id="single_trip_km"
            step="0.1"
            required
            readonly
            class="block w-full border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring focus:ring-blue-200"
          >
          <button
            type="button"
            id="calculate-distance-btn"
            class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 focus:outline-none focus:ring focus:ring-blue-200"
          >
            Bereken
          </button>
        </div>
      </div>

      <!-- Retour -->
      <div id="return-trip-container" class="flex items-center space-x-2">
        <input
          type="checkbox"
          name="return_trip"
          id="return_trip"
          value="1"
          class="h-4 w-4 border-gray-300 rounded focus:ring focus:ring-blue-500"
          onchange="updateTotalKm()"
        >
        <label for="return_trip" class="text-sm text-gray-700">Retour</label>
      </div>

      <!-- Totaal (km) -->
      <div>
        <label for="total_km" class="block mb-1 text-sm font-medium text-gray-700">Totaal (km)</label>
        <input
          type="number"
          name="total_km"
          id="total_km"
          step="0.1"
          required
          readonly
          class="block w-full border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring focus:ring-blue-200"
        >
      </div>

      <!-- Knoppen -->
      <div class="flex space-x-2">
        <button
          type="submit"
          class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 focus:outline-none focus:ring focus:ring-blue-200"
        >
          Toevoegen
        </button>
        <a
          href="/travel_records"
          class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 focus:outline-none focus:ring focus:ring-gray-200"
        >
          Annuleren
        </a>
      </div>
    </form>
  </div>
</div>

<script>
// Adres regex voor validatie
const addressRegex = /^[a-zA-Z0-9\s,.-]+$/;
let extraAddressCount = 0;

// Toon/verberg retouroptie
function checkReturnTripVisibility() {
  const addressInputs = document.querySelectorAll('.address-input');
  const returnTripContainer = document.getElementById('return-trip-container');
  const returnTripCheckbox = document.getElementById('return_trip');

  if (addressInputs.length === 2 && document.getElementById('extra-addresses-container').children.length === 0) {
    returnTripContainer.style.display = 'flex';
  } else {
    returnTripContainer.style.display = 'none';
    returnTripCheckbox.checked = false;
  }

  updateTotalKm();
}

// Validatie en herberekening
function validateAddress(input) {
  const value = input.value.trim();
  if (!value || !addressRegex.test(value)) {
    input.classList.add('border-red-500');
    return false;
  } else {
    input.classList.remove('border-red-500');
    return true;
  }
}

// Bereken afstand tussen adressen
async function calculateDistance() {
  // Verzamel alle adressen
  const addressInputs = document.querySelectorAll('.address-input');
  const addresses = Array.from(addressInputs).map(input => input.value.trim()).filter(address => address);

  // Controleer of er minstens twee adressen zijn
  if (addresses.length < 2) {
    alert('Vul minstens twee adressen in.');
    return;
  }

  // Valideer alle adressen
  let allValid = true;
  addressInputs.forEach(input => {
    if (!validateAddress(input)) {
      allValid = false;
    }
  });

  if (!allValid) {
    alert('Controleer de adressen op fouten.');
    return;
  }

  try {
    console.log('Berekenen afstand voor adressen:', addresses);

    // Bereken de afstand
    const response = await fetch('/api/calculate-total-distance', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ addresses })
    });

    console.log('Response status:', response.status);

    // Controleer of de response OK is
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API fout:', response.status, errorText);
      throw new Error(`API fout (${response.status}): ${errorText}`);
    }

    const data = await response.json();
    console.log('Response data:', data);

    if (data.error) {
      console.error('API foutmelding:', data.error);
      alert('Fout bij het berekenen van de afstand: ' + data.error);
      return;
    }

    // Vul de afstand in
    document.getElementById('single_trip_km').value = data.distance.toFixed(1);
    updateTotalKm();
  } catch (error) {
    console.error('Fout bij het berekenen van de afstand:', error);
    alert('Fout bij het berekenen van de afstand: ' + error.message);
  }
}

// Update totaal kilometers
function updateTotalKm() {
  const singleTripKm = parseFloat(document.getElementById('single_trip_km').value) || 0;
  const returnTrip = document.getElementById('return_trip').checked;
  const addressInputs = document.querySelectorAll('.address-input');
  const extraAddressContainer = document.getElementById('extra-addresses-container');

  let totalKm = singleTripKm;

  if (returnTrip && addressInputs.length === 2 && extraAddressContainer.children.length === 0) {
    totalKm = singleTripKm * 2;
  }

  document.getElementById('total_km').value = totalKm.toFixed(1);
}

// Voeg een extra adres toe
function addExtraAddress() {
  const container = document.getElementById('extra-addresses-container');
  extraAddressCount++;

  const addressDiv = document.createElement('div');
  addressDiv.className = 'flex items-center space-x-2 mt-2';
  addressDiv.innerHTML = `
    <input
      type="text"
      name="extra_address_${extraAddressCount}"
      placeholder="Extra adres ${extraAddressCount}"
      class="address-input block w-full border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring focus:ring-blue-200"
    >
    <button
      type="button"
      class="remove-address-btn px-2 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 focus:outline-none focus:ring focus:ring-red-200"
    >
      X
    </button>
  `;

  container.appendChild(addressDiv);

  // Voeg event listeners toe
  const input = addressDiv.querySelector('input');
  const removeButton = addressDiv.querySelector('.remove-address-btn');

  input.addEventListener('blur', () => {
    validateAddress(input);
    calculateDistance();
  });

  removeButton.addEventListener('click', () => {
    container.removeChild(addressDiv);
    updateExtraAddressesField();
    checkReturnTripVisibility();
    calculateDistance();
  });

  updateExtraAddressesField();
  checkReturnTripVisibility();
}

// Update het verborgen veld met extra adressen
function updateExtraAddressesField() {
  const extraAddressInputs = Array.from(document.querySelectorAll('#extra-addresses-container .address-input'));
  const extraAddresses = extraAddressInputs.map(input => input.value.trim()).filter(address => address);
  document.getElementById('extra_addresses').value = JSON.stringify(extraAddresses);
}

// Schakel woon-werkverkeer modus
function toggleCommuteMode() {
  const isCommute = document.getElementById('is_commute').checked;
  const projectNumberField = document.getElementById('project_number');
  const addressBField = document.getElementById('address_b');

  if (isCommute) {
    // Bewaar de huidige waarde als data-attribute als het nog niet is opgeslagen
    if (!projectNumberField.hasAttribute('data-original-value')) {
      projectNumberField.setAttribute('data-original-value', projectNumberField.value);
    }
    if (!addressBField.hasAttribute('data-original-value')) {
      addressBField.setAttribute('data-original-value', addressBField.value);
    }

    // Vul "Woon-werkverkeer" in
    projectNumberField.value = "Woon-werkverkeer";
    projectNumberField.readOnly = true;
    projectNumberField.classList.add('bg-gray-100');

    // Vul automatisch het "naar" adres in
    addressBField.value = 'Rondven 24, 6026PX, Maarheeze';

    // Bereken automatisch de afstand als beide adressen zijn ingevuld
    if (document.getElementById('address_a').value && addressBField.value) {
      calculateDistance();
    }
  } else {
    // Herstel de originele waarde als die is opgeslagen
    if (projectNumberField.hasAttribute('data-original-value')) {
      projectNumberField.value = projectNumberField.getAttribute('data-original-value');
    } else {
      projectNumberField.value = "";
    }

    // Herstel het originele "naar" adres
    if (addressBField.hasAttribute('data-original-value')) {
      addressBField.value = addressBField.getAttribute('data-original-value');
    } else {
      addressBField.value = "";
    }

    projectNumberField.readOnly = false;
    projectNumberField.classList.remove('bg-gray-100');

    // Reset de afstand
    document.getElementById('total_km').value = '';
  }
}

// Event listeners
document.addEventListener('DOMContentLoaded', () => {
  // Adres validatie en automatische berekening
  document.getElementById('address_a').addEventListener('blur', function() {
    validateAddress(this);
    if (document.getElementById('address_a').value && document.getElementById('address_b').value) {
      calculateDistance();
    }
  });

  document.getElementById('address_b').addEventListener('blur', function() {
    validateAddress(this);
    if (document.getElementById('address_a').value && document.getElementById('address_b').value) {
      calculateDistance();
    }
  });

  // Bereken afstand (handmatig)
  document.getElementById('calculate-distance-btn').addEventListener('click', calculateDistance);

  // Voeg extra adres toe
  document.getElementById('add-address-btn').addEventListener('click', addExtraAddress);

  // Retour checkbox
  document.getElementById('return_trip').addEventListener('change', updateTotalKm);

  // Woon-werkverkeer checkbox
  document.getElementById('is_commute').addEventListener('change', toggleCommuteMode);

  // Initialisatie
  checkReturnTripVisibility();
  toggleCommuteMode(); // Initialiseer de woon-werkverkeer modus
});
</script>