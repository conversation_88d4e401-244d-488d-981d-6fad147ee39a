<div class="max-w-7xl mx-auto px-4 py-6">
    <h1 class="text-2xl font-semibold mb-6"><PERSON><PERSON></h1>

    <!-- Filterformulier -->
    <form method="GET" action="/travel_records" id="filter-form" class="bg-white shadow-sm rounded-md p-4 mb-6">
        <!-- <PERSON><PERSON> van 4 kolommen op md+ -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <!-- Datum vanaf -->
            <div class="flex flex-col">
                <label for="date_from" class="mb-1 text-sm font-medium text-gray-700">
                    Datum vanaf:
                </label>
                <input
                    type="date"
                    id="date_from"
                    name="date_from"
                    value="<?php echo View::escape($dateFrom); ?>"
                    class="border border-gray-300 rounded py-1 px-2 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
            </div>

            <!-- Datum tot -->
            <div class="flex flex-col">
                <label for="date_to" class="mb-1 text-sm font-medium text-gray-700">
                    Datum tot:
                </label>
                <input
                    type="date"
                    id="date_to"
                    name="date_to"
                    value="<?php echo View::escape($dateTo); ?>"
                    class="border border-gray-300 rounded py-1 px-2 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
            </div>

            <!-- Projectnummer -->
            <div class="flex flex-col">
                <label for="project_number" class="mb-1 text-sm font-medium text-gray-700">
                    Projectnummer:
                </label>
                <input
                    type="text"
                    id="project_number"
                    name="project_number"
                    value="<?php echo View::escape($projectNumber); ?>"
                    class="border border-gray-300 rounded py-1 px-2 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
            </div>

            <!-- Checkboxes Status -->
            <div class="flex flex-col">
                <label class="mb-1 text-sm font-medium text-gray-700">Status:</label>

                <label class="flex items-center space-x-2 text-sm text-gray-700 mb-1">
                    <input
                        type="checkbox"
                        name="show_open"
                        <?php if ($showOpen) echo 'checked'; ?>
                        class="rounded focus:ring-blue-500"
                    >
                    <span>Openstaand</span>
                </label>

                <label class="flex items-center space-x-2 text-sm text-gray-700 mb-1">
                    <input
                        type="checkbox"
                        name="show_submitted"
                        <?php if ($showSubmitted) echo 'checked'; ?>
                        class="rounded focus:ring-blue-500"
                    >
                    <span>Ingediend</span>
                </label>

                <label class="flex items-center space-x-2 text-sm text-gray-700 mb-1">
                    <input
                        type="checkbox"
                        name="show_paid"
                        <?php if ($showPaid) echo 'checked'; ?>
                        class="rounded focus:ring-blue-500"
                    >
                    <span>Uitbetaald</span>
                </label>

                <label class="flex items-center space-x-2 text-sm text-gray-700 mb-1">
                    <input
                        type="checkbox"
                        name="show_rejected"
                        <?php if ($showRejected) echo 'checked'; ?>
                        class="rounded focus:ring-blue-500"
                    >
                    <span>Afgekeurd</span>
                </label>
            </div>
        </div>

        <!-- Filter buttons -->
        <div class="flex space-x-2">
            <button
                type="submit"
                class="bg-blue-600 text-white rounded-md px-4 py-2 hover:bg-blue-700 transition-colors"
            >
                Filter toepassen
            </button>
            <a
                href="/travel_records"
                class="bg-gray-200 text-gray-700 rounded-md px-4 py-2 hover:bg-gray-300 transition-colors"
            >
                Filter resetten
            </a>
        </div>
    </form>

    <!-- Tabel met reiskosten -->
    <form method="POST" action="/travel_records/submit" class="bg-white shadow-sm rounded-md p-4">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 text-sm text-left">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="px-3 py-2">
                            <input
                                type="checkbox"
                                onclick="toggleAll(this)"
                                class="rounded focus:ring-blue-500"
                            >
                        </th>
                        <th class="px-3 py-2 font-medium text-gray-700">Datum</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Project</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Van</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Naar</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Enkele reis (km)</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Retour</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Totaal (km)</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Status</th>
                        <th class="px-3 py-2 font-medium text-gray-700">Acties</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                <?php if (count($records) > 0): ?>
                    <?php foreach ($records as $record): ?>
                        <tr>
                            <td class="px-3 py-2">
                                <input
                                    type="checkbox"
                                    name="record_ids[]"
                                    value="<?php echo $record['id']; ?>"
                                    <?php if ($record['submitted']) echo 'disabled'; ?>
                                    class="rounded focus:ring-blue-500"
                                >
                            </td>
                            <td class="px-3 py-2"><?php echo date('d-m-Y', strtotime($record['date'])); ?></td>
                            <td class="px-3 py-2"><?php echo View::escape($record['project_number']); ?></td>
                            <td class="px-3 py-2"><?php echo View::escape($record['address_a']); ?></td>
                            <td class="px-3 py-2"><?php echo View::escape($record['address_b']); ?></td>
                            <td class="px-3 py-2"><?php echo number_format($record['single_trip_km'], 1, ',', '.'); ?></td>
                            <td class="px-3 py-2"><?php echo isset($record['return_trip']) && $record['return_trip'] ? 'Ja' : 'Nee'; ?></td>
                            <td class="px-3 py-2"><?php echo number_format($record['total_km'], 1, ',', '.'); ?></td>
                            <td class="px-3 py-2">
                                <?php echo View::escape($record['dynamic_status']); ?>
                            </td>
                            <td class="px-3 py-2 space-x-2">
                                <?php if (!$record['submitted']): ?>
                                    <a
                                        href="/travel_records/edit/<?php echo $record['id']; ?>"
                                        class="text-blue-600 hover:text-blue-800 transition-colors"
                                    >
                                        Bewerken
                                    </a>
                                    <a
                                        href="/travel_records/delete/<?php echo $record['id']; ?>"
                                        class="text-red-600 hover:text-red-800 transition-colors"
                                    >
                                        Verwijderen
                                    </a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="10" class="px-3 py-2 text-center text-gray-500">
                            Geen reiskosten gevonden.
                        </td>
                    </tr>
                <?php endif; ?>
                </tbody>
                <tfoot class="bg-gray-50">
                    <tr>
                        <td colspan="7" class="px-3 py-2 text-right font-medium">Totaal kilometers:</td>
                        <td class="px-3 py-2 font-medium"><?php echo number_format($totalKm, 1, ',', '.'); ?></td>
                        <td colspan="2"></td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- Knoppen onder de tabel -->
        <div class="flex items-center mt-4">
            <!-- Linkerknop: Reiskosten indienen -->
            <button
                type="submit"
                class="bg-blue-600 text-white rounded-md px-4 py-2 hover:bg-blue-700 transition-colors"
            >
                Indienen
            </button>

            <!-- Rechterknop: 'ml-auto' duwt deze helemaal naar rechts -->
            <button
                type="button"
                class="bg-blue-600 text-white rounded-md px-4 py-2 hover:bg-blue-700 transition-colors ml-auto"
                onclick="window.location.href='/travel_records/create';"
            >
                Nieuwe reiskostenregel toevoegen
            </button>
        </div>
    </form>
</div>

<script>
    function toggleAll(source) {
        const checkboxes = document.getElementsByName('record_ids[]');
        for(let i = 0; i < checkboxes.length; i++) {
            if (!checkboxes[i].disabled) {
                checkboxes[i].checked = source.checked;
            }
        }
    }
</script>
