<div class="max-w-7xl mx-auto px-4 py-6">
  <div class="bg-white shadow rounded p-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold">Logviewer</h1>
      
      <div class="flex space-x-2">
        <button 
          id="test-log-btn"
          class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
        >
          Test Log
        </button>
        
        <form method="POST" action="/logs/clear" onsubmit="return confirm('Weet je zeker dat je het logbestand wilt legen?');">
          <button 
            type="submit"
            class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
          >
            Leeg Logbestand
          </button>
        </form>
      </div>
    </div>
    
    <!-- Filters -->
    <div class="mb-6">
      <form method="GET" action="/logs" class="flex flex-wrap gap-4">
        <div class="flex-1 min-w-[200px]">
          <label for="search" class="block mb-1 text-sm font-medium text-gray-700">Zoeken</label>
          <input 
            type="text" 
            id="search" 
            name="search" 
            value="<?php echo View::escape($search); ?>"
            class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Zoek in logs..."
          >
        </div>
        
        <div>
          <label for="level" class="block mb-1 text-sm font-medium text-gray-700">Level</label>
          <select 
            id="level" 
            name="level"
            class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="" <?php if ($level === '') echo 'selected'; ?>>Alle levels</option>
            <option value="error" <?php if ($level === 'error') echo 'selected'; ?>>Error</option>
            <option value="warning" <?php if ($level === 'warning') echo 'selected'; ?>>Warning</option>
            <option value="info" <?php if ($level === 'info') echo 'selected'; ?>>Info</option>
            <option value="debug" <?php if ($level === 'debug') echo 'selected'; ?>>Debug</option>
          </select>
        </div>
        
        <div>
          <label for="limit" class="block mb-1 text-sm font-medium text-gray-700">Aantal</label>
          <select 
            id="limit" 
            name="limit"
            class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="25" <?php if ($limit === 25) echo 'selected'; ?>>25</option>
            <option value="50" <?php if ($limit === 50) echo 'selected'; ?>>50</option>
            <option value="100" <?php if ($limit === 100) echo 'selected'; ?>>100</option>
            <option value="250" <?php if ($limit === 250) echo 'selected'; ?>>250</option>
          </select>
        </div>
        
        <div class="flex items-end">
          <button 
            type="submit"
            class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition-colors"
          >
            Filter
          </button>
        </div>
      </form>
    </div>
    
    <!-- Log tabel -->
    <div class="overflow-x-auto">
      <table class="min-w-full bg-white">
        <thead class="bg-gray-100">
          <tr>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tijd</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bericht</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <?php if (empty($logs)): ?>
            <tr>
              <td colspan="3" class="py-4 px-4 text-center text-gray-500">Geen logs gevonden</td>
            </tr>
          <?php else: ?>
            <?php foreach ($logs as $log): ?>
              <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 text-sm text-gray-500 whitespace-nowrap"><?php echo View::escape($log['timestamp']); ?></td>
                <td class="py-2 px-4">
                  <?php if ($log['level'] === 'error'): ?>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      Error
                    </span>
                  <?php elseif ($log['level'] === 'warning'): ?>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      Warning
                    </span>
                  <?php elseif ($log['level'] === 'info'): ?>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Info
                    </span>
                  <?php elseif ($log['level'] === 'debug'): ?>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      Debug
                    </span>
                  <?php endif; ?>
                </td>
                <td class="py-2 px-4 text-sm text-gray-500 break-all">
                  <div class="max-h-24 overflow-y-auto">
                    <?php echo nl2br(View::escape($log['message'])); ?>
                  </div>
                </td>
              </tr>
            <?php endforeach; ?>
          <?php endif; ?>
        </tbody>
      </table>
    </div>
    
    <!-- Paginering -->
    <?php if ($totalPages > 1): ?>
      <div class="mt-6 flex justify-between items-center">
        <div class="text-sm text-gray-700">
          Pagina <?php echo $page; ?> van <?php echo $totalPages; ?> (<?php echo $total; ?> logs)
        </div>
        
        <div class="flex space-x-2">
          <?php if ($page > 1): ?>
            <a 
              href="/logs?page=<?php echo $page - 1; ?>&limit=<?php echo $limit; ?>&search=<?php echo urlencode($search); ?>&level=<?php echo urlencode($level); ?>"
              class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition-colors"
            >
              Vorige
            </a>
          <?php endif; ?>
          
          <?php if ($page < $totalPages): ?>
            <a 
              href="/logs?page=<?php echo $page + 1; ?>&limit=<?php echo $limit; ?>&search=<?php echo urlencode($search); ?>&level=<?php echo urlencode($level); ?>"
              class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition-colors"
            >
              Volgende
            </a>
          <?php endif; ?>
        </div>
      </div>
    <?php endif; ?>
  </div>
</div>

<!-- Test Log Modal -->
<div id="test-log-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
  <div class="bg-white rounded-lg p-6 w-full max-w-md">
    <h2 class="text-xl font-semibold mb-4">Test Log Bericht</h2>
    
    <form method="POST" action="/logs/test" class="space-y-4">
      <div>
        <label for="test-level" class="block mb-1 text-sm font-medium text-gray-700">Level</label>
        <select 
          id="test-level" 
          name="level"
          class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="error">Error</option>
          <option value="warning">Warning</option>
          <option value="info" selected>Info</option>
          <option value="debug">Debug</option>
        </select>
      </div>
      
      <div>
        <label for="test-message" class="block mb-1 text-sm font-medium text-gray-700">Bericht</label>
        <textarea 
          id="test-message" 
          name="message"
          class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows="3"
        >Dit is een testbericht</textarea>
      </div>
      
      <div class="flex justify-end space-x-2">
        <button 
          type="button"
          id="test-log-cancel"
          class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition-colors"
        >
          Annuleren
        </button>
        
        <button 
          type="submit"
          class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
        >
          Versturen
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  // Test Log Modal
  const testLogBtn = document.getElementById('test-log-btn');
  const testLogModal = document.getElementById('test-log-modal');
  const testLogCancel = document.getElementById('test-log-cancel');
  
  testLogBtn.addEventListener('click', () => {
    testLogModal.classList.remove('hidden');
  });
  
  testLogCancel.addEventListener('click', () => {
    testLogModal.classList.add('hidden');
  });
  
  // Sluit modal als er buiten wordt geklikt
  testLogModal.addEventListener('click', (e) => {
    if (e.target === testLogModal) {
      testLogModal.classList.add('hidden');
    }
  });
</script>
